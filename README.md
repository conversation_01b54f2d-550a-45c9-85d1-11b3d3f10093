## 依赖安装

-   使用 `yarn` 命令进行安装
-   使用 `yarn upgrade xxx` 命令进行某个依赖包更新

## 项目工具可视化页面

-   http://localhost:5173/#/common-tools 【端口需要根据实际项目中运行的端口保持一致】

## unocss 查询

-   文档: https://unocss.dev/
-   查询: https://unocss.dev/interactive/

## loading 资源

-   [loading 资源网站](https://loading.io/spinner/)

## mescroll.js

-   [mescroll.js 文档](http://www.mescroll.com/api.html)

```js
/**
 * 白名单，用于排除不需要登录的页面(示例)
 */
whiteList: [
    'login',
    (to) => {
        return to.path === '/large-screen/home';
    },
],
```

## 代码仓库版本和分支管理策略

### 一、版本号命名规范

采用语义化版本控制（Semantic Versioning）：

1. 格式：vMAJOR.MINOR.PATCH
2. 规则：
    - MAJOR：向后不兼容的变更时递增。
    - MINOR：添加功能或修改现有功能但保持兼容时递增。
    - PATCH：修复 bug 时递增。

### 二、分支管理策略（采用 Git Flow）

#### 主分支

-   main：稳定版本，所有上线后的分支全部合并到该分支，开发分支也要从该分支拉取。

#### 发布分支

-   uat：准备发布时创建， uat 环境部署分支，同时用作上线分支。

#### 测试分支：

-   sit：sit 环境部署分支，仅用作测试，不可合并到 uat 分支。
-   注意 ⚠️：不要让 sit 分支污染开发分支，即遇到冲突时，应先根据开发分支新建一个临时分支，用作本次冲突解决，合到到 sit 分支的也是这个临时分支。

#### 开发分支

-   feature/xxxx：开发新功能时创建，命名如 feature/authentication-nickname。
-   fix/xxxx : 修复 bug 的分支，命名如 fix/add-user-nickname
-   refact/xxxx：重构代码的分支，命名如 refact/add-user-nickname
-   用法：从 main 分支创建，部署到 sit 环境时是需要合并到 sit 分支，部署到 uat 环境时需要合到 uat 分支。

#### 紧急分支：

-   urgent/xxxx ：当遇到紧急上线时，才会新建，如：生产 bug 紧急修复，此分支优先级最高， sit 环境可直接合入 sit 分支，uat 环境会直接用该分支部署，上线时也会用该分支上线。
