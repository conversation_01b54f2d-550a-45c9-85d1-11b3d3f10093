#!/bin/bash

# 用法: ./move_tag_to_latest.sh
# 作用：在当前分支将指定tag移动到最新commit，并强制推送到远程（仅当远端已有该tag时）

read -p "请输入要移动的tag名称: " TAG_NAME

if [ -z "$TAG_NAME" ]; then
  echo "未输入tag名称，退出。"
  exit 1
fi

# 检查远端是否有该tag
REMOTE_TAG_EXISTS=$(git ls-remote --tags origin | grep -w "refs/tags/$TAG_NAME" | wc -l)

if [ "$REMOTE_TAG_EXISTS" -eq 0 ]; then
  echo "远端不存在tag: $TAG_NAME，无需处理。"
  exit 0
fi

# 强制移动tag到当前分支最新commit
git tag -f "$TAG_NAME"

# 强制推送tag到远程
git push origin "$TAG_NAME" -f

# 删除本地旧 tag 并拉取最新的远程 tag
git tag -d "$TAG_NAME"
git fetch origin tag "$TAG_NAME"

# 提示用户本地同步
echo "Tag $TAG_NAME 已成功移动到当前分支最新 commit 并推送到远程。"