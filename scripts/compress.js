const { execSync } = require('child_process');
const fs = require('fs');
const AdmZip = require('adm-zip');
const dayjs = require('dayjs'); // 引入 dayjs

const TYPE = process.argv[2];
/**
 * 获取当前的git hash，自动将dist_management文件夹压缩为zip格式，
 * 重命名为dist_management_${git_hash}_${日期}.zip
 */
async function compressDistFolder() {
    try {
        // 删除之前的压缩文件
        fs.readdirSync('.').forEach((file) => {
            if (file.startsWith('dist_management') && file.endsWith('.zip')) {
                fs.unlinkSync(file);
                console.log(`🔨 历史打包文件已删除`);
            }
        });
        // 获取当前的git hash
        const gitHash = execSync('git rev-parse --short HEAD').toString().trim();
        // 获取package.json中的版本号
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const version = packageJson.version;

        // 使用 dayjs 获取当前日期和时间，精确到时分秒
        const dateTime = dayjs().format('YYYYMMDDHHmm');

        const outputFileName =
            TYPE === 'custom' ? `dist_management_v${version}_${gitHash}_${dateTime}.zip` : 'dist_management.zip';
        const outputFilePath = `./${outputFileName}`;
        const sourceFolder = 'dist_management';

        // 检查dist_management文件夹是否存在
        if (!fs.existsSync(sourceFolder)) {
            console.error(`错误：文件夹 '${sourceFolder}' 不存在。`);
            return;
        }

        // 创建 AdmZip 实例
        console.log(`🔨 正在打包本地目录: ${sourceFolder}`);
        const zip = new AdmZip();

        // 将整个文件夹添加到 zip 中
        // 第一个参数是源文件夹路径，第二个参数是 zip 文件中的目标路径（空字符串表示根目录）
        zip.addLocalFolder(sourceFolder, '');

        // 将 zip 写入文件
        zip.writeZip(outputFilePath);

        console.log(`成功创建 ${outputFileName}`);

        // console.log('压缩完成，正在删除原始文件夹...');
        // fs.rmSync(sourceFolder, { recursive: true, force: true });
        // console.log(`原始文件夹 '${sourceFolder}' 已删除。`);
    } catch (error) {
        console.error('压缩过程中发生错误：', error);
    }
}

compressDistFolder();
