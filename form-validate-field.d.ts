export {};
import {
    FormRules,
    FormItemProps,
    InputProps,
    CascaderProps,
    SelectProps,
    DatePickerProps,
    SwitchProps,
    InputNumberProps,
    GridItemProps,
    TransferTreeProps,
    TreeSelectProps,
    UploadProps,
    ProCheckboxProps
} from 'naive-ui';
import { MinioUploadProps } from '@/typings/minio-upload';

type FormValidateFieldItemComponent = {
    input: InputProps;
    select: SelectProps;
    cascader: CascaderProps;
    datePicker: DatePickerProps;
    switch: SwitchProps;
    number: InputNumberProps;
    transferTree: TransferTreeProps;
    treeSelect: TreeSelectProps;
    minioUpload: MinioUploadProps;
    upload: UploadProps;
    proCheckbox: ProCheckboxProps;
};
import { Component, VNode, ExtractPropTypes } from 'vue';
declare global {
    type FormValidateField = FormValidateFieldItem[];
    type FormValidateFieldItem<C = keyof FormValidateFieldItemComponent | Component | VNode> = {
        label?: string;
        component: C;
        field: string;
        rules?: FormRules[string];
        config?: FormItemProps;
        gridItemProps?: GridItemProps;
        props?: C extends keyof FormValidateFieldItemComponent
            ? FormValidateFieldItemComponent[C]
            : C extends VNode | Component
            ? ExtractPropTypes<C>
            : never;
        slots?: {
            formFeedback?: Component | VNode;
            formLabel?: Component | VNode;
            gridBefore?: Component | VNode;
            gridAefter?: Component | VNode;
            [key: string]: Component | VNode;
        };
        fieldModel?: string;
        notBorder?: boolean;
    };
}
