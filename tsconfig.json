{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "lib": ["esnext", "dom"], "baseUrl": "./", "paths": {"@/*": ["./src/*"]}, "skipLibCheck": true, "types": ["node", "vite/client", "vitejs-get-vue-ref"], "allowSyntheticDefaultImports": true, "noEmit": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "auto-api.ts", "form-validate-field.d.ts"]}