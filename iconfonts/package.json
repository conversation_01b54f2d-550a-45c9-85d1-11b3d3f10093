{"name": "iconfonts", "version": "1.0.0", "description": "", "main": "index.ts", "type": "module", "scripts": {"clone:iconfonts": "rimraf ../src/icons/svgs/* && rimraf ../src/icons/iconfont/* && ts-node-esm index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.3.4", "compressing": "^1.8.0", "fs-extra": "^11.1.0", "naive-ui": "git+https://gitlab.zhijiasoft.com/vue3-base-framework/wisdom-naive-release.git", "puppeteer": "^19.7.2", "rimraf": "^4.1.3", "ts-node": "^10.9.1", "typescript": "^4.9.5"}, "devDependencies": {"@types/fs-extra": "^11.0.1"}}