# 单位人员发放/回收组件需求文档

## 1. 组件名称

grant-recycle（单位人员发放/回收）

---

## 2. 组件定位与功能

-   **定位**：用于在单位组织架构树中，批量发放或回收人员权限/资格。
-   **主要功能**：
    -   左侧组织树展示所有单位及人员，支持多选、搜索。
    -   右侧展示已选人员列表，支持多选、搜索。
    -   中间提供“发放”“回收”操作按钮，实现批量发放/回收。
    -   支持人员状态标识（已发放、审批中）。
    -   禁用已发放/审批中的人员，防止重复操作。

---

## 3. 组件结构

-   **左侧：单位人员树**
    -   组织树结构，节点为单位/部门/人员。
    -   支持全选、单选、搜索（姓名/部门）。
    -   已发放/审批中人员节点禁用，并有状态标识。
-   **中间：操作按钮**
    -   “发放”按钮：将左侧选中人员加入右侧已选列表。
    -   “回收”按钮：将右侧选中人员从已选列表移除。
-   **右侧：已选人员列表**
    -   列表展示所有已选人员，支持多选、全选、搜索（姓名）。

---

## 4. 交互说明

-   **左侧树**
    -   支持多选，已发放/审批中人员禁用。
    -   搜索时自动展开所有节点。
    -   全选/反选功能。
-   **右侧列表**
    -   支持多选、全选、反选。
    -   搜索过滤已选人员。
-   **操作按钮**
    -   “发放”按钮仅在左侧有选中人员时可用。
    -   “回收”按钮仅在右侧有选中人员时可用。
-   **状态标识**
    -   “审批中”人员：橙色字体标识。
    -   “已发放”人员：蓝色字体标识。

---

## 5. Props 说明

| Prop 名称       | 类型                  | 说明                  | 是否必填 | 备注             |
| --------------- | --------------------- | --------------------- | -------- | ---------------- |
| treeData        | TreeOption[]          | 组织架构树（含人员）  | 是       | 需包含 key/label |
| selectedIds     | string[]              | 已选人员 id 数组      | 否       | 默认空数组       |
| approvingIds    | string[]              | 审批中人员 id 数组    | 否       | 默认空数组       |
| selectedList    | Record<string, any>[] | 已选人员详细信息      | 是       | 需含 key/label   |
| leftKeyField    | string                | 左侧树 key 字段名     | 否       | 默认 'key'       |
| leftLabelField  | string                | 左侧树 label 字段名   | 否       | 默认 'label'     |
| rightKeyField   | string                | 右侧列表 key 字段名   | 否       | 默认 'key'       |
| rightLabelField | string                | 右侧列表 label 字段名 | 否       | 默认 'label'     |

---

## 6. Emits 事件

| 事件名  | 参数类型 | 说明             |
| ------- | -------- | ---------------- |
| grant   | string[] | 发放人员 id 数组 |
| recycle | string[] | 回收人员 id 数组 |

---

## 7. 主要数据结构

### 组织树 TreeOption

```ts
interface TreeOption {
    key: string; // 节点唯一标识
    label: string; // 显示名称
    children?: TreeOption[]; // 子节点
    // 其他自定义字段
}
```

### 已选人员 selectedList

```ts
interface SelectedPerson {
    key: string; // 人员唯一标识
    label: string; // 人员姓名
    // 其他自定义字段
}
```

---

## 8. UI 规范

-   **组件库**：Naive UI
-   **布局**：flex 布局，左右两侧 panel 固定宽度，中间按钮竖直居中
-   **样式**：使用 Less，scoped，风格与项目统一
-   **按钮类型**：
    -   发放：`type="primary"`
    -   回收：`type="warning"`
-   **禁用态**：已发放/审批中人员禁用选择
-   **状态标识**：
    -   审批中：橙色 `#f0a020`
    -   已发放：蓝色 `#005EFF`

---

## 9. 关键方法与逻辑

-   **getAllKeysLeft**：递归获取所有人员节点 key
-   **markTreeDisabled**：递归设置树节点禁用状态
-   **renderLeftLabel**：自定义左侧树节点 label，显示状态标识
-   **toggleLeftCheckAll/toggleRightCheckAll**：全选/反选逻辑
-   **handleGrant/handleRecycle**：发放/回收操作，触发事件并清空选中

---

## 10. 其他说明

-   **类型定义**：全部使用 TypeScript，props、emit、内部变量均需类型声明
-   **注释**：关键业务、mock 结构、交互逻辑需有中文注释
-   **可扩展性**：支持自定义 key/label 字段名，便于后续扩展
-   **无直接 DOM 操作**，全部通过 Vue 方式实现

---

## 11. 示例用法

```vue
<grant-recycle
    :tree-data="orgTree"
    :selected-ids="selectedIds"
    :approving-ids="approvingIds"
    :selected-list="selectedList"
    @grant="handleGrant"
    @recycle="handleRecycle"
/>
```

---

## 12. 需求变更与维护

-   组件如需扩展（如支持更多状态、批量导入等），应保持 props、事件兼容性，优先向下兼容。
-   相关文档、类型定义需同步更新。

---

> 本文档为《单位人员发放/回收》组件需求说明，适用于 Vue3 + Naive UI 项目，后续如有变更请及时同步更新。
