## 主要修改内容

1. 添加了 defaultFileList 属性，允许设置默认文件列表
2. 添加了 fileList 响应式变量，用于管理文件列表状态
3. 添加了 update:fileList 事件，支持 v-model 绑定文件列表
4. 在 beforeUpload 方法中更新文件列表
5. 在 customUploadMethod 中添加了 updateFileInList 辅助函数，用于更新文件列表中的状态
6. 在上传过程中实时更新文件的状态和进度
7. 在取消上传时更新文件列表状态

```vue
<template>
    <div class="minio-upload bg-#fff">
        <n-upload
            v-bind="props"
            :custom-request="customUploadMethod"
            @before-upload="beforeUpload"
            :default-file-list="fileList"
        >
            <n-upload-dragger v-if="$slots.drag">
                <slot name="drag" />
            </n-upload-dragger>
            <template v-else>
                <slot />
            </template>
        </n-upload>

        <div v-if="uploading && loading" class="upload-loading-mask">
            <div class="progress-list">
                <span class="title">文件上传中</span>
                <div class="progress-item" v-for="(info, uid) in uploadProgressMap" :key="uid">
                    <div class="filename">{{ info.name }}</div>
                    <n-progress
                        type="line"
                        :percentage="Math.floor(info.progress)"
                        :status="info.status"
                        :height="8"
                        :show-indicator="true"
                    />
                </div>
                <n-button type="error" size="small" @click="abortAllUploads" class="mt-16px"> 取消上传 </n-button>
            </div>
        </div>
    </div>
</template>
```

```vue
<template>
    <minio-upload v-model:file-list="fileList" :default-file-list="defaultFiles" @success="handleSuccess">
        <n-button>上传文件</n-button>
    </minio-upload>
</template>

<script setup>
import { ref } from 'vue';
import { UploadFileInfo } from 'naive-ui';

const fileList = ref([]);
const defaultFiles = ref([
    {
        id: 'a',
        name: '示例文件.png',
        status: 'finished',
        url: 'https://example.com/file.png'
    }
]);

const handleSuccess = (id) => {
    console.log('上传成功，文件ID:', id);
};
</script>
```
