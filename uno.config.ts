import { defineConfig, presetWind } from 'unocss';
import { default as less } from 'less';
const tint = (color: string, amount: number) =>
    less.functions?.functionRegistry
        .get('tint')(new less.color(color.replace(/#/, '')), new less.dimension(amount, '%'))
        .toRGB();
export default defineConfig({
    presets: [presetWind()],
    theme: {
        breakpoints: {
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px',
            '3xl': '1920px',
            '4xl': '2560px'
        }
    },
    safelist: [
        'bg-lg-#005EFF',
        'bg-lg-#388e3c',
        'bg-lg-#FC2D2D',
        'bg-lg-#FF9800',
        'bg-lg-#9E9E9E',
        'c-#005EFF',
        'c-#388e3c',
        'c-#FC2D2D',
        'c-#FF9800',
        'c-#9E9E9E'
    ],
    shortcuts: {
        'flex-center': 'flex justify-center items-center',
        'flex-center-start': 'flex justify-start items-center',
        'flex-center-end': 'flex justify-end items-center',
        'flex-center-between': 'flex justify-between items-center',
        'flex-center-around': 'flex justify-around items-center',
        'flex-v': 'flex flex-col',
        'abs-f': 'fixed',
        'abs-r': 'relative',
        abs: 'absolute',
        'size-content': 'left-0 top-0 w-100% h-100%',
        'abs-content': 'absolute left-0 top-0 w-100% h-100%',
        'abs-start': 'absolute left-0 top-0',
        'abs-end': 'absolute right-0 top-0',
        'abs-end-bottom': 'absolute right-0 bottom-0',
        'abs-start-bottom': 'absolute left-0 bottom-0',
        'abs-center': 'absolute left-50% top-50% translate--50%',
        'abs-x': 'absolute left-50% translate-x--50%',
        'abs-y': 'absolute top-50% translate-y--50%',
        bold: 'font-bold'
    },
    rules: [
        [
            // c-var--primary-color => color: var(--primary-color)
            /^c-var-([a-zA-Z0-9-]+)$/,
            (match) => {
                return {
                    color: `var(--${match[1]})`
                };
            }
        ],
        [
            // 包含小数点的 flex
            /^flex-?([0-9]+(?:\.[0-9]+)?)$/,
            (match) => {
                return {
                    flex: match[1]
                };
            }
        ],
        [
            // bg-[linear-gradient(79deg,#FFFFFF_15%,#f7faff_99%)] => background: linear-gradient(79deg, #ffffff 15%, #f7faff 99%)
            /^bg-\[(.+)\]$/,
            ([, value]) => {
                if (value.startsWith('linear-gradient')) {
                    return { background: value.replace(/_/g, ' ') };
                }
                return { 'background-color': value };
            }
        ],
        [
            // bg-[linear-gradient(79deg,#FFFFFF_15%,#f7faff_99%)] => background: linear-gradient(79deg, #ffffff 15%, #f7faff 99%)
            /^bg-lg-((#*\w+)|(#*\w+)-(\d+)-(.+))$/,
            ([, , value, value2, amount, direction]) => {
                return {
                    background: `linear-gradient(${direction || 'to right'}, ${value || value2},${tint(
                        value || value2,
                        Number(amount) || 50
                    )})`
                };
            }
        ],
        [
            // border-[1px,solid,#FFFFFF] => border: 1px solid #ffffff
            /^border-\[(.+)\]$/,
            ([, value]) => {
                const [width, style, color] = value.split(',');
                return { border: `${width} ${style} ${color}` };
            }
        ],
        [
            // w-calc-100%-220px => width: calc(100% - 220px)
            /^w-calc-(.+)$/,
            ([, value]) => {
                // 自动在 - 两边加空格
                const safeValue = value.replace(/([0-9a-zA-Z%])-(?=[0-9])/g, '$1 - ');
                return { width: `calc(${safeValue})` };
            }
        ]
    ]
});
