import useStore from '@/store/modules/main';
import router from '../router';
import type { ConfigHooks } from './typings';
import baseConfig from './base';
import config from './config';
export const views = import.meta.glob('../views/**/**');

import { createDiscreteApi } from 'naive-ui';
import { MenuListData } from '@/api/sass/api/v1/menu';
import { RouteRecordRaw, RouterView } from 'vue-router';
import { createAsyncComponent } from '@/utils/route';
import { commonToast } from '@/utils/common';
import useSideRoutesStore from '@/store/modules/side-routes';
const { message } = createDiscreteApi(['message']);

const transToRoutes = (menusMap: MenuListData[]): RouteRecordRaw[] => {
    return (
        menusMap?.map((menu) => {
            const redirect =
                menu.redirect || (menu.children?.[0] && !menu.component ? { name: menu.children[0].name } : undefined);
            return {
                name: menu.name,
                path: menu.url,
                component: redirect ? RouterView : createAsyncComponent(menu.name, views[`../views${menu.component}`]),
                redirect,
                meta: {
                    icon: menu.icon ?? null,
                    title: menu.title || menu.name,
                    hidden: menu.hidden,
                    hiddenInTag: menu.hiddenInTab,
                    fixed: menu.fixed,
                    permissions: menu.permissions,
                    isFullPage: menu.isFullPage
                },
                children: transToRoutes(menu.children)
            };
        }) || []
    );
};

export default {
    /**
     * 请求相关
     */
    request: {
        beforeEach(config) {
            const store = useStore();
            if (!config) return;
            if (!config.headers) config.headers = {};

            const sm4Urls: Array<RegExp> = [/^\/security\/encrypt\/status$/];
            if (store.apiEncryptStatus && !sm4Urls.some((e) => e.test(config.url as string))) {
                config.encryptKey = 'p_data';
                config.encryptDataKeyAll = true;
                config.notIrs = '/security/cipher/transfer';
                config.fieldSync = true;
            }
        },
        afterEach(config) {
            if (!config) return;
        },
        errorHandle(msg, _, error) {
            const store = useStore();
            if (baseConfig.isH5) commonToast(msg);
            else if ((error as any)?.response?.status === 401) {
                message.error('身份认证过期，请重新登录！');
                store.setToken();
                $alert.dialog.closeAll();
                router.replace({ name: 'login' });
            } else message.error(msg);
        },
        logout() {
            router.push({ name: 'login' });
        }
    },
    /**
     * 布局相关
     */
    layout: {
        menuSelect(route) {
            if (!route) return;
            if (route?.meta?.url) {
                const meta = route.meta;
                if (meta?.target) {
                    window.open(meta.url, meta.target);
                } else {
                    location.href = meta.url || '#';
                }
            } else {
                router.push({ name: route.name });
            }
        },
        filterNav(route: RouteRecordRaw) {
            return !route.meta?.hidden && route.path !== '/' && route.name !== 'index';
        }
    },
    /**
     * 全局路由相关
     */
    router: {
        firstTimeEnter() {
            const storage = config.router.session ? sessionStorage : localStorage;
            const userInfo = storage.getItem(baseConfig.unique + 'userInfo');
            const token = storage.getItem(baseConfig.unique + 'token');
            const apiEncryptStatus = storage.getItem(baseConfig.unique + 'apiEncryptStatus');
            if (userInfo) {
                const store = useStore();
                store.userInfo = JSON.parse(userInfo);
                store.token = token ?? '';
                store.apiEncryptStatus = apiEncryptStatus ? JSON.parse(apiEncryptStatus) : false;
            }
        },
        beforeEach(to, from) {
            // 每个路由进入前发起一个请求
            const store = useSideRoutesStore();
            store.setHistoryRoutes(to, from);
        },
        /**
         * 用于 登录 / 第一次进入页面时获取权限
         * 可验证token是否有效及续期
         */
        async getUserinfo() {
            const store = useStore();
            if (!store.token) throw new Error('Token 不存在');
        },
        /**
         * 过滤路由，流程在 getUserinfo 之后
         */
        async routesFilter(routes, filter) {
            const rootRouteConfig: RouteRecordRaw = {
                name: 'index',
                path: '/',
                component: () => import('@/components/layout/index.vue')
            };
            const store = useStore();
            if (!filter || !config.router.needLogin || !config.router.remote) {
                rootRouteConfig.redirect = {
                    name: routes?.[0]?.name as string
                } as any;
                return ([rootRouteConfig] as RouteRecordRaw[]).concat(routes);
            }
            const data = (await window.api.sass.api.v1.menu.get_menus_by_user(store.userInfo.id ?? '')).data.data;
            const indexName = (data.length > 0 && data?.[0].name) || 'login';
            if (indexName === 'login') {
                message.error('您没有任何页面的访问权限！');
                return [];
            }
            rootRouteConfig.redirect = { name: indexName } as any;
            return ([rootRouteConfig] as RouteRecordRaw[]).concat(transToRoutes(data));
        }
    },
    /**
     * 错误处理
     */
    error: {
        handle(error) {
            console.log(error);
        }
    }
} as ConfigHooks;
