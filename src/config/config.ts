import type { Config } from './typings';

export default {
    request: {
        baseURL: import.meta.env.VITE_API,
        successCode: [0, 200],
        useStore: () => window.store.main,
        trim: true,
        timeout: 20000,
        sm4Key: '9r17u127a9z64h4p',
        onlySm4: true
    },
    router: {
        history: false,
        /** 是否使用远程路由 */
        remote: import.meta.env.PROD,
        /**
         * 系统是否需要登录
         */
        needLogin: true,
        /**
         * 是否使用 SessionStorage 记录登录状态
         * 如果为 false 则使用 LocalStorage
         */
        session: false,
        /**
         * 白名单，用于排除不需要登录的页面
         */
        whiteList: ['login'],
        /**
         * 是否保持 alive 状态
         */
        keepAlive: false,
        /**
         * 菜单是否需要 icon 图标以及页面响应式
         */
        needSideMenuIcon: true,
        /**
         * 菜单顶部是否显示收缩按钮
         */
        showContractIcon: true
    },
    /**
     * 页签配置
     */
    tabbarViews: {
        disabled: false
    },
    /**
     * 是否显示页脚
     */
    showFooter: true,
    /**
     * H5的配置项
     */
    h5Config: {
        showTopNav: false,
        showBottomNav: false,
        openPullRefresh: false
    }
} as Config;
