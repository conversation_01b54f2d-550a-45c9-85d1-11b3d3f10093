import { AppConfigState, DeviceType, LayoutMode, PageAnim, SideTheme, ThemeMode, LocaleName } from '@/typings';

const primaryColor = '#005EFF';
const primaryHoverColor = '#3a79fe';

document.documentElement.style.setProperty('--van-primary-color', primaryColor);

export default {
    theme: ThemeMode.LIGHT,
    sideTheme: SideTheme.WHITE,
    themeColor: primaryColor,
    themeColorHover: primaryHoverColor,
    layoutMode: LayoutMode.TTB,
    deviceType: DeviceType.PC,
    pageAnim: PageAnim.OPACITY,
    isCollapse: false,
    actionBar: {
        isShowNotice: true,
        isShowRefresh: true,
        isShowFullScreen: true
    },
    localeName: LocaleName.zhCN
} as AppConfigState;
