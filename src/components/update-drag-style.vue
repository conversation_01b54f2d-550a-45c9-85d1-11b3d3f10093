<template>
    <n-upload-dragger class="updateDragStyle">
        <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
                <archive-icon />
            </n-icon>
        </div>
        <n-text style="font-size: 16px">
            {{ title }}
        </n-text>
        <n-p depth="3" style="margin: 8px 0 0 0">
            {{ msg }}
        </n-p>
    </n-upload-dragger>
</template>
<script setup lang="ts">
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
withDefaults(
    defineProps<{
        title?: string;
        msg?: string;
    }>(),
    {
        title: '点击或者拖动文件到该区域来上传',
        msg: '支持扩展名：.rar .zip .doc .docx .pdf .jpg ...'
    }
);
</script>
<style scoped lang="less">
.updateDragStyle {
}
</style>
