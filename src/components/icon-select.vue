<template>
    <div class="icon-select">
        <div class="con" @click="unCheck" :class="{ active: selected === '' }">
            <n-icon size="18">
                <BanOutline />
            </n-icon>
        </div>
        <div
            v-for="(item, key) in iconList"
            :key="key"
            class="con"
            :class="{ active: selected === item }"
            @click="init(item)"
        >
            <SvgIcon :name="item" :size="20" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { BanOutline } from '@vicons/ionicons5';

const icons = import.meta.glob('@/icons/menus/*');

const props = defineProps<{
    value?: string | undefined;
}>();
const emit = defineEmits(['update:value']);

const iconList = ref<string[]>([]);
const selected = ref<string>('');
for (const [key] of Object.entries(icons).filter((v) => v[0].indexOf('fill') < 0)) {
    let moduleName = key.replace('/src/icons/', '').replace(/\.svg/g, '').split('/').join('-');
    iconList.value.push(moduleName);
}

const init = (name?: string) => {
    selected.value = name ? name : props.value ? props.value : '';
    emit('update:value', selected.value);
};

const unCheck = () => {
    selected.value = '';
    emit('update:value', selected.value);
};

onMounted(() => {
    nextTick(() => {
        init();
    });
});
</script>

<style scoped lang="less">
.icon-select {
    width: 100%;
    height: 100px;
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    .con {
        width: 30px;
        height: 30px;
        margin: 5px 5px 0 5px;
        padding: 5px;
        border: 1px solid #eeeeee;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        &.active {
            border: 1px solid var(--van-primary-color);
        }
        svg {
            width: 100%;
            height: 100%;
            color: #333333;
        }
    }
}
</style>
