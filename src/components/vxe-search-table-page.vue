<template>
    <div class="vxe-search-table-page w-full h-full">
        <n-search-table-page
            ref="searchTablePageRef"
            v-bind="$attrs"
            :data-table-props="{
                size: 'small',
                ...($attrs['data-table-props'] || {})
            }"
            :search-table-space="{ size: 20, ...($attrs['search-table-space'] || {}) }"
        >
            <!-- 搜索区域整体布局 -->
            <template #search_layout v-if="$slots.search_layout">
                <slot name="search_layout"></slot>
            </template>

            <!-- 搜索区域底部布局 -->
            <template #search_bottom_layout v-if="$slots.search_bottom_layout">
                <slot name="search_bottom_layout"></slot>
            </template>

            <!-- 搜索区域左侧内容 -->
            <template #search_form v-if="$slots.search_form">
                <slot name="search_form"></slot>
            </template>

            <!-- 搜索区域左侧输入框左边内容 -->
            <template #search_form_pre v-if="$slots.search_form_pre">
                <slot name="search_form_pre"></slot>
            </template>

            <!-- 搜索区域左侧输入框右边内容 -->
            <template #search_form_middle v-if="$slots.search_form_middle">
                <slot name="search_form_middle"></slot>
            </template>

            <!-- 搜索区域左侧最右边内容 -->
            <template #search_form_after v-if="$slots.search_form_after">
                <slot name="search_form_after"></slot>
            </template>

            <!-- 搜索区域右侧内容 -->
            <template #search_handle v-if="$slots.search_handle">
                <slot name="search_handle"></slot>
            </template>

            <!-- 搜索区域右侧最左边内容 -->
            <template #search_handle_pre v-if="$slots.search_handle_pre">
                <slot name="search_handle_pre"></slot>
            </template>

            <!-- 搜索区域右侧最右边内容 -->
            <template #search_handle_after v-if="$slots.search_handle_after">
                <slot name="search_handle_after"></slot>
            </template>

            <template #table_layout>
                <div class="flex-v">
                    <vxe-table ref="tableRef" v-bind="mergedTableProps" :data="tableData" :loading="loading">
                        <template v-for="col in columns" :key="col.title">
                            <vxe-column v-if="col?.type === 'seq'" v-bind="col" />
                            <vxe-column v-else v-bind="col">
                                <template #default="scope">
                                    <slot :name="`table_${col.field}`" :row="scope.row">
                                        {{ scope.row[col.field] }}
                                    </slot>
                                </template>
                            </vxe-column>
                        </template>
                    </vxe-table>
                    <div v-if="props.pagination" class="flex justify-end pt-10px">
                        <n-pagination
                            v-model:page="page"
                            v-model:page-size="pageSize"
                            :item-count="itemCount"
                            :default-page-size="pageSize"
                            :page-sizes="[5, 10, 15, 20, 50, 100]"
                            :show-quick-jumper="props.pagination"
                            :show-size-picker="props.pagination"
                            :page-slot="6"
                            @update:page="handlePageChange"
                            @update:page-size="handlePageSizeChange"
                        >
                            <template #prefix="{ itemCount }"> 共 {{ itemCount }} 项 </template>
                        </n-pagination>
                    </div>
                    <div v-if="!props.pagination && props.showItemCount" class="flex justify-end pt-10px">
                        <n-text class="mr-4">共 {{ itemCount }} 项</n-text>
                    </div>
                </div>
            </template>
        </n-search-table-page>
    </div>
</template>

<script lang="ts" setup>
import type { VxeTableEventProps, VxeTableProps } from 'vxe-table';
import { VxeTable, VxeColumn } from 'vxe-table';

// 默认配置
const defaultTableProps: VxeTableProps = {
    showOverflow: true,
    rowConfig: {
        keyField: 'id',
        isHover: true,
        drag: false
    },
    rowDragConfig: {
        isPeerDrag: false
    },
    cellConfig: {
        height: 40
    },
    headerCellConfig: {
        height: 40
    },
    treeConfig: {
        reserve: true,
        transform: true,
        hasChild: 'hasChild'
    }
};

const props = withDefaults(
    defineProps<{
        vxeTableProps?: VxeTableProps & VxeTableEventProps;
        columns: Record<string, any>[];
        params?: Record<string, any>;
        dataApi?: (params: any) => Promise<any>;
        data?: any[];
        dataField?: string;
        totalField?: string;
        pagination?: boolean;
        showItemCount?: boolean;
    }>(),
    {
        vxeTableProps: () => ({}),
        columns: () => [],
        params: () => ({}),
        dataApi: undefined,
        data: undefined,
        dataField: 'data',
        totalField: 'total',
        pagination: true,
        showItemCount: false
    }
);

// 深度合并函数
const deepMerge = (target: any, source: any): any => {
    const result = { ...target };

    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = deepMerge(result[key] || {}, source[key]);
        } else {
            result[key] = source[key];
        }
    }

    return result;
};

// 合并默认配置和用户配置
const mergedTableProps = computed(() => {
    return deepMerge(defaultTableProps, props.vxeTableProps || {});
});

const searchTablePageRef = ref();
const loading = ref(false);
const tableData = ref<any[]>([]);

// 分页相关状态
const page = ref(1);
const pageSize = ref(10);
const itemData = ref([]);
const itemCount = ref(0);

// 加载数据
const initData = async () => {
    // 如果直接传入了数据，则直接使用
    if (props.data !== undefined) {
        tableData.value = props.data;
        return;
    }

    // 否则通过 API 获取数据
    if (!props.dataApi) return;

    loading.value = true;
    try {
        const params = props.pagination
            ? {
                  page: page.value,
                  pageSize: pageSize.value,
                  ...props.params
              }
            : props.params;
        const res = await props.dataApi(params);

        // 处理返回的数据
        const rawData = res.data?.[props.dataField];
        itemData.value = rawData == null ? [] : rawData;
        itemCount.value = res.data?.[props.totalField] ?? (Array.isArray(itemData.value) ? itemData.value.length : 0);

        if (!props.pagination) {
            pageSize.value = itemCount.value;
        }

        // 如果是树形数据，进行处理
        if (props.vxeTableProps?.treeConfig) {
            tableData.value = processNodeData(itemData.value);
        } else {
            tableData.value = itemData.value;
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        window.$message.error('加载数据失败');
    } finally {
        loading.value = false;
    }
};

// 刷新指定节点的子级数据
const reloadTreeExpand = async (row: any) => {
    if (!props.vxeTableProps?.treeConfig?.loadMethod) return;

    try {
        const children = await props.vxeTableProps.treeConfig.loadMethod({
            $table: tableRef.value,
            row
        });
        const processedChildren = processNodeData(children);

        // 更新表格中该节点的子级数据
        if (tableRef.value) {
            tableRef.value.reloadTreeExpand(row, processedChildren);
        }
    } catch (error) {
        console.error('重新加载子级数据失败:', error);
    }
};

// 数据处理
const processNodeData = (data: any[]): any[] => {
    return data?.map((element) => ({
        ...element,
        hasChild: true,
        children: Array.isArray(element.children) ? processNodeData(element.children) : undefined
    }));
};

// 监听 data 变化
watch(
    () => props.data,
    (newData) => {
        if (newData !== undefined) {
            // 如果是树形数据，进行处理
            if (props.vxeTableProps?.treeConfig) {
                tableData.value = processNodeData(newData);
            } else {
                tableData.value = newData;
            }
        }
    },
    { immediate: true }
);

// 处理页码变化
const handlePageChange = (newPage: number) => {
    page.value = newPage;
    initData();
};

// 处理每页条数变化
const handlePageSizeChange = (newPageSize: number) => {
    pageSize.value = newPageSize;
    page.value = 1; // 重置到第一页
    initData();
};

const tableRef = ref();

// 组件挂载时加载数据
onMounted(() => {
    if (props.data === undefined) {
        initData();
    }
});

// 暴露方法给父组件
defineExpose({
    initData,
    tableRef,
    searchTablePageRef,
    reloadTreeExpand
});
</script>

<style scoped lang="less"></style>
