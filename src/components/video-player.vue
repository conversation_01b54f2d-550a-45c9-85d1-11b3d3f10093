<template>
    <div class="video-player">
        <video ref="videoRef" :src="props.src" :style="{ filter: `brightness(${brightness}%)` }" controls></video>
        <div class="brightness-control">
            <span>亮度调节：</span>
            <n-slider
                v-model:value="brightness"
                :min="50"
                :max="200"
                :step="10"
                :tooltip="false"
                style="width: 200px"
            />
            <span>{{ brightness }}%</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { NSlider } from 'naive-ui';

interface Props {
    src: string;
}

const props = defineProps<Props>();
const videoRef = ref<HTMLVideoElement>();
const brightness = ref(100); // 默认亮度为100%
</script>

<style scoped>
.video-player {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.brightness-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

video {
    max-width: 100%;
    height: auto;
}
</style>
