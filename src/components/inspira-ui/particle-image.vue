<template>
    <div class="particle-image-container" @mouseenter="showParticles" @mouseleave="hideParticles">
        <img
            ref="imageParticleRef"
            :src="imageSrc"
            :class="cn($props.class)"
            :data-particle-gap="particleGap"
            :data-width="canvasWidth"
            :data-height="canvasHeight"
            :data-gravity="gravity"
            :data-particle-size="particleSize"
            :data-mouse-force="mouseForce"
            :data-renderer="renderer"
            :data-color="color"
            :data-color-arr="colorArr"
            :data-init-position="initPosition"
            :data-init-direction="initDirection"
            :data-fade-position="fadePosition"
            :data-fade-direction="fadeDirection"
            :data-noise="noise"
            :data-responsive-width="responsiveWidth"
            :style="{ display: showImage ? '' : 'none' }"
        />
    </div>
</template>

<script lang="ts" setup>
import { cn } from '@/utils/inspira-ui/utils';
import { inspiraImageParticles, type InspiraImageParticle as ImageParticle } from './inspiraImageParticles';
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

type ParticleImageProps = {
    imageSrc: string;
    class?: string;
    canvasWidth?: string;
    canvasHeight?: string;
    gravity?: string;
    particleSize?: string;
    particleGap?: string;
    mouseForce?: string;
    renderer?: 'default' | 'webgl';
    color?: string;
    colorArr?: number[];
    initPosition?: 'random' | 'top' | 'left' | 'bottom' | 'right' | 'misplaced' | 'none';
    initDirection?: 'random' | 'top' | 'left' | 'bottom' | 'right' | 'none';
    fadePosition?: 'explode' | 'top' | 'left' | 'bottom' | 'right' | 'random' | 'none';
    fadeDirection?: 'random' | 'top' | 'left' | 'bottom' | 'right' | 'none';
    noise?: number;
    responsiveWidth?: boolean;
};

defineProps<ParticleImageProps>();

const imageParticleRef = ref<HTMLImageElement>();
const particles = ref<ImageParticle | any | null>(null);
const showImage = ref(false); // 初始设置为false，等动画完成后再显示
const animationCompleted = ref(false);
let canvasElement: HTMLCanvasElement | any | null = null;

/**
 * 初始化粒子动画
 */
onMounted(() => {
    // 确保图片元素已经渲染
    nextTick(() => {
        const { InspiraImageParticle } = inspiraImageParticles();
        particles.value = new InspiraImageParticle(imageParticleRef.value);

        // 监听动画完成事件
        particles.value.on('stopped', handleAnimationStopped);

        // 保存canvas元素引用
        if (particles.value && particles.value.canvas) {
            canvasElement = particles.value.canvas;
            // 初始隐藏canvas
            canvasElement.style.display = 'none';
        }

        // 等待动画完成后隐藏canvas
        setTimeout(() => {
            if (!animationCompleted.value) {
                handleAnimationStopped();
            }
        }, 2000); // 设置一个合理的超时时间，确保动画有足够时间完成
    });
});

/**
 * 处理动画完成事件
 */
const handleAnimationStopped = () => {
    if (particles.value && particles.value.canvas) {
        canvasElement = particles.value.canvas;
        canvasElement.style.display = 'none';

        // 恢复显示原始图片
        if (imageParticleRef.value) {
            imageParticleRef.value.style.display = '';
        }
        showImage.value = true;
        animationCompleted.value = true;
    }
};

/**
 * 显示粒子动画效果
 */
const showParticles = () => {
    if (animationCompleted.value && canvasElement && particles.value) {
        showImage.value = false;

        // 确保图片被隐藏
        if (imageParticleRef.value) {
            imageParticleRef.value.style.display = 'none';
        }

        // 显示canvas
        canvasElement.style.display = '';

        // 如果动画已经停止，重新启动
        if (particles.value.state === 'stopped') {
            particles.value.start();
        }
    }
};

/**
 * 隐藏粒子动画效果，显示原始图片
 */
const hideParticles = () => {
    if (animationCompleted.value && canvasElement && particles.value) {
        setTimeout(() => {
            showImage.value = true;
            // 隐藏canvas
            canvasElement.style.display = 'none';
        }, 1500);
    }
};

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
    if (particles.value) {
        particles.value.stop();
    }
});

// 用例
// <ParticleImage
//     class="w-50px h-50px"
//     :image-src="logo"
//     :noise="10"
//     mouse-force="2"
//     particleSize="100"
//     particleGap="1"
//     canvasHeight="50"
//     canvasWidth="50"
// />
</script>

<style scoped>
.particle-image-container {
    position: relative;
    display: inline-block;
}
</style>
