<template>
    <div :class="cn('mx-auto block h-100vh overflow-hidden', className)">
        <div class="flex size-full items-center justify-center">
            <div class="size-[1720px] shrink-0 scale-50 sm:scale-75 lg:scale-100">
                <div
                    :style="{
                        transform: 'rotateX(55deg) rotateY(0deg) rotateZ(-45deg)'
                    }"
                    class="relative top-96 right-[40%] grid size-full origin-top-left grid-cols-4 gap-8 transform-3d"
                >
                    <motion.div
                        v-for="(subarray, colIndex) in chunks"
                        :key="colIndex + 'marquee'"
                        class="flex flex-col items-start gap-8"
                        :animate="{ y: colIndex % 2 === 0 ? 100 : -100 }"
                        :transition="{
                            duration: colIndex % 2 === 0 ? 10 : 15,
                            repeat: Infinity,
                            repeatType: 'reverse'
                        }"
                    >
                        <!-- 垂直网格线组件 -->
                        <div
                            :style="getVerticalLineStyle(verticalOffsets[colIndex] || '80px')"
                            :class="
                                cn(
                                    'absolute -left-4 top-[calc(var(--offset)/2*-1)] h-[calc(100%+var(--offset))] w-[var(--width)]',
                                    'bg-[linear-gradient(to_bottom,var(--color),var(--color)_50%,transparent_0,transparent)]',
                                    '[background-size:var(--width)_var(--height)]',
                                    '[mask:linear-gradient(to_top,var(--background)_var(--fade-stop),transparent),_linear-gradient(to_bottom,var(--background)_var(--fade-stop),transparent),_linear-gradient(black,black)]',
                                    '[mask-composite:exclude]',
                                    'z-30',
                                    'dark:bg-[linear-gradient(to_bottom,var(--color-dark),var(--color-dark)_50%,transparent_0,transparent)]'
                                )
                            "
                        ></div>

                        <div v-for="(image, imageIndex) in subarray" :key="imageIndex + image" class="relative">
                            <!-- 水平网格线组件 -->
                            <div
                                :style="getHorizontalLineStyle(horizontalOffsets[imageIndex] || '20px')"
                                :class="
                                    cn(
                                        'absolute -top-4 left-[calc(var(--offset)/2*-1)] h-[var(--height)] w-[calc(100%+var(--offset))]',
                                        'bg-[linear-gradient(to_right,var(--color),var(--color)_50%,transparent_0,transparent)]',
                                        '[background-size:var(--width)_var(--height)]',
                                        '[mask:linear-gradient(to_left,var(--background)_var(--fade-stop),transparent),_linear-gradient(to_right,var(--background)_var(--fade-stop),transparent),_linear-gradient(black,black)]',
                                        '[mask-composite:exclude]',
                                        'z-30',
                                        'dark:bg-[linear-gradient(to_right,var(--color-dark),var(--color-dark)_50%,transparent_0,transparent)]'
                                    )
                                "
                            ></div>

                            <motion.img
                                :src="image"
                                :alt="`Image ${imageIndex + 1}`"
                                class="aspect-[970/700] rounded-lg object-cover ring ring-gray-950/5 hover:shadow-2xl"
                                width="970"
                                height="700"
                                :whileHover="{ y: -10 }"
                                :transition="{
                                    duration: 0.3,
                                    ease: 'easeInOut'
                                }"
                            />
                        </div>
                    </motion.div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
/**
 * InspiraMarquee组件 - 3D图片轮播展示
 * 将图片分成4列，并以交错的方式上下移动，创建3D效果
 * @param {string[]} images - 要展示的图片URL数组
 * @param {string} [className] - 可选的额外CSS类名
 */
import { cn } from '@/utils/inspira-ui/utils';
import { motion } from 'motion-v';

const props = defineProps<{
    images: string[];
    className?: string;
}>();

// 将图片数组分成4等份
const chunks = computed(() => {
    const chunkSize = Math.ceil(props.images.length / 4);
    return Array.from({ length: 4 }, (_, colIndex) => {
        const start = colIndex * chunkSize;
        return props.images.slice(start, start + chunkSize);
    });
});

// 垂直线条的偏移量
const verticalOffsets = ref<Record<number, string>>({});

/**
 * 获取垂直线条样式
 * @param {string} offset - 偏移量
 * @returns {object} - CSS样式对象
 */
const getVerticalLineStyle = (offset: string) => {
    return {
        '--background': '#ffffff',
        '--color': 'rgba(0, 0, 0, 0.2)',
        '--height': '5px',
        '--width': '1px',
        '--fade-stop': '90%',
        '--offset': offset,
        '--color-dark': 'rgba(255, 255, 255, 0.2)',
        'mask-composite': 'exclude'
    };
};

// 水平线条的偏移量
const horizontalOffsets = ref<Record<number, string>>({});

/**
 * 获取水平线条样式
 * @param {string} offset - 偏移量
 * @returns {object} - CSS样式对象
 */
const getHorizontalLineStyle = (offset: string) => {
    return {
        '--background': '#ffffff',
        '--color': 'rgba(0, 0, 0, 0.2)',
        '--height': '1px',
        '--width': '5px',
        '--fade-stop': '90%',
        '--offset': offset,
        '--color-dark': 'rgba(255, 255, 255, 0.2)',
        'mask-composite': 'exclude'
    };
};
</script>
