<template>
    <div class="uploadStyle">
        <SvgIcon name="svgs-add"></SvgIcon>
        <div>{{ title }}</div>
        <div class="text-12px text-#999">{{ msg }}</div>
    </div>
</template>
<script setup lang="ts">
withDefaults(
    defineProps<{
        title?: string;
        msg?: string;
    }>(),
    {
        title: '上传设备图片',
        msg: '只支持.jpg格式'
    }
);
</script>
<style scoped lang="less">
.uploadStyle {
}
</style>
