<template>
    <div class="file-preview flex justify-center items-center w-100% min-h-300px">
        <n-watermark
            class="w-100%"
            content="本文件涉密 禁止拍照或截图"
            :remarks="remarks"
            cross
            selectable
            :font-size="16"
            :line-height="50"
            :width="500"
            :height="200"
            :x-offset="-10"
            :y-offset="28"
            :re-x-offset="90"
            :re-y-offset="80"
            :rotate="-15"
            font-color="rgba(128,128,128,.15)"
        >
            <iframe
                v-if="isOfficeFile"
                class="h-70vh w-100%"
                :src="fileUrl"
                width="100%"
                height="100%"
                @error="handleError"
                @load="handleLoad"
            ></iframe>
            <n-image v-else-if="isImageFile" width="100%" :src="fileUrl" @error="handleError" @load="handleLoad" />
            <video
                v-else-if="isVideoFile"
                class="max-w-100%"
                :src="fileUrl"
                controls
                muted
                @error="handleError"
                @loadeddata="handleLoad"
            />
            <n-empty v-else-if="error" class="empty" :description="error"></n-empty>
            <n-empty v-else class="empty" :description="`${props.name} 暂不支持预览`"></n-empty>
            <n-spin v-if="loading" :show="true" class="abs w-100% h-100% flex justify-center items-center"> </n-spin>
        </n-watermark>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { fileTypes } from '@/utils/common';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';

interface Props {
    id: string;
    name: string;
    format: string;
}

const store = useStore();
const props = withDefaults(defineProps<Props>(), {
    id: '',
    name: '',
    format: ''
});

const remarks = computed(() => {
    return store.userInfo.nickname + dayjs().format('YYYY-MM-DD HH:mm:ss');
});

const fileUrl = ref<string>('');
const loading = ref<boolean>(true);
const error = ref<string>('');

const isOfficeFile = computed(() => fileTypes.office.includes(props.format));
const isImageFile = computed(() => fileTypes.image.includes(props.format));
const isVideoFile = computed(() => fileTypes.video.includes(props.format));

const handleError = () => {
    error.value = '文件加载失败，请稍后重试';
    loading.value = false;
};

const handleLoad = () => {
    loading.value = false;
};

const getFileUrl = async () => {
    try {
        loading.value = true;
        error.value = '';

        if (isOfficeFile.value) {
            fileUrl.value = `${
                import.meta.env.VITE_API || window.location.origin
            }/macrohard/api/v1/word/onlinePreview?fileId=${props.id}`;
        } else {
            const res = await api.file.api.v1.file.download({ id: props.id });
            fileUrl.value = res.data.fileDownloadPreSignedUrl;
        }
    } catch (err) {
        error.value = '获取文件预览地址失败';
        window.$message.error('获取文件预览地址失败');
        loading.value = false;
    }
};

onMounted(() => {
    if (!props.id || !props.format) {
        error.value = '文件信息不完整';
        loading.value = false;
        return;
    }
    getFileUrl();
});
</script>

<style scoped>
.file-preview {
    background-color: #f5f5f5;
    border-radius: 4px;
    position: relative;
}
</style>
