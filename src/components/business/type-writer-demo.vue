<template>
    <div class="demo-container">
        <n-space vertical size="large">
            <!-- 基础使用 -->
            <n-card title="基础打字机效果">
                <type-writer
                    :texts="basicTexts"
                    :type-speed="100"
                    :back-speed="50"
                    :back-delay="2000"
                    text-type="primary"
                    @on-complete="onComplete"
                    @on-string-typed="onStringTyped"
                />
            </n-card>

            <!-- 高级配置 -->
            <n-card title="智能删除效果（注意相同前缀不会被删除）">
                <type-writer
                    :texts="advancedTexts"
                    :type-speed="80"
                    :back-speed="30"
                    :back-delay="1500"
                    :random-speed="[50, 150]"
                    text-type="success"
                    text-depth="2"
                    cursor-char="▎"
                    :cursor-style="{ color: '#18a058', fontWeight: 'bold' }"
                    :smart-backspace="true"
                    @on-complete="onComplete"
                />
            </n-card>

            <!-- 不循环模式 -->
            <n-card title="不循环模式（完成后光标自动隐藏）">
                <n-space vertical>
                    <type-writer
                        ref="typewriterRef"
                        :texts="controlTexts"
                        :type-speed="60"
                        :loop="false"
                        :auto-start="false"
                        text-type="warning"
                        cursor-char="●"
                        :cursor-blink="true"
                    />
                    <n-space>
                        <n-button @click="start" type="primary">开始</n-button>
                        <n-button @click="pause" type="info">暂停</n-button>
                        <n-button @click="resume" type="success">继续</n-button>
                        <n-button @click="restart" type="warning">重新开始</n-button>
                        <n-button @click="stop" type="error">停止</n-button>
                    </n-space>
                </n-space>
            </n-card>

            <!-- 会话缓存演示 -->
            <n-card title="会话缓存演示（刷新页面试试）">
                <n-space vertical>
                    <n-alert type="info" style="margin-bottom: 16px">
                        启用会话缓存后，这个打字机效果在同一浏览器会话中只会播放一次。刷新页面或重新进入页面时，如果已经播放过，会直接显示最后的文本。
                    </n-alert>

                    <type-writer
                        ref="sessionTypewriterRef"
                        :texts="sessionCacheTexts"
                        :type-speed="80"
                        :loop="false"
                        text-type="primary"
                        cursor-char="▌"
                        :session-cache="{
                            enabled: true,
                            key: 'welcome-message',
                            showLastText: true
                        }"
                    />

                    <n-space>
                        <n-button @click="restartSession" type="primary">重新开始</n-button>
                        <n-button @click="clearCache" type="error">清除缓存</n-button>
                        <n-button @click="checkCacheStatus" type="info">检查状态</n-button>
                    </n-space>

                    <n-text v-if="cacheStatus" :type="cacheStatus.type">
                        {{ cacheStatus.message }}
                    </n-text>
                </n-space>
            </n-card>

            <!-- 自定义样式 -->
            <n-card title="自定义样式">
                <type-writer
                    :texts="styledTexts"
                    :type-speed="120"
                    :back-speed="60"
                    text-type="error"
                    :text-style="{
                        fontSize: '24px',
                        fontWeight: 'bold',
                        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text'
                    }"
                    :cursor-style="{
                        color: '#ff6b6b',
                        fontSize: '24px',
                        fontWeight: 'bold',
                        textShadow: '0 0 10px #ff6b6b'
                    }"
                    cursor-char="█"
                />
            </n-card>

            <!-- 配置面板 -->
            <n-card title="动态配置（智能删除演示）">
                <n-space vertical>
                    <n-space>
                        <n-input-number
                            v-model:value="config.typeSpeed"
                            :min="10"
                            :max="1000"
                            :step="10"
                            placeholder="打字速度"
                        />
                        <n-input-number
                            v-model:value="config.backSpeed"
                            :min="10"
                            :max="1000"
                            :step="10"
                            placeholder="删除速度"
                        />
                        <n-input-number
                            v-model:value="config.backDelay"
                            :min="100"
                            :max="5000"
                            :step="100"
                            placeholder="暂停时间"
                        />
                    </n-space>

                    <n-space>
                        <n-select
                            v-model:value="config.textType"
                            :options="textTypeOptions"
                            placeholder="文本类型"
                            style="width: 120px"
                        />
                        <n-input v-model:value="config.cursorChar" placeholder="光标字符" style="width: 100px" />
                        <n-switch v-model:value="config.loop">
                            <template #checked>循环</template>
                            <template #unchecked>不循环</template>
                        </n-switch>
                        <n-switch v-model:value="config.smartBackspace">
                            <template #checked>智能删除</template>
                            <template #unchecked>普通删除</template>
                        </n-switch>
                    </n-space>

                    <n-alert type="info" style="margin-bottom: 16px">
                        开启智能删除后，注意观察"JavaScript"等相同前缀不会被删除，只删除不同的部分！
                    </n-alert>

                    <type-writer
                        :texts="dynamicTexts"
                        :type-speed="config.typeSpeed"
                        :back-speed="config.backSpeed"
                        :back-delay="config.backDelay"
                        :text-type="config.textType"
                        :cursor-char="config.cursorChar"
                        :loop="config.loop"
                        :smart-backspace="config.smartBackspace"
                        :key="configKey"
                    />
                </n-space>
            </n-card>

            <!-- 事件日志 -->
            <n-card title="事件日志">
                <n-scrollbar style="max-height: 200px">
                    <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
                        <n-text :type="log.type">{{ log.message }}</n-text>
                        <n-text depth="3" style="margin-left: 10px">{{ log.time }}</n-text>
                    </div>
                </n-scrollbar>
            </n-card>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import {
    NSpace,
    NCard,
    NButton,
    NInputNumber,
    NSelect,
    NInput,
    NSwitch,
    NScrollbar,
    NText,
    NAlert,
    useMessage
} from 'naive-ui';

const message = useMessage();

const basicTexts = ['欢迎使用打字机组件！', '这是一个基于 Vue3 的打字机效果', '支持多种配置参数', '让你的文本更有趣！'];

const advancedTexts = ['前端开发技术栈', '前端开发工程师', '前端开发最佳实践', '前端性能优化', '前端架构设计'];

const controlTexts = ['点击按钮控制打字机', '支持开始、暂停、继续', '以及重新开始和停止', '完全可控的打字体验'];

const styledTexts = ['✨ 自定义样式', '🎨 渐变文字效果', '💫 发光光标', '🚀 无限可能！'];

const dynamicTexts = [
    'JavaScript 是世界上最好的语言',
    'JavaScript 框架生态丰富',
    'JavaScript 全栈开发',
    'Java 也是不错的选择',
    'Python 简单易学'
];

const sessionCacheTexts = [
    '🎉 欢迎来到我们的网站！',
    '🚀 这是一个现代化的 Web 应用',
    '✨ 体验丝滑的用户交互',
    '💎 享受极致的视觉体验'
];

const typewriterRef = ref();
const sessionTypewriterRef = ref();

const cacheStatus = ref<{
    type: 'success' | 'info' | 'warning' | 'error';
    message: string;
} | null>(null);

const config = reactive({
    typeSpeed: 100,
    backSpeed: 50,
    backDelay: 2000,
    textType: 'default' as any,
    cursorChar: '|',
    loop: true,
    smartBackspace: false
});

const configKey = computed(
    () =>
        `${config.typeSpeed}-${config.backSpeed}-${config.backDelay}-${config.textType}-${config.cursorChar}-${config.loop}-${config.smartBackspace}`
);

const textTypeOptions = [
    { label: '默认', value: 'default' },
    { label: '主要', value: 'primary' },
    { label: '信息', value: 'info' },
    { label: '成功', value: 'success' },
    { label: '警告', value: 'warning' },
    { label: '错误', value: 'error' }
];

const eventLogs = ref<
    Array<{
        type: string;
        message: string;
        time: string;
    }>
>([]);

const addLog = (type: string, message: string) => {
    eventLogs.value.unshift({
        type,
        message,
        time: new Date().toLocaleTimeString()
    });

    if (eventLogs.value.length > 50) {
        eventLogs.value.pop();
    }
};

const start = () => {
    typewriterRef.value?.start();
    addLog('info', '开始打字');
};

const pause = () => {
    typewriterRef.value?.pause();
    addLog('warning', '暂停打字');
};

const resume = () => {
    typewriterRef.value?.resume();
    addLog('success', '继续打字');
};

const restart = () => {
    typewriterRef.value?.restart();
    addLog('info', '重新开始');
};

const stop = () => {
    typewriterRef.value?.stop();
    addLog('error', '停止打字');
};

const restartSession = () => {
    sessionTypewriterRef.value?.restart();
    addLog('info', '重新开始会话缓存演示');
};

const clearCache = () => {
    sessionTypewriterRef.value?.clearSessionCache();
    addLog('warning', '清除会话缓存');
    cacheStatus.value = {
        type: 'success',
        message: '会话缓存已清除，下次访问时将重新播放打字效果'
    };
};

const checkCacheStatus = () => {
    const hasPlayed = sessionTypewriterRef.value?.hasPlayedInSession();
    cacheStatus.value = {
        type: hasPlayed ? 'info' : 'warning',
        message: hasPlayed ? '当前会话已播放过打字效果' : '当前会话未播放过打字效果'
    };
    addLog('info', `检查缓存状态: ${hasPlayed ? '已播放' : '未播放'}`);
};

const onComplete = () => {
    addLog('success', '打字完成');
    message.success('打字完成！');
};

const onStringTyped = (index: number, text: string) => {
    addLog('info', `完成字符串 ${index + 1}: ${text}`);
};
</script>

<style scoped>
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.log-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
    border-bottom: none;
}
</style>
