<template>
    <div class="people-select w-full">
        <n-tree-select v-bind="$attrs" v-model:value="modelValue" :options="filterOptions" filterable />
    </div>
</template>

<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        kind: string;
        filterPeople?: string[];
    }>(),
    {
        modelValue: [],
        kind: 'custom',
        filterPeople: () => []
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);

const options = ref<any[]>([]);
const filterOptions = computed(() => {
    if (props.filterPeople.length > 0) {
        return options.value.filter((item) => !props.filterPeople.includes(item.key));
    }
    return options.value;
});

watch(
    () => props.kind,
    (val: string) => {
        getData(val);
    }
);

const getData = async (kind: string) => {
    switch (kind) {
        case 'designate':
            await api.sass.api.v1.organizationUserInfo.companyList().then((res) => {
                options.value = res.data?.map((item: any) => {
                    return {
                        label: item.nickname,
                        key: item.id
                    };
                });
            });
            break;
        case 'role':
            await api.sass.api.v1.role.list({ noPage: true, status: 'true' }).then((res) => {
                options.value = res.data?.data?.map((item: any) => {
                    return {
                        label: item.name,
                        key: item.id
                    };
                });
            });
            break;
        case 'position':
            await api.sass.api.v1.position.list({ noPage: true, status: 'true' }).then((res) => {
                options.value = res.data?.data?.map((item: any) => {
                    return {
                        label: item.name,
                        key: item.id
                    };
                });
            });
            break;
        case 'group_designate':
            await api.sass.api.v1.organizationUserInfo.groupList().then((res) => {
                options.value = res.data?.map((item: any) => {
                    return {
                        label: item.nickname,
                        key: item.id
                    };
                });
            });
            break;
        default:
            break;
    }
};

onMounted(() => {
    getData(props.kind);
});
</script>

<style scoped></style>
