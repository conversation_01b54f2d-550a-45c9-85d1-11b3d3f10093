<template>
    <div class="bsTab">
        <n-tabs v-model:value="modelValue">
            <n-tab-pane v-for="(item, index) in tabs" :key="index" :name="item.value" :tab="item.label"> </n-tab-pane>
        </n-tabs>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        tabs: any[];
    }>(),
    {
        modelValue: '',
        tabs: () => []
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
</script>
<style scoped lang="less">
.bsTab {
}
</style>
