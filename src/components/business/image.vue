<template>
    <div class="image h-$n-height flex-center-start">
        <n-image :src="modelValue" v-bind="options" />
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        options?: any;
    }>(),
    {
        modelValue: ''
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
</script>
<style scoped lang="less"></style>
