<template>
    <div class="p-24px flex-1">
        <div class="flex items-center justify-between mb-24px">
            <span class="text-20px font-bold">{{ title }}</span>
            <span class="text-14px c-#666 cursor-pointer" @click="onMoreClick">更多</span>
        </div>
        <div class="flex-center flex-col gap-16px">
            <bs-todo-notice-card-item
                v-for="(item, index) in displayList"
                :key="index"
                :img-src="imgSrc"
                :title="item.title"
                :date="item.date"
            />
            <n-image v-if="list.length === 0" :src="emptyImg" preview-disabled />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
const props = defineProps<{
    title: string;
    list: { title: string; date: string }[];
    imgSrc: string;
    emptyImg: string;
    max?: number;
}>();
const emit = defineEmits(['more']);

const displayList = computed(() => {
    if (props.max) {
        return props.list.slice(0, props.max);
    }
    return props.list;
});

function onMoreClick() {
    emit('more');
}
</script>
