<template>
    <div class="minio-upload bg-#fff">
        <n-upload
            v-bind="uploadProps"
            v-model:file-list="fileList"
            :custom-request="customUploadMethod"
            :renderIcon="renderIcon"
            @before-upload="beforeUpload"
            :on-remove="handleRemove"
        >
            <n-upload-dragger v-if="$slots.drag">
                <slot name="drag" />
            </n-upload-dragger>
            <template v-else>
                <slot />
            </template>
        </n-upload>

        <div v-if="uploading && loading" class="upload-loading-mask">
            <div class="progress-list">
                <span class="title">文件上传中</span>
                <div class="progress-item" v-for="(info, uid) in uploadProgressMap" :key="uid">
                    <div class="filename">{{ info.name }}</div>
                    <n-progress
                        type="line"
                        :percentage="Math.floor(info.progress)"
                        :status="info.status"
                        :height="8"
                        :show-indicator="true"
                    />
                </div>
                <n-button type="error" size="small" @click="abortAllUploads" class="mt-16px"> 取消上传 </n-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { UploadCustomRequestOptions, UploadFileInfo, UploadSettledFileInfo } from 'naive-ui';
import SvgIcon from '@/components/layout/svg-icon/index.vue';
import { iconMap } from '@/utils/common';
import { MinioUploadProps } from '@/typings/minio-upload';

const props = withDefaults(defineProps<MinioUploadProps>(), {
    loading: false,
    limitFileSize: true,
    fileSize: 50 * 1024 * 1024,
    uploadProps: () => ({}),
    fileList: () => []
});
const emit = defineEmits<{
    (e: 'success', file: { id: string; name: string; format: string }): void;
    (e: 'error', error: any): void;
    (e: 'update:fileList', fileList: UploadFileInfo[]): void;
}>();

// 文件列表
const { fileList, uploadProps } = useVModels(props, emit);

// 是否有上传任务中
const uploading = computed(() => Object.keys(uploadProgressMap).length > 0);

// 存储每个文件的上传状态和进度
const uploadProgressMap = reactive<
    Record<
        string,
        {
            name: string;
            progress: number;
            status: 'success' | 'error' | 'default' | 'warning' | undefined;
            abort?: () => void;
        }
    >
>({});

// 自定义文件图标
const renderIcon = (file: UploadSettledFileInfo) => {
    const format: string = file.name.split('.').pop() || '';
    return h(SvgIcon, { name: iconMap[format], size: 14, style: { marginRight: '5px', marginBottom: '1px' } });
};

const beforeUpload = (options: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
    /**
     * 限制上传文件格式
     */
    const allowedTypes: string = props.uploadProps.accept || '';
    const format: string = options.file.name.split('.').pop()?.replace('.', '') || '';
    const fileType: string = options.file.type?.split('/')[0] || '';

    if (allowedTypes) {
        const types = allowedTypes.split(',');
        const isValid = types.some((type) => {
            if (type.includes('*')) {
                const category = type.split('/')[0];
                return fileType === category;
            }
            return type.includes(format);
        });

        if (!isValid) {
            window.$message.error('上传文件格式错误');
            return false;
        }
    }
    /**
     * 限制上传文件大小
     */
    if (props.limitFileSize) {
        const fileSize = options.file?.file?.size || 0;
        if (fileSize > props.fileSize) {
            window.$message.error(`上传文件大小不能超过${props.fileSize / (1024 * 1024)}MB`);
            return false;
        }
    }
    /**
     * 更新文件列表
     */
    fileList.value = options.fileList;
    emit('update:fileList', options.fileList);
    /**
     * 默认返回true，允许上传
     */
    return true;
};

// 上传函数（带进度）
const uploadWithProgress = (
    file: File,
    url: string,
    onProgress: (percent: number) => void,
    onAbort: (abortFn: () => void) => void
): Promise<void> => {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', url, true);
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');

        xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
                onProgress((event.loaded / event.total) * 100);
            }
        };

        xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve();
            } else {
                reject(new Error(`上传失败，状态码 ${xhr.status}`));
            }
        };

        xhr.onerror = () => reject(new Error('网络错误'));
        xhr.onabort = () => reject(new Error('用户取消上传'));

        xhr.send(file);
        onAbort(() => xhr.abort());
    });
};

/**
 * 自定义上传方法
 * 处理文件上传并更新进度状态
 */
const customUploadMethod = async (options: UploadCustomRequestOptions) => {
    const { file, onFinish, onError, onProgress } = options;

    const uid = file.id || String(Date.now() + Math.random());

    // 更新文件列表中的状态
    const updateFileInList = (id: string, status: string, percentage?: number) => {
        const index = fileList.value.findIndex((f) => f.id === id);
        if (index !== -1) {
            fileList.value[index].status = status as any;
            if (percentage !== undefined) {
                fileList.value[index].percentage = percentage;
            }
            emit('update:fileList', [...fileList.value]);
        }
    };

    // 初始化上传进度状态
    uploadProgressMap[uid] = {
        name: file.name,
        progress: 0,
        status: 'default'
    };

    // 更新文件列表状态为上传中
    updateFileInList(uid, 'uploading', 0);

    try {
        const res = await window.api.file.api.v1.file.upload({
            fileName: file.name,
            fileSize: file.file?.size || 0
        });

        if (!res.data) throw new Error('获取预签名 URL 失败');

        const { fileUploadPreSignedUrl, id } = res.data;
        const url = fileUploadPreSignedUrl;
        await uploadWithProgress(
            file.file as File,
            url,
            (percent) => {
                uploadProgressMap[uid].progress = percent;
                updateFileInList(uid, 'uploading', percent);
                onProgress?.({ percent });
            },
            (abortFn) => {
                uploadProgressMap[uid].abort = abortFn;
            }
        );

        window.$message.success('上传成功');

        // 更新文件状态为完成
        updateFileInList(uid, 'finished', 100);
        onFinish({ id, name: file.name, status: 'finished' });
        emit('success', { id, name: file.name, format: file.name.split('.').pop() || '' });

        uploadProgressMap[uid].status = 'success';
    } catch (err) {
        // 更新文件状态为错误
        updateFileInList(uid, 'error');
        onError();
        emit('error', err);
        uploadProgressMap[uid].status = 'error';
        window.$message.error(err instanceof Error ? err.message : '上传失败');
    } finally {
        setTimeout(() => {
            delete uploadProgressMap[uid];
        }, 1000);
    }
};

// 取消所有上传
const abortAllUploads = () => {
    Object.keys(uploadProgressMap).forEach((uid) => {
        const abortFn = uploadProgressMap[uid].abort;
        if (abortFn) {
            abortFn();
            // 更新文件列表状态
            const index = fileList.value.findIndex((f) => f.id === uid);
            if (index !== -1) {
                fileList.value[index].status = 'error';
                emit('update:fileList', [...fileList.value]);
            }
        }
    });
};

const handleRemove = (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo> }) => {
    props.uploadProps.onRemove?.(options as any);
    abortAllUploads();
};
</script>

<style lang="less" scoped>
.minio-upload {
    :deep(.n-upload) {
        .n-upload-trigger {
            width: 100%;
        }
        .n-upload-dragger {
            padding: 0;
        }
        .n-upload-file--image-card-type {
            .n-upload-file-info__name {
                margin-right: 0;
            }
        }
        .n-upload-file--text-type {
            .n-upload-file-info__name {
                margin-right: 80px !important;
            }
        }
    }

    .upload-loading-mask {
        position: fixed;
        inset: 0;
        z-index: 9999;
        background-color: rgba(255, 255, 255, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;

        .progress-list {
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: #fff;
            width: 40vw;
            min-width: 400px;
            max-height: 60vh;
            overflow-y: auto;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);

            .title {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 16px;
                text-align: center;
            }

            .progress-item {
                margin-bottom: 16px;
            }

            .filename {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 8px;
            }
        }
    }
}
</style>
