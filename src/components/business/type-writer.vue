<template>
    <div class="typewriter-container">
        <n-text :type="textType" :depth="textDepth" :class="textClass" :style="textStyle">
            {{ displayText }}
            <span
                v-if="showCursor && (!isComplete || props.loop)"
                class="cursor"
                :class="{ 'cursor-blink': cursorBlink }"
                :style="cursorStyle"
            >
                {{ cursorChar }}
            </span>
        </n-text>
    </div>
</template>

<script setup lang="ts">
import { NText } from 'naive-ui';

interface TypewriterProps {
    // 文本数组
    texts: string[];
    // 打字速度 (毫秒)
    typeSpeed?: number;
    // 删除速度 (毫秒)
    backSpeed?: number;
    // 开始前延迟 (毫秒)
    startDelay?: number;
    // 完成后暂停时间 (毫秒)
    backDelay?: number;
    // 是否循环
    loop?: boolean;
    // 是否显示光标
    showCursor?: boolean;
    // 光标字符
    cursorChar?: string;
    // 光标是否闪烁
    cursorBlink?: boolean;
    // 光标样式
    cursorStyle?: Record<string, any>;
    // 是否自动开始
    autoStart?: boolean;
    // 是否停止删除（只打字不删除）
    stopBlinkinOnComplete?: boolean;
    // 随机打字速度范围
    randomSpeed?: [number, number];
    // 文本类型 (naive-ui NText 的 type 属性)
    textType?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';
    // 文本深度 (naive-ui NText 的 depth 属性)
    textDepth?: '1' | '2' | '3';
    // 文本样式类
    textClass?: string;
    // 文本内联样式
    textStyle?: Record<string, any>;
    // 智能删除（只删除不同的部分）
    smartBackspace?: boolean;
    // 保留HTML标签
    preserveHtml?: boolean;
    // 会话缓存配置
    sessionCache?: {
        // 是否启用会话缓存
        enabled: boolean;
        // 缓存key，用于区分不同的打字机实例
        key: string;
        // 缓存完成后是否直接显示最后一个文本
        showLastText?: boolean;
    };
}

const props = withDefaults(defineProps<TypewriterProps>(), {
    texts: () => [],
    typeSpeed: 50,
    backSpeed: 50,
    startDelay: 0,
    backDelay: 2000,
    loop: true,
    showCursor: true,
    cursorChar: '|',
    cursorBlink: true,
    cursorStyle: () => ({}),
    autoStart: true,
    stopBlinkinOnComplete: false,
    randomSpeed: () => [0, 0],
    textType: 'default',
    textDepth: '1',
    textClass: '',
    textStyle: () => ({}),
    smartBackspace: false,
    preserveHtml: false,
    sessionCache: () => ({ enabled: false, key: '', showLastText: true })
});

const emit = defineEmits<{
    onComplete: [];
    onStringTyped: [index: number, text: string];
    onLastStringBackspaced: [];
    onTypingPaused: [];
    onTypingResumed: [];
}>();

const displayText = ref('');
const currentStringIndex = ref(0);
const currentCharIndex = ref(0);
const isTyping = ref(false);
const isDeleting = ref(false);
const isPaused = ref(false);
const isComplete = ref(false);
const hasPlayedInSession = ref(false);

let typeTimeout: any = null;

// 会话缓存相关方法
const getSessionCacheKey = () => {
    return `typewriter-played-${props.sessionCache?.key || 'default'}`;
};

const checkSessionCache = () => {
    if (!props.sessionCache?.enabled || props.loop) return false;

    try {
        const cacheKey = getSessionCacheKey();
        const hasPlayed = sessionStorage.getItem(cacheKey) === 'true';
        hasPlayedInSession.value = hasPlayed;
        return hasPlayed;
    } catch (error) {
        console.warn('SessionStorage not available:', error);
        return false;
    }
};

const setSessionCache = () => {
    if (!props.sessionCache?.enabled || props.loop) return;

    try {
        const cacheKey = getSessionCacheKey();
        sessionStorage.setItem(cacheKey, 'true');
        hasPlayedInSession.value = true;
    } catch (error) {
        console.warn('SessionStorage not available:', error);
    }
};

const clearSessionCache = () => {
    if (!props.sessionCache?.enabled) return;

    try {
        const cacheKey = getSessionCacheKey();
        sessionStorage.removeItem(cacheKey);
        hasPlayedInSession.value = false;
    } catch (error) {
        console.warn('SessionStorage not available:', error);
    }
};

// 初始化显示（如果已经播放过）
const initializeFromCache = () => {
    if (checkSessionCache()) {
        if (props.sessionCache?.showLastText && props.texts.length > 0) {
            // 直接显示最后一个文本
            currentStringIndex.value = props.texts.length - 1;
            const lastText = props.texts[props.texts.length - 1];
            displayText.value = lastText;
            currentCharIndex.value = lastText.length;
            isComplete.value = true;
        }
    }
};

// 计算实际打字速度
const getTypeSpeed = () => {
    const [min, max] = props.randomSpeed;
    if (min > 0 && max > 0) {
        return Math.random() * (max - min) + min;
    }
    return props.typeSpeed;
};

// 获取当前文本
const getCurrentText = () => {
    return props.texts[currentStringIndex.value] || '';
};

// 开始打字
const startTyping = () => {
    if (props.texts.length === 0) return;

    // 检查会话缓存
    if (checkSessionCache()) {
        // 如果已经播放过，直接完成
        initializeFromCache();
        return;
    }

    isTyping.value = true;
    isDeleting.value = false;
    isPaused.value = false;

    type();
};

// 打字逻辑
const type = () => {
    if (isPaused.value) return;

    const currentText = getCurrentText();

    if (isDeleting.value) {
        // 删除模式
        const targetLength = props.smartBackspace ? getSmartBackspaceTarget() : 0;

        if (currentCharIndex.value > targetLength) {
            currentCharIndex.value = Math.max(targetLength, currentCharIndex.value - 1);
            displayText.value = currentText.substring(0, currentCharIndex.value);

            typeTimeout = setTimeout(type, props.backSpeed);
        } else {
            // 删除完成，准备打下一个字符串
            isDeleting.value = false;
            currentStringIndex.value = (currentStringIndex.value + 1) % props.texts.length;

            if (currentStringIndex.value === 0 && !props.loop) {
                // 如果不循环且回到第一个，则完成
                setTimeout(() => {
                    isComplete.value = true;
                    emit('onComplete');
                    return;
                }, 2000);
            }

            typeTimeout = setTimeout(type, props.typeSpeed);
        }
    } else {
        // 打字模式
        if (currentCharIndex.value < currentText.length) {
            currentCharIndex.value++;
            displayText.value = currentText.substring(0, currentCharIndex.value);

            typeTimeout = setTimeout(type, getTypeSpeed());
        } else {
            // 当前字符串打完
            emit('onStringTyped', currentStringIndex.value, currentText);

            if (props.loop || currentStringIndex.value < props.texts.length - 1) {
                // 需要继续到下一个字符串
                isDeleting.value = true;
                typeTimeout = setTimeout(type, props.backDelay);
            } else {
                // 所有字符串都打完了
                setTimeout(() => {
                    isComplete.value = true;
                    setSessionCache(); // 设置会话缓存
                    emit('onComplete');
                }, 2000);
            }
        }
    }
};

// 获取智能删除的目标长度
const getSmartBackspaceTarget = () => {
    if (!props.smartBackspace) return 0;

    const currentText = getCurrentText();
    const nextIndex = (currentStringIndex.value + 1) % props.texts.length;
    const nextText = props.texts[nextIndex] || '';

    // 如果是最后一个且不循环，删除全部
    if (!props.loop && currentStringIndex.value === props.texts.length - 1) {
        return 0;
    }

    let commonPrefixLength = 0;
    const minLength = Math.min(currentText.length, nextText.length);

    // 找出相同前缀的长度
    while (commonPrefixLength < minLength && currentText[commonPrefixLength] === nextText[commonPrefixLength]) {
        commonPrefixLength++;
    }

    return commonPrefixLength;
};

// 暂停打字
const pause = () => {
    isPaused.value = true;
    if (typeTimeout) {
        clearTimeout(typeTimeout);
        typeTimeout = null;
    }
    emit('onTypingPaused');
};

// 恢复打字
const resume = () => {
    if (isPaused.value) {
        isPaused.value = false;
        emit('onTypingResumed');
        type();
    }
};

// 停止打字
const stop = () => {
    if (typeTimeout) {
        clearTimeout(typeTimeout);
        typeTimeout = null;
    }
    isTyping.value = false;
    isDeleting.value = false;
    isPaused.value = false;
};

// 重置
const reset = () => {
    stop();
    displayText.value = '';
    currentStringIndex.value = 0;
    currentCharIndex.value = 0;
    isComplete.value = false;
    // 重置时清除会话缓存
    clearSessionCache();
};

// 重新开始
const restart = () => {
    reset();
    setTimeout(startTyping, props.startDelay);
};

// 监听 texts 变化
watch(
    () => props.texts,
    () => {
        restart();
    },
    { deep: true }
);

// 暴露方法给父组件
defineExpose({
    start: startTyping,
    pause,
    resume,
    stop,
    reset,
    restart,
    clearSessionCache,
    hasPlayedInSession: () => hasPlayedInSession.value
});

onMounted(() => {
    // 首先检查会话缓存
    initializeFromCache();

    // 如果没有播放过且需要自动开始
    if (props.autoStart && props.texts.length > 0 && !hasPlayedInSession.value) {
        setTimeout(startTyping, props.startDelay);
    }
});

onUnmounted(() => {
    stop();
});
</script>

<style scoped>
.typewriter-container {
    display: inline-block;
}

.cursor {
    display: inline-block;
    color: inherit;
    font-weight: inherit;
}

.cursor-blink {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0;
    }
}
</style>
