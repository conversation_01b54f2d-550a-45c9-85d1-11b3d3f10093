<template>
    <div class="set-sign">
        <div class="sign-main" v-if="hasSet">
            <div class="wrap1" v-show="isLandscape">
                <div class="sign-title color-red">建议请保持手机在横屏模式下进行签名！</div>
                <canvas class="canvas1" ref="canvas1"></canvas>
                <div class="actions">
                    <n-button class="w-200px" @click="handleClear1">重签</n-button>
                    <n-button class="w-200px" type="primary" @click="handlePreview1">完成签字</n-button>
                </div>
            </div>
            <div class="wrap2" v-show="!isLandscape">
                <div class="actionsWrap">
                    <div class="actions">
                        <n-button class="w-200px" @click="handleClear2">重签</n-button>
                        <n-button class="w-200px" type="primary" @click="handlePreview2">完成签字</n-button>
                    </div>
                </div>
                <canvas class="canvas" ref="canvas2"></canvas>
                <div class="text-box">
                    <div class="text color-red">建议请保持手机在横屏模式下进行签名</div>
                </div>
            </div>
        </div>
        <div v-else class="h-100vh p-15px">
            <div class="box">
                <svg-icon name="svgs-success" size="120" />
                <div class="text-20px">签名提交成功</div>
                <div class="text-20px">请前往pc端进行确认</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import SmoothSignature from 'smooth-signature';
import useStore from '@/store/modules/main';
import { useEventListener } from '@vueuse/core';

const store = useStore();
const route = useRoute();

const canvas2 = ref<any>(null);
const canvas1 = ref<any>(null);
const signature1 = ref<any>(null);
const signature2 = ref<any>(null);
const hasSet = ref(false);
const taskId = ref('');

const initSignature1 = () => {
    const canvas = canvas1.value as any;
    const options = {
        width: window.innerWidth - 30,
        height: window.innerHeight - 100,
        minWidth: 2,
        maxWidth: 4,
        openSmooth: true,
        bgcolor: '#ffffff'
    };
    if (signature1.value) {
        signature1.value.removeListener();
    }
    signature1.value = new SmoothSignature(canvas, options);
};
const initSignature2 = () => {
    const canvas = canvas2.value as any;
    const options = {
        width: window.innerWidth - 120,
        height: window.innerHeight - 30,
        minWidth: 2,
        maxWidth: 4,
        openSmooth: true,
        bgcolor: '#ffffff'
    };
    if (signature2.value) {
        signature2.value.removeListener();
    }
    signature2.value = new SmoothSignature(canvas, options);
};

const handleClear1 = () => {
    const sgn = signature1.value;
    const sgn2 = signature2.value;
    sgn2.clear();
    sgn.clear();
};
const handleClear2 = () => {
    const sgn = signature1.value;
    const sgn2 = signature2.value;
    sgn2.clear();
    sgn.clear();
};

const compressBase64Image = (
    base64: string,
    quality = 0.7,
    maxWidth = 800,
    maxHeight = 800,
    outputType: 'image/jpeg' | 'image/webp' = 'image/jpeg'
): Promise<string> => {
    return new Promise((resolve) => {
        const img = new window.Image();
        img.src = base64;
        img.onload = () => {
            let { width, height } = img;
            // 限制最大宽高
            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width = width * ratio;
                height = height * ratio;
            }
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                resolve(base64);
                return;
            }
            ctx.drawImage(img, 0, 0, width, height);
            resolve(canvas.toDataURL(outputType, quality));
        };
    });
};

const setData = async (image: any) => {
    if (!taskId.value) {
        return window.$message.error('参数异常，请刷新PC端页面重新生成二维码扫码签名！');
    }
    await $apis.nebula.api.v1.signature.uploadSignature({
        signatureBase64: image,
        taskId: taskId.value
    });
    hasSet.value = false;
};
const handlePreview1 = async () => {
    const sgn = signature1.value;
    const isEmpty = sgn.isEmpty();
    if (isEmpty) {
        window.$message.error('请签字');
        return;
    }
    const pngUrl = sgn.getPNG();
    const compressedUrl = await compressBase64Image(pngUrl, 0.8, 800, 420, 'image/webp');
    setData(compressedUrl);
};
const handlePreview2 = async () => {
    const sgn2 = signature2.value;
    const isEmpty = sgn2.isEmpty();
    if (isEmpty) {
        window.$message.error('请签字');
        return;
    }
    const canvas = sgn2.getRotateCanvas(-90);
    const pngUrl = canvas.toDataURL();
    const compressedUrl = await compressBase64Image(pngUrl, 0.8, 800, 420, 'image/webp');
    setData(compressedUrl);
};
const isLandscape = ref(false);
const getIsLandscape = () => {
    const orientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    if (orientation === 'portrait') {
        isLandscape.value = false;
    } else {
        isLandscape.value = true;
    }
};

function init() {
    getIsLandscape();
    nextTick(() => {
        initSignature1();
        initSignature2();
    });
}
onMounted(async () => {
    hasSet.value = true;
    if (route.query.token) {
        await store.setToken(route.query.token as string);
    }
    if (route.query.taskId) {
        taskId.value = route.query.taskId as string;
    }
    init();
    useEventListener(window, 'resize', () => {
        setTimeout(() => init(), 100);
    });
});

onUnmounted(() => {
    window.removeEventListener('resize', () => {
        setTimeout(() => init(), 100);
    });
});
</script>

<style scoped lang="less">
.sign-main {
    height: 100vh;
    width: 100vw;

    canvas {
        border-radius: 10px;
        border: 2px dashed #ccc;
    }

    .wrap1 {
        height: 100%;
        padding: 15px;

        .actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
    }

    .wrap2 {
        padding: 15px;
        height: 100%;
        display: flex;
        justify-content: center;

        .actionsWrap {
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .canvas {
            flex: 1;
        }

        .actions {
            white-space: nowrap;
            transform: rotate(90deg);
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .text-box {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 30px;

            .text {
                display: flex;
                white-space: nowrap;
                transform: rotate(90deg);
            }
        }
    }
}

.sign-title {
    display: flex;
    justify-content: center;
}

.box {
    margin: 0 auto;
    margin-top: 100px;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
}
</style>
