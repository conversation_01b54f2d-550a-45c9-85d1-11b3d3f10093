<template>
    <n-tree-select
        :value="value"
        :options="treeOptions"
        :placeholder="placeholder"
        :default-expand-all="defaultExpandAll"
        :show-path="showPath"
        :clearable="clearable"
        :filterable="filterable"
        :multiple="multiple"
        :checkable="checkable"
        :cascade="cascade"
        :max-tag-count="maxTagCount"
        :loading="loading"
        @update:value="handleValueChange"
    />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

interface TreeNode {
    label: string;
    value: string;
    code?: string;
    children?: TreeNode[];
    [key: string]: any;
}

interface PathInfo {
    ids: string[];
    names: string[];
    codes: string[];
    pathString: string;
    codePathString: string;
    selectedId: string;
    selectedCode: string;
}

interface Props {
    value: string | string[] | null;
    options?: any[];
    placeholder?: string;
    defaultExpandAll?: boolean;
    showPath?: boolean;
    clearable?: boolean;
    filterable?: boolean;
    multiple?: boolean;
    checkable?: boolean;
    cascade?: boolean;
    maxTagCount?: number | 'responsive';
    separator?: string;
    codeSeparator?: string;
    needPathInfo?: boolean;
}

interface Emits {
    (e: 'update:value', value: string | string[] | null): void;
    (e: 'change', value: string | string[] | null, pathInfo: PathInfo | PathInfo[] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择',
    defaultExpandAll: false,
    showPath: true,
    clearable: true,
    filterable: false,
    multiple: false,
    checkable: false,
    cascade: false,
    maxTagCount: 'responsive',
    separator: '-',
    codeSeparator: '-',
    needPathInfo: false
});

const emit = defineEmits<Emits>();

const loading = ref(false);

// 转换树形数据格式
const internalOptions = ref<any[]>([]);
const treeOptions = computed(() => {
    const options = props.options || internalOptions.value;
    return $utils.treeData.convertTreeData(options);
});

// 调用接口获取数据
const fetchData = async () => {
    if (props.options) return;

    try {
        loading.value = true;
        const res = await window.api.sass.api.v1.organization.tree.list();
        internalOptions.value = res.data.data || [];
    } catch (error) {
        console.error('获取树形数据失败:', error);
        internalOptions.value = [];
    } finally {
        loading.value = false;
    }
};

// 组件挂载时获取数据
onMounted(() => {
    fetchData();
});

const getPathInfo = (value: string, options: TreeNode[]): PathInfo | null => {
    if (!value || !options || !Array.isArray(options)) {
        return null;
    }
    const findPath = (
        nodes: TreeNode[],
        targetValue: string,
        currentPath: { id: string; name: string; code: string }[] = []
    ): { id: string; name: string; code: string }[] | null => {
        for (const node of nodes) {
            if (!node || typeof node !== 'object') continue;
            const newPath = [
                ...currentPath,
                {
                    id: node.value,
                    name: node.name || node.label || '', // 优先取 name 字段
                    code: node.code || ''
                }
            ];
            if (node.value === targetValue) {
                return newPath;
            }
            if (node.children && Array.isArray(node.children) && node.children.length > 0) {
                const result = findPath(node.children, targetValue, newPath);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    };
    const path = findPath(options, value);
    if (!path) return null;
    const ids = path.map((item) => item.id);
    const names = path.map((item) => item.name);
    const codes = path.map((item) => item.code);
    const filteredNames = names.filter((name) => name && name.trim());
    const filteredCodes = codes.filter((code) => code && code.trim());
    const pathString = filteredNames.join(props.separator);
    const codePathString = filteredCodes.join(props.codeSeparator);
    const result = {
        ids,
        names,
        codes,
        pathString,
        codePathString,
        selectedId: value,
        selectedCode: codes[codes.length - 1] || ''
    };
    return result;
};

const getMultiplePathInfo = (values: string[], options: TreeNode[]): PathInfo[] => {
    if (!Array.isArray(values)) return [];
    return values.map((value) => getPathInfo(value, options)).filter(Boolean) as PathInfo[];
};

const handleValueChange = (value: string | string[] | null) => {
    emit('update:value', value);
    // 根据 needPathInfo 开关决定是否计算 pathInfo
    if (props.needPathInfo) {
        let pathInfo: PathInfo | PathInfo[] | null = null;
        if (value) {
            if (Array.isArray(value)) {
                // 多选模式
                pathInfo = getMultiplePathInfo(value, treeOptions.value);
            } else {
                // 单选模式
                pathInfo = getPathInfo(value, treeOptions.value);
            }
        }
        emit('change', value, pathInfo);
    } else {
        // 不需要 pathInfo 时，只发送值
        emit('change', value, null);
    }
};

defineExpose({
    getPathInfo: (value: string) => getPathInfo(value, treeOptions.value),
    getMultiplePathInfo: (values: string[]) => getMultiplePathInfo(values, treeOptions.value),
    treeOptions
});
</script>

<style scoped></style>
