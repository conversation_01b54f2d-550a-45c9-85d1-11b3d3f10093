# 待办详情页组件

本文件夹存放与待办事项详情页面相关的业务组件，主要用于文件管理系统中的各种审批流程表单。

## 文件分类

### 📋 审批表单组件
这些组件用于展示不同类型的审批申请详情，均为只读表单，用于审批流程中的信息展示。

#### 1. `custom-approval-form.vue` - 文件发放申请表单
- **功能**：展示文件管理-发放申请的详细信息
- **包含字段**：申请人、申请日期、发放类型、文件类型、文件类别、发放原因、期望发放日期等
- **特色功能**：包含发放清单表格，支持文件权限、接收方等详细信息展示
- **使用场景**：FILE_GRANT 业务类型的审批流程

#### 2. `recycle-form.vue` - 文件回收申请表单
- **功能**：展示文件管理-回收申请的详细信息
- **包含字段**：回收人、回收日期、发放信息、回收原因等
- **特色功能**：包含回收清单表格，展示需要回收的文件详情
- **使用场景**：FILE_RECLAIM 业务类型的审批流程

#### 3. `paper-disposal-form.vue` - 纸质文件处置表单
- **功能**：展示文件管理-纸质文件处置申请的详细信息
- **包含字段**：回收人、回收日期、发放信息、处置方式等
- **特色功能**：包含处置清单表格，展示处置文件的份数信息
- **使用场景**：FILE_DISPOSAL 业务类型的审批流程

#### 4. `inclusion-process.vue` - 文件纳入流程组件
- **功能**：展示纳入集团外部文件到子公司的申请信息
- **包含字段**：集团文件编号、集团文件名称、原文件编号、原文件版次
- **特色功能**：支持多个文件的批量展示
- **使用场景**：FILE_INCORPORATE 业务类型的审批流程


## 使用方式

这些组件主要在 `src/data/approval-auto-form.ts` 中被引用，作为不同审批业务类型的表单组件：

```typescript
// 文件发放申请
FILE_GRANT: {
    '1.0.0': {
        title: '文件管理-发放申请',
        components: markRaw(BsCustomApprovalForm)
    }
},
// 文件回收申请
FILE_RECLAIM: {
    '1.0.0': {
        title: '文件管理-回收申请',
        components: markRaw(BsRecycleForm)
    }
},
// 纸质文件处置
FILE_DISPOSAL: {
    '1.0.0': {
        title: '文件管理-纸质文件处置',
        components: markRaw(BsPaperDisposalForm)
    }
},
// 文件纳入流程
FILE_INCORPORATE: {
    '1.0.0': {
        title: '纳入集团外部文件到子公司审批单',
        fields: [
            {
                field: 'data',
                component: markRaw(BsInclusionProcess),
                gridItemProps: { span: 24 }
            }
        ]
    }
}
```