<template>
    <div class="radio h-$n-height flex-center">
        <n-radio
            v-for="(item, index) in options"
            :key="index"
            :checked="item.value === modelValue"
            @change="modelValue = item.value"
            :disabled="disabled"
            >{{ item.label }}</n-radio
        >
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        options?: Array<{
            [key: string]: any;
            label: string;
            value: any;
        }>;
        disabled?: boolean;
    }>(),
    {
        options: () => [],
        disabled: false
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
</script>
<style scoped lang="less">
.radio {
}
</style>
