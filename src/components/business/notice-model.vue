<template>
    <div class="notice-center">
        <!-- 左侧菜单 -->
        <div class="notice-center-slider">
            <div class="notice-center-menu">
                <div
                    v-for="item in menuList"
                    :key="item.key"
                    :class="['notice-center-menu-item', { active: tab === item.key }]"
                    @click="handleTabChange(item.key)"
                >
                    {{ item.label }}<span v-if="item.key === 'unread'"> {{ unreadCount }}</span>
                </div>
            </div>
        </div>
        <!-- 右侧内容 -->
        <div class="notice-center-main">
            <!-- 顶部筛选栏 -->
            <div class="notice-center-toolbar">
                <n-space>
                    <n-select
                        v-model:value="filter.level"
                        :options="levelOptions"
                        class="w-142px!"
                        clearable
                        placeholder="紧急程度"
                    />
                    <n-input
                        v-model:value="filter.moduleName"
                        class="w-142px!"
                        placeholder="请输入来源模块"
                        clearable
                    />
                    <n-date-picker v-model:value="filter.dateRange" type="datetimerange" class="w-380px!" clearable />
                </n-space>
                <n-button text @click="markAllRead">
                    <template #icon>
                        <n-icon>
                            <cleaning-services-round />
                        </n-icon>
                    </template>
                    全部已读
                </n-button>
            </div>
            <!-- 消息列表 -->
            <div class="notice-center-list">
                <n-scrollbar style="max-height: 500px">
                    <div
                        v-for="msg in msgList.filter((msg) => (tab === 'unread' ? !msg.isRead : msg.isRead))"
                        :key="msg.id"
                        class="notice-center-item"
                    >
                        <n-badge
                            class="notice-center-item-icon"
                            dot
                            :offset="[-8, 8]"
                            :show="tab === 'unread' && !msg.isRead"
                        >
                            <img :src="calendarIcon" class="icon-img" />
                        </n-badge>
                        <div class="notice-center-item-content">
                            <div class="notice-center-item-title">
                                <n-tag
                                    v-if="msg.level && msg.level > 1"
                                    size="small"
                                    :bordered="false"
                                    :type="getLevelMeta(msg.level).type"
                                >
                                    {{ getLevelMeta(msg.level).label }}
                                </n-tag>
                                <span class="msg-text">{{ msg.content }}</span>
                            </div>
                            <div class="notice-center-item-meta">
                                <n-time :time="msg.createdAt" format="yyyy-MM-dd HH:mm" />
                                <span class="source">来源:{{ msg.moduleName }}</span>
                            </div>
                        </div>
                        <n-button
                            v-if="!msg.isRead"
                            class="ml-20px"
                            type="primary"
                            size="small"
                            secondary
                            round
                            @click="markRead(msg)"
                        >
                            已读
                        </n-button>
                    </div>
                </n-scrollbar>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { CleaningServicesRound } from '@vicons/material';
import calendarIcon from '@/assets/images/entry/notice.webp';
import type { YonduNotification } from '@/api/apis/yondu/api/v1/notification';
import useStore from '@/store/modules/main';

const store = useStore();
const emit = defineEmits(['markRead']);

// 紧急程度元数据
const levelMetaList = [
    { value: 3, label: '紧急', type: 'error' },
    { value: 2, label: '预警', type: 'warning' },
    { value: 1, label: '普通', type: 'default' }
] as const;
// 紧急程度下拉选项
const levelOptions = levelMetaList.map((item) => ({ label: item.label, value: item.value }));
// 获取紧急程度元数据
function getLevelMeta(level: number) {
    return levelMetaList.find((item) => item.value === level) || { label: '', type: 'default' };
}

// 左侧菜单配置
const menuList = [
    { key: 'unread', label: '未读' },
    { key: 'read', label: '已读' }
] as const;
type TabKey = (typeof menuList)[number]['key'];
const tab = ref<TabKey>('unread');
// 切换 tab 并重置筛选
function handleTabChange(key: TabKey) {
    tab.value = key;
    filter.value.level = null;
    filter.value.moduleName = '';
    filter.value.dateRange = null;
}
// 未读数量
const unreadCount = computed(() => msgList.value.filter((msg) => !msg.isRead).length);

// 筛选条件
const filter = ref<{
    level: number | null;
    moduleName: string;
    dateRange: [number, number] | null;
}>({
    level: null,
    moduleName: '',
    dateRange: null
});

watchDebounced(
    () => filter.value,
    () => {
        getMsgList(filter.value);
    },
    {
        deep: true,
        debounce: 500
    }
);

// 获取数据
const getMsgList = async (params: Record<string, any> = {}) => {
    const res = await $apis.yondu.api.v1.notification.list({
        noPage: true,
        ...params
    });
    msgList.value = res.data.data;
};

// 消息列表（mock 数据）
const msgList = ref<YonduNotification[]>([]);

// 标记单条为已读
const markRead = async (msg: YonduNotification) => {
    await $apis.yondu.api.v1.notification.read({
        ids: [msg.id as string],
        recipientId: store.userInfo.id
    });
    window.$message.success('已读成功');
    getMsgList(filter.value);
    emit('markRead');
};
// 全部标记为已读
const markAllRead = async () => {
    await $apis.yondu.api.v1.notification.read({
        ids: msgList.value.map((msg) => msg.id as string),
        recipientId: store.userInfo.id
    });
    window.$message.success('已读成功');
    getMsgList(filter.value);
    emit('markRead');
};

onMounted(() => {
    getMsgList();
});
</script>

<style scoped lang="less">
.notice-center {
    display: flex;
    min-height: 400px;
}
.notice-center-slider {
    border-right: 1px solid #e3e8f0;
    padding: 20px 20px 0 0;
    .notice-center-menu {
        .notice-center-menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 162px;
            height: 45px;
            color: #666;
            padding: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 0 10px 0;
            border-radius: 8px;
            background: #f5f5f5;
            &.active {
                background: #ebf1ff;
                color: #2d6fff;
                font-weight: bold;
            }
        }
    }
}
.notice-center-main {
    flex: 1;
    padding: 0 10px 0 20px;
    .notice-center-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 18px;
        .n-button {
            font-size: 14px;
        }
    }
    .notice-center-list {
        .notice-center-item {
            display: flex;
            align-items: flex-start;
            background: #fff;
            border-radius: 12px;
            margin-bottom: 10px;
            margin-right: 15px;
            padding: 20px 28px 20px 24px;
            border: 1px solid #e3e8f0;

            &:hover {
                border-color: #b3cfff;
            }

            .notice-center-item-icon {
                margin-right: 20px;
                .icon-img {
                    width: 44px;
                    height: 44px;
                    object-fit: contain;
                }
            }

            .notice-center-item-content {
                flex: 1;
                min-width: 0;
                .notice-center-item-title {
                    font-size: 16px;
                    font-weight: 500;
                    margin-bottom: 8px;
                    .n-tag {
                        margin-right: 8px;
                    }
                    .msg-text {
                        color: #222;
                        margin-left: 0;
                        word-break: break-all;
                    }
                }
                .notice-center-item-meta {
                    font-size: 13px;
                    color: #999;
                    .source {
                        margin-left: 24px;
                    }
                }
            }
        }
    }
}
</style>
