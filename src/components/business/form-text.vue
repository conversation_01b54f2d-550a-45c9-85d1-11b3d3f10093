<template>
    <div class="form-text p-15px w-100% bg-$color h-100%">
        <slot>
            <n-ellipsis v-if="ellipsis">
                {{ modelValue }}
            </n-ellipsis>
            <template v-else>
                {{ modelValue }}
            </template>
        </slot>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        color?: string;
        ellipsis?: boolean;
    }>(),
    {
        modelValue: [],
        color: '#fff',
        ellipsis: false
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
useCssVars(() => ({
    color: props.color
}));
</script>
<style scoped lang="less">
.form-text {
}
</style>
