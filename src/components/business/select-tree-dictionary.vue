<template>
    <n-tree-select
        :value="value"
        :options="treeOptions"
        :placeholder="loading ? '加载中...' : placeholder"
        :default-expand-all="defaultExpandAll"
        :show-path="showPath"
        :clearable="clearable"
        :filterable="filterable"
        :multiple="multiple"
        :checkable="checkable"
        :cascade="cascade"
        :max-tag-count="maxTagCount"
        :loading="loading"
        @update:value="handleValueChange"
    />
</template>

<script setup lang="ts">
interface TreeNode {
    label: string;
    value: string;
    code?: string;
    children?: TreeNode[];
    [key: string]: any;
}

interface PathInfo {
    ids: string[];
    names: string[];
    codes: string[];
    pathString: string;
    codePathString: string;
    selectedId: string;
    selectedCode: string;
}

interface Props {
    value: string | string[] | null;
    options?: any[];
    placeholder?: string;
    defaultExpandAll?: boolean;
    showPath?: boolean;
    clearable?: boolean;
    filterable?: boolean;
    multiple?: boolean;
    checkable?: boolean;
    cascade?: boolean;
    maxTagCount?: number | 'responsive';
    separator?: string;
    codeSeparator?: string;
    needPathInfo?: boolean;
    params?: string | null;
}

interface Emits {
    (e: 'update:value', value: string | string[] | null): void;
    (e: 'change', value: string | string[] | null, pathInfo: PathInfo | PathInfo[] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择',
    defaultExpandAll: false,
    showPath: true,
    clearable: true,
    filterable: false,
    multiple: false,
    checkable: false,
    cascade: false,
    maxTagCount: 'responsive',
    separator: '-',
    codeSeparator: '-',
    needPathInfo: false,
    params: null
});

const emit = defineEmits<Emits>();

// 内部数据管理
const internalOptions = ref<any[]>([]);
const loading = ref(false);
const dictData = ref<any>(null);

// 转换树形数据格式
const treeOptions = computed(() => {
    const options = props.options || internalOptions.value;
    return $utils.treeData.convertTreeData(options);
});

// 调用接口获取数据
const fetchData = async () => {
    if (props.options) return;
    if (!dictData.value) return;
    if (!props.params) return;
    try {
        loading.value = true;
        const fileCategoryObj = dictData.value;
        const fileCategoryId = fileCategoryObj[props.params] || Object.values(fileCategoryObj)[0];

        const result = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: fileCategoryId
        });
        internalOptions.value = result.data || result || [];
    } catch (error) {
        console.error('获取树形数据失败:', error);
        internalOptions.value = [];
    } finally {
        loading.value = false;
    }
};

// 组件挂载时获取 dict 并缓存
onMounted(async () => {
    loading.value = true;
    try {
        const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
        dictData.value = dict.data.data[0].extra.fileCategory;
        await fetchData(); // 初始化时拉一次
    } catch (error) {
        dictData.value = null;
        internalOptions.value = [];
    } finally {
        loading.value = false;
    }
});

// 监听 apiParams 变化自动刷新
watch(
    () => props.params,
    () => {
        internalOptions.value = [];
        fetchData();
    },
    { deep: true }
);

const getPathInfo = (value: string, options: TreeNode[]): PathInfo | null => {
    if (!value || !options || !Array.isArray(options)) {
        return null;
    }

    const findPath = (
        nodes: TreeNode[],
        targetValue: string,
        currentPath: { id: string; name: string; code: string }[] = []
    ): { id: string; name: string; code: string }[] | null => {
        for (const node of nodes) {
            if (!node || typeof node !== 'object') continue;

            const newPath = [
                ...currentPath,
                {
                    id: node.value,
                    name: node.label,
                    code: node.code || ''
                }
            ];

            if (node.value === targetValue) {
                return newPath;
            }

            if (node.children && Array.isArray(node.children) && node.children.length > 0) {
                const result = findPath(node.children, targetValue, newPath);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    };

    const path = findPath(options, value);

    if (!path) return null;

    const ids = path.map((item) => item.id);
    const names = path.map((item) => item.name);
    const codes = path.map((item) => item.code);

    // 过滤空值并拼接路径
    const filteredNames = names.filter((name) => name && name.trim());
    const filteredCodes = codes.filter((code) => code && code.trim());

    const pathString = filteredNames.join(props.separator);
    const codePathString = filteredCodes.join(props.codeSeparator);

    const result = {
        ids,
        names,
        codes,
        pathString,
        codePathString,
        selectedId: value, // 当前选中的节点ID
        selectedCode: codes[codes.length - 1] || '' // 当前选中的节点代码（路径中的最后一个）
    };

    return result;
};

const getMultiplePathInfo = (values: string[], options: TreeNode[]): PathInfo[] => {
    if (!Array.isArray(values)) return [];

    return values.map((value) => getPathInfo(value, options)).filter(Boolean) as PathInfo[];
};

const handleValueChange = (value: string | string[] | null) => {
    emit('update:value', value);

    // 根据 needPathInfo 开关决定是否计算 pathInfo
    if (props.needPathInfo) {
        let pathInfo: PathInfo | PathInfo[] | null = null;

        if (value) {
            if (Array.isArray(value)) {
                // 多选模式
                pathInfo = getMultiplePathInfo(value, treeOptions.value);
            } else {
                // 单选模式
                pathInfo = getPathInfo(value, treeOptions.value);
            }
        }

        emit('change', value, pathInfo);
    } else {
        // 不需要 pathInfo 时，只发送值
        emit('change', value, null);
    }
};

defineExpose({
    getPathInfo: (value: string) => getPathInfo(value, treeOptions.value),
    getMultiplePathInfo: (values: string[]) => getMultiplePathInfo(values, treeOptions.value),
    treeOptions
});
</script>
