<template>
    <alert-content :on-default-save="save">
        <template v-if="modelValue.type === 'custom'">
            <n-form-item ref="formItemRef" v-for="item in modelValue.data" :key="item.nodeId" :rule="rule">
                <template #label>
                    <div class="flex items-center">
                        <span class="w-80px">办理环节</span>
                        <span class="font-bold">{{ item.nodeName }}</span>
                    </div>
                </template>
                <div class="flex-v w-full gap-8px">
                    <div v-if="item.approverIds" class="flex items-center">
                        <span class="w-90px ml-2px">办理人员</span>
                        <people-select v-model:modelValue="item.approverIds" kind="designate" multiple />
                    </div>
                    <div v-if="item.ccIds" class="flex items-center">
                        <span class="w-90px">抄送人员</span>
                        <people-select v-model:modelValue="item.ccIds" kind="designate" multiple />
                    </div>
                </div>
            </n-form-item>
        </template>
        <template v-if="modelValue.type === 'department'">
            <n-form-item label="抄送人员">
                <people-select v-model:modelValue="modelValue.data.ccIds" kind="designate" multiple />
            </n-form-item>
        </template>
    </alert-content>
</template>

<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: {}
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);

const rule = {
    trigger: ['input', 'blur'],
    validator() {
        for (const element of modelValue.value.data) {
            if (
                (element.approverIds && element.approverIds.length === 0) ||
                (element.ccIds && element.ccIds.length === 0)
            ) {
                return new Error('请至少选择一个人员');
            }
        }
        return true;
    }
};

const formItemRef = ref();
const save = async () => {
    await Promise.all(formItemRef.value.map((itemRef: any) => itemRef?.validate()));
};
</script>

<style scoped></style>
