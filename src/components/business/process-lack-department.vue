<template>
    <alert-content :on-default-save="save">
        <n-form-item ref="formItemRef" label="审批单位" :rule="rule">
            <n-select
                v-model:value="selectedValue"
                :options="props.options"
                labelField="name"
                valueField="id"
                placeholder="请选择审批单位"
            ></n-select>
        </n-form-item>
    </alert-content>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
    options: Array<{
        label: string;
        value: string;
    }>;
}>();

const emit = defineEmits<{
    (e: 'save', value: string): void;
}>();

const selectedValue = ref('');

const rule = {
    trigger: ['input', 'blur'],
    validator() {
        if (selectedValue.value === '') {
            return new Error('请选择一个单位或者部门');
        }
        return true;
    }
};

const formItemRef = ref();
const save = async () => {
    await formItemRef.value?.validate();
    emit('save', selectedValue.value);
};
</script>

<style scoped></style>
