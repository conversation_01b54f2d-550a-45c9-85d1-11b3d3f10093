<template>
    <div class="signature-display">
        <div :style="{ width: `${width}px`, height: `${height}px` }">
            <div class="signature-container" :style="containerStyle">
                <canvas ref="canvasRef" :style="canvasStyle" @click="previewSignature" />

                <!-- 加载状态 -->
                <div v-if="loading" class="loading-overlay">
                    <n-spin size="small" />
                </div>

                <!-- 错误状态 -->
                <div v-if="error" class="error-overlay">
                    <n-text depth="3" style="font-size: 12px; line-height: 1"> 加载失败 </n-text>
                </div>

                <!-- 空状态 -->
                <div v-if="!modelValue && !loading && !error" class="empty-overlay">
                    <n-text depth="3" style="font-size: 12px; line-height: 1"> 无签名 </n-text>
                </div>
            </div>
        </div>

        <!-- 预览模态框 -->
        <n-modal
            class="signature-preview-modal"
            v-model:show="showPreview"
            preset="card"
            title="签名预览"
            style="width: 600px; max-width: 600px"
        >
            <div class="preview-container">
                <canvas ref="previewCanvasRef" />
            </div>
        </n-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { NText, NSpin, NModal } from 'naive-ui';
import type { CSSProperties } from 'vue';

interface Props {
    modelValue?: string;
    width?: number;
    height?: number;
    backgroundColor?: string;
    borderColor?: string;
    showBorder?: boolean;
    quality?: 'high' | 'medium' | 'low';
    autoFit?: boolean;
    noPreview?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    width: 100,
    height: 40,
    backgroundColor: '#ffffff',
    borderColor: '#e0e0e0',
    showBorder: true,
    quality: 'high',
    autoFit: true,
    noPreview: false
});

const emit = defineEmits<{
    loaded: [dimensions: { width: number; height: number }];
    error: [error: string];
}>();

// 响应式数据
const canvasRef = ref<HTMLCanvasElement>();
const previewCanvasRef = ref<HTMLCanvasElement>();
const loading = ref(false);
const error = ref(false);
const imageLoaded = ref(false);
const showPreview = ref(false);
const originalDimensions = ref({ width: 0, height: 0 });

// 计算属性
const containerStyle = computed(
    () =>
        ({
            position: 'relative' as CSSProperties['position'],
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: props.backgroundColor,
            border: props.showBorder ? `1px solid ${props.borderColor}` : 'none'
        } as CSSProperties)
);

const canvasStyle = computed(() => ({
    maxWidth: '100%',
    maxHeight: '100%',
    cursor: 'pointer'
}));

// 高质量绘制图片（主显示和预览共用）
function drawSignatureImage({
    img,
    canvas,
    width,
    height,
    quality,
    backgroundColor
}: {
    img: HTMLImageElement;
    canvas: HTMLCanvasElement;
    width: number;
    height: number;
    quality: 'high' | 'medium' | 'low';
    backgroundColor: string;
}) {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 创建临时canvas
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = width;
    tempCanvas.height = height;
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    // 背景
    tempCtx.fillStyle = backgroundColor;
    tempCtx.fillRect(0, 0, width, height);

    // 缩放比例，保持原图比例
    const fitScale = Math.min(width / img.width, height / img.height);
    const scaledWidth = img.width * fitScale;
    const scaledHeight = img.height * fitScale;
    const x = (width - scaledWidth) / 2;
    const y = (height - scaledHeight) / 2;

    // 多次叠加绘制增强线条清晰度
    const repeatCount = quality === 'high' ? 20 : 5;
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = 'high';
    for (let i = 0; i < repeatCount; i++) {
        tempCtx.globalAlpha = 1.0;
        tempCtx.drawImage(img, x, y, scaledWidth, scaledHeight);
    }

    // 适配DPI
    const dpr = window.devicePixelRatio || 1;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);
    ctx.imageSmoothingEnabled = quality === 'high';
    if (ctx.imageSmoothingEnabled) ctx.imageSmoothingQuality = 'high';
    ctx.clearRect(0, 0, width, height);
    ctx.drawImage(tempCanvas, 0, 0, width, height);
}

// 加载并绘制图片
const loadAndDrawImage = async () => {
    if (!props.modelValue) {
        imageLoaded.value = false;
        return;
    }
    loading.value = true;
    error.value = false;
    try {
        const img = new window.Image();
        await new Promise<void>((resolve, reject) => {
            img.onload = () => {
                originalDimensions.value = {
                    width: img.width,
                    height: img.height
                };
                resolve();
            };
            img.onerror = () => reject(new Error('图片加载失败'));
            const base64Data = props.modelValue?.startsWith('data:')
                ? props.modelValue
                : `data:image/png;base64,${props.modelValue}`;
            img.src = base64Data as string;
        });
        await nextTick();
        drawSignatureImage({
            img,
            canvas: canvasRef.value as HTMLCanvasElement,
            width: props.width,
            height: props.height,
            quality: props.quality,
            backgroundColor: props.backgroundColor
        });
        imageLoaded.value = true;
        emit('loaded', originalDimensions.value);
    } catch (err) {
        error.value = true;
        emit('error', err instanceof Error ? err.message : '未知错误');
    } finally {
        loading.value = false;
    }
};

// 预览签名
const previewSignature = async () => {
    if (props.noPreview) return;
    if (!imageLoaded.value) return;
    showPreview.value = true;
    await nextTick();
    if (props.modelValue) {
        const img = new window.Image();
        img.onload = () => {
            drawSignatureImage({
                img,
                canvas: previewCanvasRef.value as HTMLCanvasElement,
                width: Math.min(img.width * 2, 500),
                height: Math.min(img.height * 2, 300),
                quality: props.quality,
                backgroundColor: props.backgroundColor
            });
        };
        const base64Data = props.modelValue.startsWith('data:')
            ? props.modelValue
            : `data:image/png;base64,${props.modelValue}`;
        img.src = base64Data;
    }
};

// 监听props变化
watch(() => props.modelValue, loadAndDrawImage, { immediate: true });

onMounted(() => {
    loadAndDrawImage();
});
</script>

<style scoped lang="less">
.signature-display {
    display: inline-block;
    overflow: hidden;
    .signature-container {
        position: relative;
        width: 100%;
        height: 100%;
        .loading-overlay,
        .error-overlay,
        .empty-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.8);
        }
    }
    .preview-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }
}
</style>
