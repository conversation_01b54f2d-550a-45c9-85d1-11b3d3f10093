<template>
    <div class="timeline">
        <n-timeline>
            <n-timeline-item v-for="(item, index) in data" :key="index" :type="getStatusInfo(item.status).type">
                <div class="bg-#ffff p-10px py-20px flex flex-col gap-10px b-rd-4px of-hidden">
                    <div class="flex-v flex-col gap-10px">
                        <span class="text-14px bold">
                            <span>{{ item.nodeName }}</span>
                            <span class="text-12px text-#5D5C5C"
                                >({{ item.signingKind === 'and' ? '会签' : '或签' }})</span
                            >
                        </span>
                        <span class="text-12px text-#5D5C5C">{{
                            item.approvers?.map((k: any) => k.approverNickname).join('、')
                        }}</span>
                        <p class="text-11px text-#5D5C5C">
                            {{ item.updatedAt === 0 ? '' : dayjs(item.updatedAt).format('YYYY-MM-DD HH:mm:ss') }}
                        </p>
                    </div>

                    <template v-if="item.signingKind === 'and'">
                        <template v-for="k in item.approvers" :key="k.approverId">
                            <div
                                v-if="k.status === 'passed'"
                                class="flex-v text-12px text-#181616 bg-#F9FAFC p-10px relative"
                            >
                                <p class="c-var-primary-color text-12px">
                                    <span>{{ k.approverNickname }}</span>
                                    <span class="text-#5D5C5C ml-5px text-11px">
                                        {{ dayjs(k.updatedAt).format('YYYY-MM-DD HH:mm:ss') }}
                                    </span>
                                </p>
                                <span v-if="k.comment" class="py-5px">{{ k.comment }} </span>
                                <div v-if="k.extra?.fileName" class="mt-10px text-#5D5C5C">
                                    <svg-icon :name="iconMap[k.extra?.format || '']" size="12" class="mr-5px" />
                                    <span class="text-11px cursor-pointer" @click="filePreview(k.extra)"
                                        >{{ k.extra?.fileName }}
                                    </span>
                                </div>
                                <span
                                    class="abs top-2px right-2px text-11px"
                                    :class="[getStatusInfo(k.status).color]"
                                    >{{ getStatusInfo(k.status).label }}</span
                                >
                            </div>
                        </template>
                    </template>
                    <template v-else>
                        <template v-for="k in item.approvers" :key="k.approverId">
                            <div v-if="item.status === 'passed'" class="flex-v p-10px relative bg-#F9FAFC text-#181616">
                                <p class="c-var-primary-color text-12px">
                                    <span>{{ k.approverNickname }}</span>
                                    <span class="text-#5D5C5C ml-5px text-11px">
                                        {{ dayjs(k.updatedAt).format('YYYY-MM-DD HH:mm:ss') }}
                                    </span>
                                </p>
                                <p v-if="k.comment" class="mt-2px text-12px">{{ k.comment }}</p>
                                <div v-if="k.extra?.fileName" class="mt-10px text-#5D5C5C">
                                    <svg-icon :name="iconMap[k.extra?.format || '']" size="12" class="mr-5px" />
                                    <span class="text-11px cursor-pointer" @click="filePreview(k.extra)"
                                        >{{ k.extra?.fileName }}
                                    </span>
                                </div>
                                <span
                                    class="abs top-2px right-2px text-10px"
                                    :class="[getStatusInfo(k.status).color]"
                                    >{{ getStatusInfo(k.status).label }}</span
                                >
                            </div>
                        </template>
                    </template>

                    <div
                        class="abs top-0 right-0 text-#fff p-x-8px p-y-3px text-10px b-rd-lb-4px b-rd-rt-4px"
                        :class="{
                            [getStatusInfo(item.status).bgColor]: true
                        }"
                    >
                        {{ getStatusInfo(item.status).label }}
                    </div>
                </div>
            </n-timeline-item>
        </n-timeline>
    </div>
</template>
<script setup lang="ts">
import { iconMap } from '@/utils/common';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        data?: any[];
    }>(),
    {
        data: () => []
    }
);
const emit = defineEmits(['update:data']);
const { data } = useVModels(props, emit);

const getStatusInfo = (status: keyof typeof $datas.approvalStatus) => {
    const statusData = $datas.approvalStatus[status];
    return {
        type: (statusData.type === 'primary' ? 'info' : statusData.type) as
            | 'success'
            | 'error'
            | 'warning'
            | 'default'
            | 'info',
        bgColor: statusData.bgColor || '',
        color: statusData.color || '',
        label: statusData.label || '进行中'
    };
};

const filePreview = (row: Record<string, any>) => {
    $alert.dialog({
        title: `文件预览: ${row.fileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: row.fileId,
            name: row.fileName,
            format: row.fileName.split('.').pop()
        }
    });
};
</script>
<style scoped lang="less"></style>
