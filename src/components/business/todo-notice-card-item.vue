<template>
    <div
        class="w-full h-96px flex items-center p-24px rounded-6px box-border border-[1px,solid,#fff] backdrop-blur-10px bg-[linear-gradient(79deg,_#FFFFFF_15%,_rgba(235,243,254,0.4)_95%)]"
    >
        <n-image :src="imgSrc" class="w-52px h-52px mr-16px" preview-disabled />
        <div class="flex-1">
            <div class="text-16px font-500 mb-8px">{{ title }}</div>
            <div class="text-14px c-#666">{{ date }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    imgSrc: string;
    title: string;
    date: string;
}>();
</script>
