<template>
    <div ref="container" class="w-full"></div>
</template>

<script setup lang="ts">
import JSONEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.css';

import type { JSONEditorMode } from 'jsoneditor';

const props = defineProps<{ value: any; mode?: JSONEditorMode }>();
const emit = defineEmits<{
    (e: 'update:value', value: any): void;
    (e: 'error'): void;
}>();

const container = ref<HTMLElement | null>(null);
let editor: JSONEditor | null = null;

onMounted(() => {
    if (!container.value) return;
    editor = new JSONEditor(container.value, {
        mode: props.mode || 'code',
        indentation: 2,
        mainMenuBar: false,
        navigationBar: false,
        statusBar: false,
        search: false,
        allowSchemaSuggestions: true,
        onChange: () => {
            try {
                const val = editor?.get();
                emit('update:value', val);
            } catch (e) {
                emit('error'); // 抛出错误
            }
        },
        onError: () => {
            emit('error');
        }
    });
    editor.set(props.value || {});
});

watch(
    () => props.value,
    (val) => {
        if (editor && JSON.stringify(editor.get()) !== JSON.stringify(val)) {
            editor.set(val);
        }
    }
);

onBeforeUnmount(() => {
    if (editor) {
        editor.destroy();
        editor = null;
    }
});
</script>

<style lang="less">
/* 例如在你的全局样式或组件样式里加 */
.jsoneditor {
    border: 1px solid #d0d0d0;
    font-size: 14px;
}
.jsoneditor-mode-preview {
    min-height: 100px;
    padding: 10px;
}
</style>
