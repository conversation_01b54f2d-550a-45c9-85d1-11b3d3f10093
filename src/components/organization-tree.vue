<template>
    <n-tree
        :pattern="patternVal"
        :show-irrelevant-nodes="false"
        :selected-keys="selectedKeys"
        block-line
        :cancelable="false"
        :data="data"
        label-field="name"
        key-field="id"
        :render-label="({ option }) => `${option.name}【${option.userCount || 0}】`"
        @update:selected-keys="selectRow"
    >
        <template #prefix="{ rawNode }">
            <SvgIcon class="mr-5px" v-if="rawNode.nodeType === 0" name="svgs-companiesSettings" color="#7facf5" />
            <SvgIcon class="mr-5px" v-if="rawNode.nodeType === 1" name="svgs-wuye" color="#7facf5" />
            <SvgIcon class="mr-5px" v-if="rawNode.nodeType === 2" name="svgs-zuzhi" color="#e8a5a5" />
        </template>
        <template #suffix="{ rawNode }" v-if="canEdit">
            <n-dropdown trigger="hover" :options="dropdownBtnList(rawNode.nodeType)" @select="editBtn($event, rawNode)">
                <SvgIcon name="svgs-qita" />
            </n-dropdown>
        </template>
    </n-tree>
</template>

<script lang="ts" setup>
import { OrganizationListData } from '@/api/sass/api/v1/organization';
import { TreeOption } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const props = withDefaults(
    defineProps<{
        canEdit?: boolean;
        initValue?: boolean;
    }>(),
    {
        canEdit: true,
        initValue: true
    }
);
const emit = defineEmits<{
    (e: 'edit', k: number, row: OrganizationListData): void;
    (e: 'selected', keys: string[], option: TreeOption[]): void;
}>();

const data = ref<OrganizationListData[]>([]);

const dropdownBtnList = computed(() => (type: number) => {
    return type === 0
        ? [
              {
                  label: '新增子公司',
                  key: 'subUnit',
                  disabled: (type !== 0 && type !== 1) || store.permissions.indexOf('addSubOrganization') < 0
              },
              {
                  label: '新增子部门',
                  key: 'department',
                  disabled: store.permissions.indexOf('addSubOrganization') < 0
              },
              {
                  label: '编辑',
                  key: 'edit',
                  disabled: store.permissions.indexOf('editOrganization') < 0
              },
              {
                  label: '设置领导',
                  key: 'setUpLeaders',
                  disabled: store.permissions.indexOf('setUpLeaders') < 0
              }
          ]
        : [
              {
                  label: '新增子部门',
                  key: 'department',
                  disabled: store.permissions.indexOf('addSubOrganization') < 0
              },
              {
                  label: '编辑',
                  key: 'edit',
                  disabled: store.permissions.indexOf('editOrganization') < 0
              },
              {
                  label: '设置领导',
                  key: 'setUpLeaders',
                  disabled: store.permissions.indexOf('setUpLeaders') < 0
              },
              {
                  label: '删除',
                  key: 'delete',
                  disabled: store.permissions.indexOf('deleteDepartment') < 0
              }
          ];
});

// 功能按钮
const editType = ref();
const editRow = ref();
const editBtn = (k: number, row: OrganizationListData) => {
    editType.value = k;
    editRow.value = row;
    emit('edit', k, row);
};

// 选中
const selectRow = (keys: string[], option: (TreeOption | null)[]) => {
    selectedKeys.value = keys;
    emit(
        'selected',
        keys,
        option.filter((item): item is TreeOption => item !== null)
    );
};

// 搜索
const patternVal = ref('');
const filter = (str: string) => {
    if (!str) init();
    patternVal.value = str;
};

// 初始化
const selectedKeys = ref<string[]>([]);
const init = async () => {
    const res = await window.api.sass.api.v1.organization.tree.list();
    data.value = res.data.data;
    if (props.initValue && data.value.length > 0) {
        if (selectedKeys.value.length < 1 || (editType.value === 6 && selectedKeys.value[0] === editRow.value.id)) {
            selectedKeys.value = [data.value[0].id];
            selectRow(selectedKeys.value, [data.value[0]]);
        }
    }
};

onMounted(init);
defineExpose({ init, filter });
</script>

<style scoped></style>
