<template>
    <n-form
        class="formValidate"
        :class="{ whiteFormTheme: whiteFormTheme }"
        ref="formRef"
        :rules="rules"
        :model="modelValue"
        v-bind="config"
    >
        <n-grid v-bind="gridProps" :cols="cols">
            <template v-for="(item, index) in field" :key="index">
                <n-grid-item v-bind="item.gridItemProps" :span="get(item, 'gridItemProps.span', cols)">
                    <n-form-item
                        :path="item.field"
                        v-bind="item.config"
                        :class="{
                            notBorder: item.notBorder
                        }"
                    >
                        <template v-if="item.label" #label>
                            <div class="text-center">{{ item.label }}</div>
                        </template>
                        <template v-if="item.slots && item.slots.gridBefore">
                            <component
                                :is="item.slots.gridBefore"
                                :field="item.field"
                                :rules="item.rules"
                                :formConfig="config"
                                :formData="modelValue"
                            />
                        </template>
                        <template v-if="componentMapConfig[item.component as any]">
                            <component
                                :is="componentMapConfig[item.component as any]"
                                v-bind="{
                                ...item.props,
                                [item.fieldModel || `value`]: get(modelValue, item.field),
                                [`onUpdate:${item.fieldModel || 'value'}`]: (v: any) => {
                                    set(modelValue, item.field, v)
                                }
                            }"
                                :formData="modelValue"
                            >
                                <!-- 动态插槽继承，后续其他组件也可以这样做 -->
                                <template v-for="(slotItem, key) in item?.slots" :key="key" #[key]="scope">
                                    <template v-if="!builtInSlot.includes(key)">
                                        <component
                                            :is="slotItem"
                                            :field="item.field"
                                            :rules="item.rules"
                                            :formConfig="config"
                                            :formData="modelValue"
                                            v-bind="scope"
                                        />
                                    </template>
                                </template>
                            </component>
                        </template>
                        <template v-else>
                            <component
                                v-if="item.component"
                                :is="item.component"
                                :field="item.field"
                                :rules="item.rules"
                                :formConfig="config"
                                :formData="modelValue"
                                v-bind="{
                                    ...item.props,
                                    [item.fieldModel || `modelValue`]: get(modelValue, item.field),
                                    [`onUpdate:${item.fieldModel || 'modelValue'}`]: (v: any) => {
                                        set(modelValue, item.field, v)
                                    }
                                }"
                            >
                                <!-- 动态插槽继承，后续其他组件也可以这样做 -->
                                <template v-for="(slotItem, key) in item?.slots" :key="key" #[key]="scope">
                                    <template v-if="!builtInSlot.includes(key)">
                                        <component
                                            :is="slotItem"
                                            :field="item.field"
                                            :rules="item.rules"
                                            :formConfig="config"
                                            :formData="modelValue"
                                            v-bind="scope"
                                        />
                                    </template>
                                </template>
                            </component>
                        </template>
                        <template v-if="item.slots && item.slots.gridAefter">
                            <component
                                :is="item.slots.gridAefter"
                                :field="item.field"
                                :rules="item.rules"
                                :formConfig="config"
                                :formData="modelValue"
                            />
                        </template>
                        <!-- 动态插槽继承，后续其他组件也可以这样做 -->
                        <template v-for="(slotItem, key) in item?.slots" :key="key" #[getKey(key)]="scope">
                            <template v-if="builtInFormSlot.includes(key)">
                                <component
                                    :is="slotItem"
                                    :field="item.field"
                                    :rules="item.rules"
                                    :formConfig="config"
                                    :formData="modelValue"
                                    v-bind="scope"
                                />
                            </template>
                        </template>
                    </n-form-item>
                </n-grid-item>
            </template>
        </n-grid>
    </n-form>
</template>
<script setup lang="ts">
import { FormRules, FormProps, GridProps } from 'naive-ui';
import * as naiveUI from 'naive-ui';
import { get, set } from 'lodash';
const getKey = (key: any) => {
    const name = (key || '').replace(/form/, '').toLowerCase();
    return name === 'default' ? null : name;
};
const builtInFormSlot = ref<any>(['formFeedback', 'formLabel']);
const builtInSlot = computed<any>(() => ['gridBefore', 'gridAefter'].concat(builtInFormSlot.value));
const componentMapConfig = shallowRef<any>({
    input: naiveUI.NInput,
    number: naiveUI.NInputNumber,
    select: naiveUI.NSelect,
    cascader: naiveUI.NCascader,
    datePicker: naiveUI.NDatePicker,
    switch: naiveUI.NSwitch,
    upload: naiveUI.NProUpload,
    transferTree: naiveUI.NTransferTree,
    treeSelect: naiveUI.NTreeSelect
});
const formRef = ref();
const props = defineProps<{
    modelValue: Record<string, any>;
    field: FormValidateField;
    config?: FormProps;
    gridProps?: GridProps;
    // 白色表单主题，只在弹框中生效
    whiteFormTheme?: boolean;
}>();
const cols = computed(() => {
    return get(props.gridProps, 'cols', 1);
});
const emit = defineEmits(['update:modelValue', 'save']);
const { modelValue, field, config } = useVModels(props, emit);
const rules = computed(() => {
    return (field.value || []).reduce<FormRules>((acc, item) => {
        acc[item.field] = item.rules as FormRules[string];
        return acc;
    }, {} as Record<string, FormRules[string]>);
});
defineExpose({
    form: formRef,
    validate: async () => {
        await formRef.value?.validate(async (errors: any) => {
            if (errors) {
                window.$message.error(errors[0]?.[0]?.message);
                return false;
            } else {
                return true;
            }
        });
    }
});
</script>
<style scoped lang="less"></style>
