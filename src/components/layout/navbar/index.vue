<template>
    <div class="vaw-nav-bar-wrapper" :class="{ 'nav-bar-ttb': layoutMode === 'ttb' }">
        <div class="nav-bar-logo">
            <transition name="logo">
                <Logo v-if="showLogo" />
            </transition>
        </div>
        <div class="nav-container" :class="{ 'nav-container-ttb': layoutMode === 'ttb' }">
            <Breadcrumb v-if="appConfig.deviceType !== 'mobile' && appConfig.layoutMode !== 'ttb'" />
            <HorizontalScrollerMenu v-else />
        </div>
        <div v-if="appConfig.deviceType !== 'mobile'" class="right-wrapper">
            <Actions />
        </div>
        <div class="avatar-wrapper">
            <Avatar />
        </div>
    </div>
</template>

<script lang="ts" setup>
import Breadcrumb from '@/components/layout/breadcrumb/index.vue';
import Actions from '@/components/layout/actions/index.vue';
import Avatar from '@/components/layout/avatar/index.vue';
import useAppConfigStore from '@/store/modules/app-config';
import HorizontalScrollerMenu from '@/components/layout/side-bar/components/horizontal-scroller-menu.vue';
const appConfig = useAppConfigStore();
const layoutMode = computed(() => {
    return appConfig.getLayoutMode;
});

withDefaults(
    defineProps<{
        showLogo?: boolean;
    }>(),
    {
        showLogo: true
    }
);
</script>

<style scoped lang="less">
.vaw-nav-bar-wrapper {
    height: var(--logo-height);
    max-height: var(--logo-height);
    min-height: var(--logo-height);
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);

    .nav-container {
        flex: 1;
        overflow: hidden;
        padding-left: 10px;
    }
    .nav-container-ttb {
        height: 100% !important;
    }

    .avatar-wrapper {
        padding-right: 15px;
    }

    .right-wrapper {
        height: 100%;
    }

    .nav-bar-logo {
        padding-left: 18px;
    }
}
.nav-bar-ttb {
    color: #fff;
    background: linear-gradient(269deg, #4a9bff -1%, #005eff 69%);
}
</style>
