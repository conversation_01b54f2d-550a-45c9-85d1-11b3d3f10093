<template>
    <div class="horizontal-menu-wrapper">
        <div class="arrow left" v-if="showLeftArrow" @click="scrollLeft">
            <svg-icon name="svgs-arrow-left" size="16" color="#fff" />
        </div>
        <n-scrollbar
            ref="scrollbarRef"
            class="horizontal-scroller-menu"
            :class="{ 'zy-scroller-menu': layoutMode === 'ttb' }"
            :x-scrollable="true"
        >
            <n-menu
                mode="horizontal"
                :value="defaultPath"
                :options="menuOptions"
                :default-value="defaultPath"
                @update:value="onMenuClick"
            />
        </n-scrollbar>
        <div class="arrow right" v-if="showRightArrow" @click="scrollRight">
            <svg-icon name="svgs-arrow-right" size="16" color="#fff" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, ref, computed, onMounted, nextTick, watchEffect, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import useStore from '@/store/modules/main';
import { NIcon } from 'naive-ui';
import SvgIcon from '@/components/layout/svg-icon/index.vue';
import { SystemRouteRow } from '@/typings';
import config from '@/config/config';
import configHooks from '@/config/config-hooks';
import useAppConfigStore from '@/store/modules/app-config';

const router = useRouter();
const route = useRoute();
const store = useStore();
const appConfig = useAppConfigStore();

const layoutMode = computed(() => {
    return appConfig.getLayoutMode;
});

const defaultPath = computed(() => route.meta.breadcrumbs?.[0].name) as unknown as string;
const menuOptions: any = store.routes
    .filter((v) => configHooks.layout.filterNav(v))
    .map((route) => ({
        key: route.name,
        label: route.meta?.title || route.name,
        info: route,
        icon:
            config.router.needSideMenuIcon && route?.meta?.icon
                ? () =>
                      h(NIcon, null, {
                          default: () =>
                              h(SvgIcon, {
                                  prefix: 'icon',
                                  name: route?.meta?.icon as string
                              })
                      })
                : null
    }));
const onMenuClick: any = (key: string, row: SystemRouteRow) => {
    router.push({ name: row.info.name });
};

/**
 * 滚动菜单箭头
 */
const scrollbarRef = ref();
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
let scrollContentEl: HTMLElement | null = null;

// 更新箭头显示逻辑
const updateArrows = () => {
    if (!scrollContentEl) return;
    showLeftArrow.value = scrollContentEl.scrollLeft > 0;
    showRightArrow.value = scrollContentEl.scrollLeft + scrollContentEl.clientWidth < scrollContentEl.scrollWidth;
};

// 左右滚动
const scrollLeft = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: -100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};
const scrollRight = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: 100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};

// 初始化和事件监听
onMounted(() => {
    nextTick(() => {
        scrollContentEl = scrollbarRef.value?.scrollbarInstRef?.containerRef;
        if (scrollContentEl) {
            scrollContentEl.addEventListener('scroll', updateArrows);
        }
        updateArrows();
    });
    window.addEventListener('resize', updateArrows);
});

// 监听菜单数据变化
watchEffect(() => {
    nextTick(() => {
        updateArrows();
        setTimeout(updateArrows, 200);
    });
});

onBeforeUnmount(() => {
    if (scrollContentEl) {
        scrollContentEl.removeEventListener('scroll', updateArrows);
    }
    window.removeEventListener('resize', updateArrows);
});
</script>

<style lang="less" scoped>
.horizontal-menu-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 30px;

    .arrow {
        width: 30px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        cursor: pointer;
        position: absolute;

        &.left {
            left: 0;
        }
        &.right {
            right: 0;
        }
    }
}
</style>
