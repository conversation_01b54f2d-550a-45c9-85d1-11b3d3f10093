<template>
    <div class="job-switching flex">
        <n-popselect
            @update:value="valueChange"
            v-model:value="selectedValue"
            :options="options"
            :render-label="renderLabel"
            trigger="click"
        >
            <div
                class="flex justify-center items-center bg-#ffffff30 b-rd-3px py-5px px-10px text-14px w-110px cursor-pointer"
            >
                <span class="flex-1 text-center text-ellipsis whitespace-nowrap overflow-hidden">
                    {{ selectedLabel }}
                </span>
                <n-icon size="16" class="flex-shrink-0">
                    <KeyboardArrowDownFilled />
                </n-icon>
            </div>
        </n-popselect>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue';
import { NEllipsis, SelectGroupOption, SelectOption } from 'naive-ui';
import { setRoutes } from '@/router/set-routes';
import { useRouter } from 'vue-router';
import useStore from '@/store/modules/main';
import { KeyboardArrowDownFilled } from '@vicons/material';

const store = useStore();
const router = useRouter();
const options = ref<SelectOption[]>([]);
const selectedValue = ref(store.userInfo.currentOrganization || '');

const selectedLabel = computed(() => {
    const found = options.value.find((v: any) => v.value === selectedValue.value);
    return found ? found.label : options.value[0]?.label || '暂未分配';
});

const init = async () => {
    const resData = await api.sass.api.v1.user.get_group_companies();
    if (resData.data && resData.data && resData.data.length > 0) {
        options.value = resData.data;
    } else {
        options.value = [{ label: '暂未分配', value: 'not_yet_assigned' }];
        selectedValue.value = 'not_yet_assigned';
    }
};

const renderLabel = (option: SelectOption | SelectGroupOption) => {
    return h(NEllipsis, { tooltip: { placement: 'left' }, style: { width: '150px' } }, () =>
        h('span', {}, { default: () => option.label })
    );
};

const valueChange = async (value: string) => {
    // 已选中在次选中不在调用切换逻辑
    if (value === selectedValue.value) return;
    const res = await api.sass.api.v1.user.update_organization({ organizationId: value });
    await store.setToken(res.data.accessToken);
    const userInfo = await api.sass.api.v1.user.info();
    await store.setUserInfo(userInfo.data);
    await setRoutes();
    await router.push('/');
};

onMounted(() => {
    init();
});
</script>

<style lang="less" scoped></style>
