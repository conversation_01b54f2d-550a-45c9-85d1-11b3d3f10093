<template>
    <n-space align="center" :wrap-item="false" class="h-100% action-items-wrapper">
        <!-- 组织架构切换 -->
        <job-switching v-if="baseConfig.hasOrganizationChange" />

        <!-- 租户切换 -->
        <n-popselect
            v-model:value="selectedValue"
            :options="options"
            trigger="click"
            @update:value="select"
            v-if="baseConfig.hasUnitChange"
        >
            <div
                class="flex justify-center items-center bg-#ffffff30 b-rd-3px py-5px px-10px text-14px w-110px cursor-pointer"
            >
                <span class="flex-1 text-center text-ellipsis whitespace-nowrap overflow-hidden">
                    {{ selectedLabel }}
                </span>
                <n-icon size="16" class="flex-shrink-0">
                    <KeyboardArrowDownFilled />
                </n-icon>
            </div>
        </n-popselect>

        <!-- 刷新 -->
        <span
            v-if="appConfig.actionBar.isShowRefresh"
            :class="[layoutMode !== 'ttb' ? 'action-item' : 'action-item-ttb']"
            @click="refreshRoute"
        >
            <n-icon size="18">
                <refresh-outline />
            </n-icon>
        </span>

        <!-- 全屏 -->
        <span
            v-if="appConfig.actionBar.isShowFullScreen"
            :class="[layoutMode !== 'ttb' ? 'action-item' : 'action-item-ttb']"
            @click="screenFull"
        >
            <n-icon size="18">
                <contract v-if="isFullscreen" />
                <expand v-else />
            </n-icon>
        </span>

        <!-- 通知 -->
        <notice
            v-if="appConfig.actionBar.isShowNotice"
            :class="[layoutMode !== 'ttb' ? 'action-item' : 'action-item-ttb']"
        />
    </n-space>
</template>

<script lang="ts" setup>
import { useMessage } from 'naive-ui';
import screenfull from 'screenfull';
import useAppConfigStore from '@/store/modules/app-config';
import { RefreshOutline, Expand, Contract } from '@vicons/ionicons5';
import { useRouter } from 'vue-router';
import { setRoutes } from '@/router/set-routes';
import useStore from '@/store/modules/main';
import baseConfig from '@/config/base';
import jobSwitching from '@/components/layout/actions/job-switching.vue';
import { KeyboardArrowDownFilled } from '@vicons/material';

const layoutMode = computed(() => {
    return appConfig.getLayoutMode;
});

const store = useStore();
const router = useRouter();
const route = useRoute();
const message = useMessage();

const selectedValue = ref();
const options = ref<any[]>([]);

const appConfig = useAppConfigStore();

// 获取用户信息
const getUserInfo = async () => {
    const res = await window.api.sass.api.v1.user.info();
    selectedValue.value = res.data.DefaultTenantId ?? null;
    if (res.data.tenants) {
        options.value = res.data.tenants.map((v: { name: string; id: string }) => ({
            label: v.name,
            value: v.id
        }));
    }
};
const selectedLabel = computed(() =>
    selectedValue.value ? options.value.find((v: any) => v.value === selectedValue.value)?.label : '默认单位'
);

// 切换租户
const select = async () => {
    const res = await $apis.stargate.api.v1.auth.switching_organization({
        organizationId: store.userInfo?.currentOrganization || '',
        tenantId: selectedValue.value
    });
    await store.setToken(res.data.accessToken);
    const userInfo = await api.sass.api.v1.user.info();
    await store.setUserInfo(userInfo.data);
    await setRoutes();
    await router.push('/');
};

// 刷新当前页面
const refreshRoute = () => {
    router.replace({
        path: '/redirect',
        query: { url: route.path, params: JSON.stringify(route.query) }
    });
};

// 全屏
const isFullscreen = ref(false);
const screenFull = () => {
    if (!screenfull.isEnabled) {
        message.error('当前浏览器不支持全屏操作');
        return false;
    }
    isFullscreen.value = !screenfull.isFullscreen;
    screenfull.toggle();
};

// 获取接口加密状态
const getApiEncryptStatus = async () => {
    const res = await $apis.security.encrypt.status();
    store.setApiEncryptStatus(res.data?.openRequestDecrypt || false);
};

onMounted(() => {
    if (baseConfig.hasUnitChange) {
        getUserInfo();
    }
    getApiEncryptStatus();
});
</script>

<style lang="less" scoped>
.action-items-wrapper {
    z-index: 1;
    padding: 0 15px;

    .action-item {
        display: flex;
        align-items: center;

        &:hover {
            cursor: pointer;
            color: var(--primary-color-hover);
        }
    }
    .action-item-ttb {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
}
</style>
