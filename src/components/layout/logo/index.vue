<template>
    <div class="logo-wrapper" :class="{ 'logo-wrapper-ttb': layoutMode === 'ttb' }">
        <img v-if="showLogo" class="logo-img" :src="logoImg" />
        <div v-if="showTitle" :class="[!appConfig.isCollapse || alwaysShow ? 'show-title' : 'close-title']">
            <span class="logo-title">{{ base.title }}</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import useAppConfigStore from '@/store/modules/app-config';
import base from '@/config/base';
import logo from '@/assets/images/logo.webp';
import logo_white from '@/assets/images/logo_white.webp';

const layoutMode = computed(() => {
    return appConfig.getLayoutMode;
});
const logoImg = computed(() => {
    return layoutMode.value === 'ttb' ? logo_white : logo;
});

withDefaults(
    defineProps<{
        showTitle?: boolean;
        showLogo?: boolean;
        alwaysShow?: boolean;
    }>(),
    {
        showTitle: true,
        showLogo: true,
        alwaysShow: false
    }
);
const appConfig = useAppConfigStore();
</script>

<style lang="less" scoped>
.logo-wrapper {
    height: var(--logo-height);
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px dashed var(--border-color);

    .logo-img {
        width: 28px;
        margin-right: 5px;
        margin-bottom: 3px;
    }

    .logo-title {
        font-weight: bold;
        font-size: 20px;
        font-family: Source Han Sans;
        margin-left: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .show-title {
        transform: scale(1);
        width: auto;
        transition: transform 0.2s ease-in;
    }

    .close-title {
        transform: scale(0);
        width: 0;
    }
}
.logo-wrapper-ttb {
    background-color: #005eff;
    color: #fff;
}
</style>
