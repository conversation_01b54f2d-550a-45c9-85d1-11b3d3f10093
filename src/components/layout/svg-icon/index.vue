<template>
    <component
        :is="component"
        :class="className"
        aria-hidden="true"
        :style="{
            color: color,
            fill: color,
            fontSize: `${size}${typeof size === 'number' ? 'px' : ''}`
        }"
    >
        <use :href="iconName" />
    </component>
</template>

<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        prefix?: string;
        color?: string;
        size?: string | number;
        name: string;
        currentColor?: boolean;
    }>(),
    {
        prefix: 'icon',
        name: '',
        currentColor: false
    }
);

const component = computed(() => (props.prefix === 'icon' ? 'svg' : 'i'));
const iconName = computed(() => `#${props.prefix}-${props.name}`);
const className = computed(() => {
    let _className = ``;
    if (props.currentColor) {
        _className += ' currentColor ';
    }
    if (props.prefix === 'icon') {
        _className += 'svg-icon';
    } else if (props.prefix === 'iconfont') {
        _className += 'iconfont ' + props.name;
    }
    return _className;
});
</script>

<style scoped lang="less">
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;

    &:hover {
        fill: var(--primary-color-hover);
    }

    &.currentColor {
        &:hover {
            fill: currentColor;
        }
    }
}

.svg-external-icon {
    background-color: currentColor;
    mask-size: cover !important;
    display: inline-block;
}
</style>
