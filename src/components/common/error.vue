<template>
    <n-result status="error" :title="error ?? '页面加载错误'">
        <div class="text-#f00 text-center">{{ $route.query.message }}</div>
        <template #footer>
            <n-space justify="center">
                <n-button @click="goHome">返回首页</n-button>
                <n-button @click="goLogin">重新登录</n-button>
            </n-space>
        </template>
    </n-result>
</template>

<script lang="ts" setup>
defineProps<{
    error?: string;
}>();

const router = useRouter();
const goHome = () => {
    localStorage.clear();
    sessionStorage.clear();
    router.replace('/');
};
const goLogin = () => router.replace('/login');
</script>

<style lang="less" scoped>
.n-result {
    margin-top: 20vh;
}
</style>
