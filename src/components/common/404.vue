<template>
    <n-result status="404" :title="error ?? '404 NOT FOUND'" description="这里空空如也">
        <template #footer>
            <n-space justify="center">
                <n-button @click="goHome">返回首页</n-button>
                <n-button @click="goLogin">重新登录</n-button>
            </n-space>
        </template>
    </n-result>
</template>

<script lang="ts" setup>
defineProps<{
    error?: string;
}>();

const router = useRouter();
const goHome = () => router.replace('/');
const goLogin = () => router.replace('/login');
</script>

<style lang="less" scoped>
.n-result {
    margin-top: 20vh;
}
</style>
