import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import config from '@/config/config';
import baseConfig from '@/config/base';
import { LoginUserInfo } from '@/typings';

const storage = config.router.session ? sessionStorage : localStorage;
const useStore = defineStore('main', {
    state: () => {
        return {
            userInfo: {} as Partial<LoginUserInfo>,
            /**
             * 请求相关
             */
            token: '',
            requests: new Set() as Set<Promise<any>>,
            /**
             * 有权限的路由
             */
            routes: [] as RouteRecordRaw[],
            flatRoutes: [] as RouteRecordRaw[],
            permissions: [] as string[],
            apiEncryptStatus: false
        };
    },
    getters: {
        loading(state) {
            return state.requests.size > 0;
        }
    },
    actions: {
        setToken(token = '') {
            return new Promise<void>((resolve) => {
                this.token = token;
                storage.setItem(baseConfig.unique + 'token', token);
                resolve();
            });
        },
        async setUserInfo(userInfo: LoginUserInfo | null) {
            if (!userInfo) {
                this.userInfo = {};
                storage.removeItem(baseConfig.unique + 'userInfo');
            } else {
                this.userInfo = userInfo;
                // 获取当前组织类型
                const organizationType = await window.api.sass.api.v1.organization.get(userInfo.currentOrganization);
                this.userInfo.organizationType = organizationType.data.nodeType;
                nextTick(() => {
                    storage.setItem(baseConfig.unique + 'userInfo', JSON.stringify(userInfo));
                });
            }
        },
        removeUserInfoAvatar() {
            if (this.userInfo.avatar) this.userInfo.avatar.url = '';
        },
        setPermissions(route: RouteLocationNormalized) {
            const _permissions = route.meta.permissions as any[];
            if (!_permissions || _permissions.length === 0) return;
            else this.permissions = _permissions.map((v: any) => v.code);
        },
        setApiEncryptStatus(status = false) {
            return new Promise<void>((resolve) => {
                this.apiEncryptStatus = status;
                storage.setItem(baseConfig.unique + 'apiEncryptStatus', JSON.stringify(status));
                resolve();
            });
        }
    }
});

export default useStore;
