export default {
    start(data: StartRequest) {
        return request({
            url: '/saas/api/v1/workflow/start',
            method: 'post',
            data
        });
    },
    approve(data: ApproveRequest) {
        return request({
            url: '/saas/api/v1/workflow/approve',
            method: 'post',
            data
        });
    },
    ccRead(id: string) {
        return request({
            url: '/saas/api/v1/workflow/cc/read',
            method: 'post',
            data: {
                ccIds: [id]
            }
        });
    },
    detail(flowId: string) {
        return request({
            url: '/saas/api/v1/workflow',
            method: 'get',
            params: {
                flowId
            }
        });
    },
    reject(flowId: string) {
        return request({
            url: '/saas/api/v1/workflow/cancel',
            method: 'post',
            data: { flowId }
        });
    }
};

export interface StartRequest {
    /**
     * 业务id
     */
    businessId: string;
    /**
     * 部门 抄送人ids
     */
    ccIds?: string[];
    /**
     * 发起部门 id
     */
    departmentId?: string;
    /**
     * 模板id
     */
    flowVersionId: string;
    /**
     * 表单信息
     */
    formContent: string;
    /**
     * 节点id
     */
    nodes?: ApproveNode[];
    [property: string]: any;
}

export interface ApproveNode {
    /**
     * 抄送人 id
     */
    ccIds?: string[];
    /**
     * 节点id
     */
    nodeId?: string;
    /**
     * 审批人 id
     */
    approverIds?: string[];
    [property: string]: any;
}

export interface ApproveRequest {
    /**
     * 审批建议
     */
    comment: string;
    /**
     * 审批状态，pass-通过, reject-拒绝
     */
    status: string;
    /**
     * 任务id
     */
    taskId: string;
    [property: string]: any;
}
