import { TableListParams } from '@/api/typing';

export default {
    list(params: TableListParams) {
        return request<TemplatesList>({
            url: '/saas/api/v1/workflow/templates',
            method: 'get',
            params
        });
    },
    get(params: Record<string, any>) {
        return request({
            url: `/saas/api/v1/workflow/template`,
            method: 'get',
            params
        });
    },
    update(data: any) {
        return request({
            url: `/saas/api/v1/workflow/template/update`,
            method: 'post',
            data
        });
    },
    version(params: TableListParams) {
        return request({
            url: `/saas/api/v1/workflow/template/versions`,
            method: 'get',
            params
        });
    },
    enable(data: Record<string, any>) {
        return request({
            url: '/saas/api/v1/workflow/template/version/enable',
            method: 'post',
            data
        });
    }
};

export interface TemplatesList {
    /**
     * 识别号
     */
    businessId: string;
    /**
     * 创建时间
     */
    createdAt: number;
    /**
     * 创建人
     */
    createdBy: string;
    /**
     * 模板id
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 更新时间
     */
    updatedAt: number;
    /**
     * 更新人
     */
    updatedBy: string;
    /**
     * 版本号
     */
    version: string;
    /**
     * 版本id
     */
    versionId: string;
    [property: string]: any;
}

export interface VersionRecordList {
    /**
     * 发布者
     */
    createdBy: string;
    /**
     * 记录
     */
    id: string;
    /**
     * 开启状态
     */
    s: boolean;
    /**
     * 状态
     */
    status: boolean;
    /**
     * 版本号
     */
    versionNo: number;
    [property: string]: any;
}
