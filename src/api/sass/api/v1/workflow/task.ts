import { TableListParams } from '@/api/typing';

export default {
    list: {
        todos(params: TableListParams) {
            return request({
                url: '/saas/api/v1/workflow/task/todos',
                method: 'get',
                params: {
                    ...preprocessor(params, {
                        taskCreatedTime: ['taskCreatedTimeBegin', 'taskCreatedTimeEnd'],
                        flowCreatedTime: ['flowCreatedTimeBegin', 'flowCreatedTimeEnd']
                    })
                }
            });
        },
        done(params: TableListParams) {
            return request({
                url: '/saas/api/v1/workflow/task/done',
                method: 'get',
                params: {
                    ...preprocessor(params, {
                        taskCreatedTime: ['taskCreatedTimeBegin', 'taskCreatedTimeEnd'],
                        flowCreatedTime: ['flowCreatedTimeBegin', 'flowCreatedTimeEnd']
                    })
                }
            });
        },
        initiates(params: TableListParams) {
            return request({
                url: '/saas/api/v1/workflow/initiates',
                method: 'get',
                params: {
                    ...preprocessor(params, {
                        taskCreatedTime: ['taskCreatedTimeBegin', 'taskCreatedTimeEnd'],
                        flowCreatedTime: ['flowCreatedTimeBegin', 'flowCreatedTimeEnd']
                    })
                }
            });
        },
        cs(params: TableListParams) {
            return request({
                url: '/saas/api/v1/workflow/task/ccs',
                method: 'get',
                params: {
                    ...preprocessor(params, {
                        taskCreatedTime: ['taskCreatedTimeBegin', 'taskCreatedTimeEnd'],
                        flowCreatedTime: ['flowCreatedTimeBegin', 'flowCreatedTimeEnd']
                    })
                }
            });
        }
    }
};

export interface WorkflowTaskList {
    /**
     * 当前环节名称
     */
    currentNodeName: string;
    /**
     * 流程发起时间
     */
    flowCreatedTime: number;
    /**
     * 发起人
     */
    flowCreatedUserNickname: string;
    /**
     * 流程 id
     */
    flowId: string;
    /**
     * 流程名称
     */
    flowName: string;
    /**
     * 节点id
     */
    nodeId: string;
    /**
     * 组织名称
     */
    organizationName: string;
    /**
     * 任务到达时间
     */
    taskCreatedTime: number;
    /**
     * 任务id
     */
    taskId: string;
    [property: string]: any;
}
