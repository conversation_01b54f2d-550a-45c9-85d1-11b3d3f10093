import { TableListParams } from '@/api/typing';

export default {
    approver_update(data: ApproverUpdateRequest) {
        return request({
            url: '/saas/api/v1/workflow/monitor/node/approver/update',
            method: 'post',
            data
        });
    },
    approval_add(data: ApproveAddRequest) {
        return request({
            url: '/saas/api/v1/workflow/monitor/node/approver/add',
            method: 'post',
            data
        });
    },
    list(params: TableListParams) {
        return request({
            url: '/saas/api/v1/workflow/monitor/list',
            method: 'get',
            params: {
                ...preprocessor(params, { flowCreatedTime: ['flowCreatedTimeBegin', 'flowCreatedTimeEnd'] })
            }
        });
    }
};

export interface ApproverUpdateRequest {
    /**
     * 新审批人 id
     */
    approverId: string;
    /**
     * 节点id
     */
    taskId: string;
    [property: string]: any;
}

export interface ApproveAddRequest {
    /**
     * 审批人 id
     */
    approverId: string;
    /**
     * 任务 id
     */
    nodeId: string;
    [property: string]: any;
}
