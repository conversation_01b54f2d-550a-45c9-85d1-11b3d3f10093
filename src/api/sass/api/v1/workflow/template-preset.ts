import { TableListParams } from '@/api/typing';

export default {
    list(params: TableListParams) {
        return request({
            url: '/saas/api/v1/workflow/template-presets',
            method: 'get',
            params
        });
    },
    save(data: SaveForm) {
        return request({
            url: '/saas/api/v1/workflow/template-preset/save',
            method: 'post',
            data
        });
    },
    bind(data: BindForm) {
        return request({
            url: '/saas/api/v1/workflow/template-preset/bind',
            method: 'post',
            data
        });
    },
    get(id: string) {
        return request({
            url: `/saas/api/v1/workflow/template-preset`,
            method: 'get',
            params: {
                id
            }
        });
    }
};

export interface SaveForm {
    /**
     * 业务id
     */
    businessId: string;
    /**
     * 流程id，修改时必传
     */
    id?: string;
    /**
     * 审批形式，depart,group
     */
    kind: string;
    /**
     * 名称
     */
    name: string;
    [property: string]: any;
}

export interface BindForm {
    /**
     * 流程id，修改时必传
     */
    id?: string;
    /**
     * 组织 ids
     */
    organizationIds: string[];
    [property: string]: any;
}
