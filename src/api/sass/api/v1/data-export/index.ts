export default {
    list(params: DataExport) {
        return request({
            url: '/saas/api/v1/data_export/list',
            method: 'get',
            params
        });
    },
    downloadStatus(id: string) {
        return request({
            url: '/saas/api/v1/data_export/update/by/id',
            method: 'post',
            data: { id }
        });
    }
};
export interface DataExport {
    page?: string;
    pageSize?: string;
    noPage?: boolean;
    fileName?: string;
    moduleName?: string;
    status?: number;
}

export interface StatusOption {
    label: string;
    value: number;
    type: 'info' | 'success' | 'warning' | 'error';
}
