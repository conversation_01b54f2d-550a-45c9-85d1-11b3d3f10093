import { TableListParams } from '@/api/typing';

export default {
    create(data: OrganizationUserForm) {
        return request({
            url: '/saas/api/v1/organization_user/create',
            method: 'post',
            data
        });
    },
    delete(ids: string[], organizationId: string) {
        return request({
            url: '/saas/api/v1/organization_user/delete',
            method: 'post',
            data: { ids, organizationId }
        });
    },
    update(data: OrganizationUserListData) {
        return request({
            url: '/saas/api/v1/organization_user/update',
            method: 'post',
            data: preprocessor(data, {
                DefaultTenantId: () => void 0,
                avatar: () => void 0,
                Extra: () => void 0
            })
        });
    },
    list(params: TableListParams & { organizationId: string }) {
        return request({
            url: '/saas/api/v1/organization_user/list',
            method: 'get',
            params
        });
    },
    allList(params: TableListParams & { organizationId: string }) {
        return request({
            url: '/saas/api/v1/organization_user/level/all/user/list',
            method: 'get',
            params
        });
    },
    get(id: string) {
        return request({
            url: `/saas/api/v1/organization_user/${id}`,
            method: 'get'
        });
    },
    addLeaderAdmin(data: addLeaderAdminForm) {
        return request({
            url: `/saas/api/v1/organization_user/leader-admin/add`,
            method: 'post',
            data
        });
    },
    groupAllList() {
        return request({
            url: `/saas/api/v1/organization_user/group/all/users`,
            method: 'get'
        });
    },
    companyList() {
        return request({
            url: '/saas/api/v1/organization_user/company/all/users',
            method: 'get'
        });
    },
    groupList() {
        return request({
            url: '/saas/api/v1/organization_user/group/users',
            method: 'get'
        });
    },
    getDepartByUser() {
        return request({
            url: '/saas/api/v1/organization_user/user/belong/department',
            method: 'get'
        });
    },
    userDragSort(data: userDragSortForm) {
        return request({
            url: '/saas/api/v1/organization_user/user/drag/sort',
            method: 'post',
            data
        });
    }
};

export interface OrganizationUserForm {
    avatar: string;
    email?: string;
    mobile: string;
    nickname: string;
    organizationId: string;
    password: string;
    sort?: number;
    status?: boolean;
    username?: string;
}

export interface addLeaderAdminForm {
    organizationId: string;
    leaderIds: string[];
    adminIds: string[];
}

export interface OrganizationUserListData extends OrganizationUserForm {
    id?: string | undefined;
}

export interface userDragSortForm {
    organizationId: string;
    userId: string;
    targetId: string;
    position: string;
}
