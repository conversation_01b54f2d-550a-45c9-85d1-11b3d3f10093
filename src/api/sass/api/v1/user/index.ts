import { TableListParams } from '@/api/typing';

export default {
    info() {
        return request({
            url: '/saas/api/v1/user/info',
            method: 'get'
        });
    },
    list(params: TableListParams & { ignoreTenant?: boolean; kind?: string }) {
        return request({
            url: '/saas/api/v1/user/list',
            method: 'get',
            params
        });
    },
    get(id: string) {
        return request({
            url: `/saas/api/v1/user/${id}`,
            method: 'get'
        });
    },
    create(data: UserForm) {
        return request({
            url: '/saas/api/v1/user/create',
            method: 'post',
            data
        });
    },
    delete(data: Record<string, any>) {
        return request({
            url: '/saas/api/v1/user/delete',
            method: 'post',
            data
        });
    },
    update(data: UserListData) {
        return request({
            url: '/saas/api/v1/user/update',
            method: 'post',
            data: preprocessor(data, {
                DefaultTenantId: () => void 0,
                avatar: () => void 0
            })
        });
    },
    update_users_password(data: UserUpdatePassword) {
        return request({
            url: '/saas/api/v1/user/update_users_password',
            method: 'post',
            data
        });
    },
    get_group_companies() {
        return request({
            url: '/saas/api/v1/organization_user/group-companies',
            method: 'get'
        });
    },
    update_organization(data: UpdateOrganization) {
        return request({
            url: '/stargate/api/v1/auth/switching/organization',
            method: 'post',
            data
        });
    }
};

export interface UserForm {
    avatar:
        | [
              {
                  id: string;
                  url: string;
                  name?: string;
                  status: string;
                  response?: any;
              }
          ]
        | any;
    kind?: string;
    id?: string;
    avatarId?: string;
    nickname: string;
    name?: string;
    positionIds?: Array<string> | null;
    username: string;
    status?: boolean;
    password: string;
    mobile: string;
    // email?: string;
}

export interface UserListData extends UserForm {
    DefaultTenantId: string;
    id: string;
    label: string;
    value: string;
    disabled?: boolean;
    organizationUserPosition?: Array<{ positionId: string; positionName: string }> | null;
    [key: string]: any;
}
export interface UserUpdatePassword {
    newPass: string;
    operatorPW: string;
    userIds: string[];
}
export interface UpdateOrganization {
    organizationId: string;
}
