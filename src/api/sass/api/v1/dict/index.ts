import { TableListParams } from '@/api/typing';

export default {
    list(params: TableListParams) {
        return request({
            url: '/saas/api/v1/dicts',
            method: 'get',
            params
        });
    },
    create(data: DictRequest) {
        return request({
            url: '/saas/api/v1/dict/create',
            method: 'post',
            data
        });
    },
    delete(id: string) {
        return request({
            url: `/saas/api/v1/dict/delete`,
            method: 'post',
            data: {
                id
            }
        });
    },
    update(data: DictRequest) {
        return request({
            url: '/saas/api/v1/dict/update',
            method: 'post',
            data
        });
    },
    get(dictType: string) {
        return request({
            url: `/saas/api/v1/dict/type/list`,
            method: 'get',
            params: {
                noPage: true,
                dictType
            }
        });
    },
    helpList() {
        return request({
            url: '/saas/api/v1/more-help/file/list',
            method: 'get'
        });
    },
    addFileList(fileId: string) {
        return request({
            url: '/saas/api/v1/more-help/file/create',
            method: 'post',
            data: { fileId }
        });
    },
    updateFileList(id: string, fileId: string) {
        return request({
            url: '/saas/api/v1/more-help/file/update',
            method: 'post',
            data: { id, fileId }
        });
    },
    deleteFileList(id: string) {
        return request({
            url: '/saas/api/v1/more-help/file/delete',
            method: 'post',
            data: { id }
        });
    },
    sortFileList(id: string, targetId: string, position: string) {
        return request({
            url: '/saas/api/v1/more-help/file/drag/sort',
            method: 'post',
            data: { id, targetId, position }
        });
    }
};

export interface DictRequest {
    id?: string;
    description: string;
    dictCode: string;
    dictName: string;
    dictType: string;
    [property: string]: any;
}

export interface fileData {
    fileSize: string;
    fileName: string;
}

export interface RowVO {
    serialNumber: number | string;
    fileName: string;
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
    id: string;
    fileId: string;
    fileType: string;
}
