export default {
    login: {
        list(params: any) {
            return request({
                url: '/saas/api/v1/system-log/login/list',
                method: 'get',
                params: {
                    ...preprocessor(params, { createdAt: ['beginTime', 'endTime'] })
                }
            });
        }
    },
    operation: {
        list(params: any) {
            return request({
                url: '/saas/api/v1/system-log/operation/list',
                method: 'get',
                params: {
                    ...preprocessor(params, { createdAt: ['beginTime', 'endTime'] })
                }
            });
        }
    }
};
