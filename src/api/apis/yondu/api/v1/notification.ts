export default {
    list(params: NotificationListParams) {
        return request({
            method: 'get',
            url: '/yondu/api/v1/notifications',
            params: {
                ...preprocessor(params, {
                    dateRange: ['startTime', 'endTime']
                })
            }
        });
    },
    read(data: MarkAsReadRequest) {
        return request({
            method: 'post',
            url: `/yondu/api/v1/notification/mark-as-read`,
            data
        });
    }
};

export interface NotificationListParams {
    /**
     * 13位时间戳
     */
    endTime?: number;
    /**
     * 是否已读
     */
    isRead?: boolean;
    /**
     * 事件级别：1-普通（默认），2-预警，3-紧急
     */
    level?: number;
    /**
     * 模块名称
     */
    moduleName?: string;
    /**
     * 是否不分页
     */
    noPage?: boolean;
    /**
     * 页码，从1开始
     */
    page?: number;
    /**
     * 每页大小
     */
    pageSize?: number;
    /**
     * 接收人ID
     */
    recipientId?: string;
    /**
     * 13位时间戳
     */
    startTime?: number;
    [property: string]: any;
}

export interface YonduNotification {
    /**
     * 业务id匹配
     */
    businessId?: string;
    /**
     * 业务类型
     */
    businessType?: string;
    /**
     * 通知内容
     */
    content?: string;
    /**
     * 13位时间戳
     */
    createdAt?: number;
    /**
     * 消息唯一标识，用于排重、保证幂等性
     */
    id?: string;
    /**
     * 已读未读标识
     */
    isRead?: boolean;
    /**
     * 1-普通（默认），2-预警，3-紧急
     */
    level?: number;
    /**
     * 模块名称
     */
    moduleName?: string;
    /**
     * 组织架构id
     */
    organizationId?: string;
    /**
     * 接收人id
     */
    recipientId?: string;
    /**
     * 租户id
     */
    tenantId?: string;
    [property: string]: any;
}

/**
 * MarkAsReadRequest
 */
export interface MarkAsReadRequest {
    /**
     * 事件ID列表
     */
    ids?: string[];
    /**
     * 接收人ID
     */
    recipientId?: string;
    [property: string]: any;
}
