export default {
    img_captcha() {
        return request({
            method: 'POST',
            url: '/stargate/api/v1/auth/captcha/img/generate'
        });
    },
    login(data: LoginRequest) {
        return request({
            method: 'POST',
            url: '/stargate/api/v1/auth/login',
            data
        });
    },
    send_sms_captcha(data: SMSRequest) {
        return request({
            method: 'POST',
            url: '/stargate/api/v1/auth/captcha/send',
            data
        });
    },
    reset(data: ResetRequest) {
        return request({
            method: 'POST',
            url: '/stargate/api/v1/auth/reset-password',
            data
        });
    },
    switching_organization(data: SwitchingOrganizationRequest) {
        return request({
            method: 'POST',
            url: '/stargate/api/v1/auth/switching/organization',
            data
        });
    }
};

export interface LoginRequest {
    /**
     * 账户
     */
    account: string;
    /**
     * 验证码
     */
    captcha: string;
    /**
     * 图形验证码
     */
    imgCaptcha?: string;
    /**
     * 图形验证码id
     */
    captchaId: string;
    /**
     * 设备类型，web，app，h5
     */
    deviceKind?: string;
    /**
     * 登录类型，account_and_password,mobile_and_sms_captcha
     */
    loginKind?: string;
    /**
     * 密码
     */
    password?: string;
    [property: string]: any;
}

export interface SMSRequest {
    /**
     * 账户，手机号或者用户名
     */
    account: string;
    /**
     * 图形验证码
     */
    imgCaptcha: string;
    /**
     * 图形验证码id
     */
    imgCaptchaId: string;
    /**
     * 类型，sms_login
     */
    kind: string;
    [property: string]: any;
}

export interface ResetRequest {
    /**
     * 账户
     */
    account: string;
    /**
     * 验证码
     */
    captcha: string;
    /**
     * 密码
     */
    password?: string;
    [property: string]: any;
}

export interface SwitchingOrganizationRequest {
    /**
     * 公司id
     */
    organizationId?: string;
    /**
     * 组织id
     */
    tenantId?: string;
    [property: string]: any;
}
