import { TableListParams } from '@/api/typing';

export default {
    list(params: TableListParams) {
        return request({
            url: '/nebula/api/v1/business-dictionary/list',
            method: 'get',
            params
        });
    },
    node: {
        list(params: { dictionaryId: string; parentId?: string }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/list',
                method: 'get',
                params
            });
        },
        create(data: DictionaryNodeForm) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/create',
                method: 'post',
                data
            });
        },
        update(data: DictionaryNodeList) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/update',
                method: 'post',
                data
            });
        },
        delete(id: string) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/delete',
                method: 'post',
                data: { nodeId: id }
            });
        },
        move(data: { id: string; sort: number }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/move',
                method: 'post',
                data
            });
        },
        tree(params: { id: string }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/tree',
                method: 'get',
                params
            });
        },
        // 文档库查询是否权限
        isPermission(data: IsPermissionParams) {
            return request({
                url: '/nebula/api/v1/document-library/permission-operation',
                method: 'post',
                data
            });
        },
        // 根据文档 id 查询发发放回收用户记录
        getRecord(params: GetRecordParams) {
            return request({
                url: '/nebula/api/v1/document-library/document/distribute/user-permissions',
                method: 'get',
                params
            });
        }
    }
};

export interface DictionaryNodeForm {
    /**
     * 字典id
     */
    dictionaryId: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父id
     */
    parentId?: string;
    /**
     * 备注
     */
    remark?: string;
    [property: string]: any;
}

export interface DictionaryNodeList {
    /**
     * 节点id
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父id
     */
    parentId: string;
    /**
     * 备注
     */
    remark: string;
    /**
     * 排序序号
     */
    sort: number;
    /**
     * 是否启用
     */
    status: boolean;
    /**
     * 更新时间
     */
    updatedAt: number;
    /**
     * 更新人
     */
    updatedBy: string;
    [property: string]: any;
}
export interface IsPermissionParams {
    documentId: string,//文档id
    filePermission: number,//文件权限,1查阅 | 2查阅/下载 | 3一次下载
    fileForm: number,//文件形式,1电子文件 | 2纸质文件
    inventoryId: string,//清单id
    operationType: number,//操作类型 1-借阅 2-下载
}
export interface GetRecordParams {
    documentId: string,//文档id
    userId?: string,//用户id
    status?: string,//状态1发放审批中 | 2已发放 | 3 回收审批人
}

