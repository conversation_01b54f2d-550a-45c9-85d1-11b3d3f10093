export default {
    receiveBook(id: string, reason: string) {
        return new Promise((resolve) => {
            resolve(id + reason);
        });
        // request({
        //     url: '/saas/api/v1/book-library/receive',
        //     method: 'post',
        //     data
        // });
    },
    borrowBook(id: string, reason: string) {
        return new Promise((resolve) => {
            resolve(id + reason);
        });
        // request({
        //     url: '/saas/api/v1/book-library/borrow',
        //     method: 'post',
        //     data
        // });
    },
    getBookList(data: BookListParams) {
        return request({
            url: '/nebula/api/v1/book/list',
            method: 'post',
            data
        });
    },
    createBook(data: any) {
        return request({
            url: '/nebula/api/v1/book/create',
            method: 'post',
            data
        });
    },
    uploadBook(data: any) {
        return request({
            url: '/nebula/api/v1/book/update',
            method: 'post',
            data
        });
    },
    deleteBook(id: string) {
        return request({
            url: `/nebula/api/v1/book/delete`,
            method: 'post',
            data: {
                id
            }
        });
    },
    importBook(data: {
        dictionaryId: string;
        excelId: string;
        fileInfo: Array<{ fileId: string; fileName: string }> | null;
    }) {
        return request({
            url: '/nebula/api/v1/book/import',
            method: 'post',
            data
        });
    }
};
export interface BookRow {
    id: string;
    number?: string;
    name?: string;
    author?: string;
    publisher?: string;
    registerCount?: number;
    receiveCount?: number;
    fileId?: string;
    borrowCount?: number;
    dictionaryNodeId?: string | null;
    bookFileInfo?: any;
}
export interface BookFormData {
    number: string;
    name: string;
    author: string;
    publisher: string;
    fileId: string;
    registerCount: number | null;
    dictionaryNodeId: string | null;
    file?: any;
}
export interface BookListParams {
    number: string;
    name: string;
    author: string;
    publisher: string;
    dictionaryNodeIds: any;
    borrowStatus: string;
    page: number;
    pageSize: number;
}
