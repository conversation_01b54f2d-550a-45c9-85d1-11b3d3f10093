import { TableListParams } from '@/api/typing';

export default {
    startTask() {
        return request({
            url: '/nebula/api/v1/signature-task',
            method: 'post'
        });
    },
    getTaskStatus(taskId: string) {
        return request({
            url: '/nebula/api/v1/signature-task/status',
            method: 'get',
            params: {
                taskId
            }
        });
    },
    uploadSignature(data: UploadSignatureReq) {
        return request({
            url: '/nebula/api/v1/signature/upload',
            method: 'post',
            data
        });
    },
    getSignature() {
        return request({
            url: '/nebula/api/v1/signatures/current',
            method: 'get'
        });
    },
    getSignatureList(params: TableListParams) {
        return request({
            url: '/nebula/api/v1/signatures/history',
            method: 'get',
            params
        });
    }
};

export interface UploadSignatureReq {
    /**
     * 签名图片Base64字符串
     */
    signatureBase64: string;
    /**
     * 签名任务ID
     */
    taskId: string;
    [property: string]: any;
}

export interface CurrentSignatureInfo {
    /**
     * 审批人姓名
     */
    approverName?: string;
    /**
     * 授权书文件ID
     */
    authLetterFileId?: string;
    /**
     * 生效日期（毫秒时间戳）
     */
    effectiveDate?: number;
    /**
     * 签名记录ID
     */
    id?: string;
    /**
     * 签名图片Base64字符串
     */
    signatureBase64?: string;
    [property: string]: any;
}
