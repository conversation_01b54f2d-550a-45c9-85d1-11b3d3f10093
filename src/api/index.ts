import auth from './sass/api/v1/auth';
import user from './sass/api/v1/user';
import organization from './sass/api/v1/organization';
import organizationUserInfo from './sass/api/v1/organization-user-info';
import dataExport from './sass/api/v1/data-export';
import menu from './sass/api/v1/menu';
import button from './sass/api/v1/button';
import role from './sass/api/v1/role';
import authority from './sass/api/v1/authority';
import apiApi from './sass/api/v1/api';
import group from './sass/api/v1/group';
import groupType from './sass/api/v1/group-type';
import position from './sass/api/v1/position';
import tenant from './sass/api/v1/tenant';
import tenantUserInfo from './sass/api/v1/tenant-user-info';
import file from './file/api/v1/file';
import apis from '@/api/apis/index';
import dict from './sass/api/v1/dict';
import system from './sass/api/v1/system-log';
import online from './sass/api/v1/user/online';
import workflow from './sass/api/v1/workflow/index';
import templatePreset from './sass/api/v1/workflow/template-preset';
import templates from './sass/api/v1/workflow/templates';
import { App } from 'vue';
import { createDiscreteApi } from 'naive-ui';
const { message, dialog, notification } = createDiscreteApi(['message', 'dialog', 'notification']);
export const api = {
    sass: {
        api: {
            v1: {
                auth,
                dict,
                user: {
                    ...user,
                    online
                },
                organization,
                menu,
                button,
                role,
                authority,
                api: apiApi,
                group,
                groupType,
                organizationUserInfo,
                dataExport,
                position,
                tenant,
                tenantUserInfo,
                system,
                workflow: {
                    workflow,
                    templates,
                    templatePreset,
                    task,
                    monitor
                }
            }
        }
    },
    file: {
        api: {
            v1: {
                file
            }
        }
    }
};

declare global {
    interface Window {
        api: typeof api;
        $apis: typeof apis;
        $message: typeof message;
        $dialog: typeof dialog;
        $datas: typeof $datas;
        $alert: typeof $alert;
        $hooks: typeof $hooks;
        $$utils: typeof $utils;
        $notification: typeof notification;
    }
}

// eslint-disable-next-line vue/prefer-import-from-vue
// import '@vue/runtime-core';
import task from './sass/api/v1/workflow/task';
import monitor from './sass/api/v1/workflow/monitor';

declare module '@vue/runtime-core' {
    export interface ComponentCustomProperties {
        api: typeof api;
        $apis: typeof apis;
        $message: typeof message;
        $dialog: typeof dialog;
        $datas: typeof $datas;
        $alert: typeof $alert;
        $hooks: typeof $hooks;
        $utils: typeof $utils;
        $route: ReturnType<typeof useRoute>;
        $router: ReturnType<typeof useRouter>;
        $notification: typeof notification;
    }
}

export default {
    install(app: App<Element>) {
        app.config.globalProperties.api = api;
        app.config.globalProperties.$apis = apis;
        app.config.globalProperties.$message = message;
        app.config.globalProperties.$dialog = dialog;
        app.config.globalProperties.$datas = $datas;
        app.config.globalProperties.$alert = $alert;
        app.config.globalProperties.$hooks = $hooks;
        app.config.globalProperties.$utils = $utils;
        app.config.globalProperties.$notification = notification;

        window.api = api;
        window.$apis = apis;
        window.$message = message;
        window.$dialog = dialog;
        window.$notification = notification;
    }
};
