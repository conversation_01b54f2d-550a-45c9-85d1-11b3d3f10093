export default {
    /**
     * 生成预签名URL，获取实际上传链接，上传文件
     */
    upload(data: GenerateRequest) {
        return request({
            url: `/file/api/v1/file/upload/presignedurl/generate`,
            method: 'post',
            data
        });
    },
    /**
     * 上传成功后回调
     */
    callback(data: CallbackRequest) {
        return request({
            url: `/file/api/v1/file/upload/success/callback`,
            method: 'post',
            data
        });
    },
    /**
     * 获取下载预签名
     */
    download(data: DownloadRequest) {
        return request({
            url: `/file/api/v1/file/download/presignedurl/generate`,
            method: 'post',
            data
        });
    },
    get(id: string) {
        return request({
            url: `/file/api/v1/file/get/${id}`,
            method: 'get'
        });
    }
};

export interface GenerateRequest {
    fileName: string;
    fileSize: number;
    [property: string]: any;
}

export interface CallbackRequest {
    /**
     * ID 编号
     */
    id: string;
    [property: string]: any;
}

export interface DownloadRequest {
    /**
     * ID 编号
     */
    id: string;
    [property: string]: any;
}
