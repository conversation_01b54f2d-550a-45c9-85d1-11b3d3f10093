:root {
    --vxe-ui-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
        Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    --vxe-ui-font-size-default: 14px;
    --vxe-ui-font-size-medium: 14px;
    --vxe-ui-font-size-small: 13px;
    --vxe-ui-font-size-mini: 12px;
    --vxe-ui-border-radius: 4px;
    --vxe-ui-input-disabled-color: #dcdfe6;
    --vxe-ui-input-height-default: 34px;
    --vxe-ui-input-height-medium: 32px;
    --vxe-ui-input-height-small: 30px;
    --vxe-ui-input-height-mini: 28px;
    --vxe-ui-table-header-font-color: var(--vxe-ui-font-color);
    --vxe-ui-table-footer-font-color: var(--vxe-ui-font-color);
    --vxe-ui-table-border-radius: var(--vxe-ui-border-radius);
    --vxe-ui-table-border-width: 1.05px;
    --vxe-ui-table-resizable-line-color: #d9dddf;
    --vxe-ui-table-resizable-drag-line-color: var(--vxe-ui-font-primary-color);
    --vxe-ui-table-footer-background-color: var(--vxe-ui-layout-background-color);
    --vxe-ui-table-tree-node-line-color: #909399;
    --vxe-ui-table-tree-node-line-style: dotted;
    --vxe-ui-table-header-font-weight: 700;
    --vxe-ui-table-row-height-default: 48px;
    --vxe-ui-table-row-height-medium: 44px;
    --vxe-ui-table-row-height-small: 40px;
    --vxe-ui-table-row-height-mini: 36px;
    --vxe-ui-table-row-line-height: 22px;
    --vxe-ui-table-column-icon-border-color: #c0c4cc;
    --vxe-ui-table-column-icon-border-hover-color: #515a6e;
    --vxe-ui-table-cell-padding-default: 10px;
    --vxe-ui-table-cell-padding-medium: 8px;
    --vxe-ui-table-cell-padding-small: 6px;
    --vxe-ui-table-cell-padding-mini: 4px;
    --vxe-ui-table-cell-placeholder-color: #c0c4cc;
    --vxe-ui-table-cell-negative-color: #f56c6c;
    --vxe-ui-table-cell-input-height-default: var(--vxe-ui-table-row-height-default) - 6;
    --vxe-ui-table-cell-input-height-medium: var(--vxe-ui-table-row-height-medium) - 6;
    --vxe-ui-table-cell-input-height-small: var(--vxe-ui-table-row-height-small) - 6;
    --vxe-ui-table-cell-input-height-mini: var(--vxe-ui-table-row-height-mini) - 6;
    --vxe-ui-table-cell-dirty-width: 5px;
    --vxe-ui-table-cell-dirty-update-color: #f56c6c;
    --vxe-ui-table-cell-dirty-insert-color: #19a15f;
    --vxe-ui-table-cell-area-border-color: var(--vxe-ui-font-primary-color);
    --vxe-ui-table-cell-area-border-width: 1px;
    --vxe-ui-table-cell-area-status-border-width: var(--vxe-ui-table-cell-area-border-width);
    --vxe-ui-table-cell-main-area-extension-border-color: #fff;
    --vxe-ui-table-cell-main-area-extension-background-color: var(--vxe-ui-font-primary-color);
    --vxe-ui-table-cell-extend-area-border-width: 2px;
    --vxe-ui-table-cell-copy-area-border-width: 3px;
    --vxe-ui-table-cell-active-area-border-width: 2px;
    --vxe-ui-table-cell-active-area-background-color: transparent;
    --vxe-ui-table-cell-copy-area-border-color: var(--vxe-ui-table-cell-area-border-color);
    --vxe-ui-table-cell-extend-area-border-color: var(--vxe-ui-table-cell-area-border-color);
    --vxe-ui-table-cell-active-area-border-color: var(--vxe-ui-table-cell-area-border-color);
    --vxe-ui-table-cell-area-background-color: rgba(64, 158, 255, 0.2);
    --vxe-ui-table-header-active-area-background-color: rgba(64, 158, 255, 0.05);
    --vxe-ui-table-expand-padding-default: 16px;
    --vxe-ui-table-checkbox-range-border-width: 1px;
    --vxe-ui-table-checkbox-range-border-color: #006af1;
    --vxe-ui-table-checkbox-range-background-color: rgba(50, 128, 252, 0.2);
    --vxe-ui-table-fixed-left-scrolling-box-shadow: 8px 0px 10px -5px var(--vxe-ui-table-fixed-scrolling-box-shadow-color);
    --vxe-ui-table-fixed-right-scrolling-box-shadow: -8px 0px 10px -5px var(--vxe-ui-table-fixed-scrolling-box-shadow-color);
    --vxe-ui-table-menu-item-width: 198px;
    --vxe-ui-table-menu-background-color: #fff;
    --vxe-ui-table-validate-error-color: #f56c6c;
    --vxe-ui-table-validate-error-background-color: var(--vxe-ui-layout-background-color);
    --vxe-ui-toolbar-custom-active-background-color: #d9dadb;
}
[data-vxe-ui-theme='light'] {
    --vxe-ui-font-color: #606266;
    --vxe-ui-font-primary-color: #409eff;
    --vxe-ui-font-lighten-color: #797b80;
    --vxe-ui-font-darken-color: #47494c;
    --vxe-ui-font-disabled-color: #bfbfbf;
    --vxe-ui-base-popup-border-color: #dadce0;
    --vxe-ui-base-popup-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.16);
    --vxe-ui-layout-background-color: #fff;
    --vxe-ui-input-border-color: #dcdfe6;
    --vxe-ui-input-placeholder-color: #c0c4cc;
    --vxe-ui-input-disabled-background-color: #f3f3f3;
    --vxe-ui-loading-background-color: hsla(0, 0%, 100%, 0.5);
    --vxe-ui-table-header-background-color: #f8f8f9;
    --vxe-ui-table-column-to-row-background-color: #f8f8f9;
    --vxe-ui-table-column-hover-background-color: #d7effb;
    --vxe-ui-table-column-current-background-color: #e6f7ff;
    --vxe-ui-table-column-hover-current-background-color: #d7effb;
    --vxe-ui-table-border-color: #e8eaec;
    --vxe-ui-table-row-hover-background-color: #f5f7fa;
    --vxe-ui-table-row-striped-background-color: #fafafa;
    --vxe-ui-table-row-hover-striped-background-color: #f5f7fa;
    --vxe-ui-table-row-radio-checked-background-color: #fff3e0;
    --vxe-ui-table-row-hover-radio-checked-background-color: #ffebbc;
    --vxe-ui-table-row-checkbox-checked-background-color: #fff3e0;
    --vxe-ui-table-row-hover-checkbox-checked-background-color: #ffebbc;
    --vxe-ui-table-row-current-background-color: #e6f7ff;
    --vxe-ui-table-row-hover-current-background-color: #d7effb;
    --vxe-ui-table-fixed-scrolling-box-shadow-color: rgba(0, 0, 0, 0.12);
    --vxe-ui-table-drag-over-background-color: rgba(255, 255, 200, 0.3);
}
[data-vxe-ui-theme='dark'] {
    color-scheme: dark;
    --vxe-ui-font-color: #a0a3a7;
    --vxe-ui-font-tinge-color: #33353b;
    --vxe-ui-font-lighten-color: #797b80;
    --vxe-ui-font-darken-color: #47494c;
    --vxe-ui-font-disabled-color: #464646;
    --vxe-ui-font-primary-color: #409eff;
    --vxe-ui-base-popup-border-color: #424242;
    --vxe-ui-base-popup-box-shadow: 0px 12px 30px 8px rgba(0, 0, 0, 0.5);
    --vxe-ui-layout-background-color: #121212;
    --vxe-ui-input-border-color: #424242;
    --vxe-ui-input-placeholder-color: #8d9095;
    --vxe-ui-input-disabled-background-color: #262727;
    --vxe-ui-loading-background-color: hsla(0, 0%, 100%, 0.1);
    --vxe-ui-table-header-background-color: #28282a;
    --vxe-ui-table-column-to-row-background-color: #28282a;
    --vxe-ui-table-column-hover-background-color: #242f3b;
    --vxe-ui-table-column-current-background-color: #18222c;
    --vxe-ui-table-column-hover-current-background-color: #242f3b;
    --vxe-ui-table-border-color: #37373a;
    --vxe-ui-table-row-hover-background-color: #262727;
    --vxe-ui-table-row-striped-background-color: #1d1d1d;
    --vxe-ui-table-row-hover-striped-background-color: #262727;
    --vxe-ui-table-row-radio-checked-background-color: #604820;
    --vxe-ui-table-row-hover-radio-checked-background-color: #6e5326;
    --vxe-ui-table-row-checkbox-checked-background-color: #604820;
    --vxe-ui-table-row-hover-checkbox-checked-background-color: #6e5326;
    --vxe-ui-table-row-current-background-color: #18222c;
    --vxe-ui-table-row-hover-current-background-color: #242f3b;
    --vxe-ui-table-fixed-scrolling-box-shadow-color: rgba(0, 0, 0, 0.8);
    --vxe-ui-table-drag-over-background-color: rgba(48, 48, 0, 0.3);
}
[class*='vxe-'],
[class*='vxe-'] :after,
[class*='vxe-'] :before,
[class*='vxe-']:after,
[class*='vxe-']:before {
    box-sizing: border-box;
}
[class*='vxe-'] {
    font-variant: tabular-nums;
    font-feature-settings: 'tnum';
}
[class*='vxe-icon--'] {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    direction: ltr;
    font-family: Verdana, Arial, Tahoma;
    font-weight: 400;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
[class*='vxe-icon--'].rotate45 {
    transform: rotate(45deg);
}
[class*='vxe-icon--'].rotate90 {
    transform: rotate(90deg);
}
[class*='vxe-icon--'].rotate180 {
    transform: rotate(180deg);
}
.vxe-icon--arrow-bottom,
.vxe-icon--arrow-left,
.vxe-icon--arrow-right,
.vxe-icon--arrow-top,
.vxe-icon--calendar,
.vxe-icon--caret-bottom,
.vxe-icon--caret-left,
.vxe-icon--caret-right,
.vxe-icon--caret-top,
.vxe-icon--check,
.vxe-icon--circle-plus,
.vxe-icon--close,
.vxe-icon--d-arrow-left,
.vxe-icon--d-arrow-right,
.vxe-icon--dot,
.vxe-icon--download,
.vxe-icon--edit-outline,
.vxe-icon--error,
.vxe-icon--eye,
.vxe-icon--eye-slash,
.vxe-icon--funnel,
.vxe-icon--info,
.vxe-icon--menu,
.vxe-icon--minus,
.vxe-icon--more,
.vxe-icon--plus,
.vxe-icon--print,
.vxe-icon--question,
.vxe-icon--refresh,
.vxe-icon--remove,
.vxe-icon--search,
.vxe-icon--square,
.vxe-icon--success,
.vxe-icon--upload,
.vxe-icon--warning,
.vxe-icon--zoomin,
.vxe-icon--zoomout {
    width: 1em;
    height: 1em;
    line-height: 1em;
}
.vxe-icon--arrow-bottom:before,
.vxe-icon--arrow-left:before,
.vxe-icon--arrow-right:before,
.vxe-icon--arrow-top:before,
.vxe-icon--calendar:after,
.vxe-icon--calendar:before,
.vxe-icon--caret-bottom:before,
.vxe-icon--caret-left:before,
.vxe-icon--caret-right:before,
.vxe-icon--caret-top:before,
.vxe-icon--check:before,
.vxe-icon--circle-plus:after,
.vxe-icon--close:before,
.vxe-icon--d-arrow-left:after,
.vxe-icon--d-arrow-left:before,
.vxe-icon--d-arrow-right:after,
.vxe-icon--d-arrow-right:before,
.vxe-icon--dot:before,
.vxe-icon--download:after,
.vxe-icon--download:before,
.vxe-icon--edit-outline:after,
.vxe-icon--edit-outline:before,
.vxe-icon--error:after,
.vxe-icon--eye-slash:after,
.vxe-icon--eye-slash:before,
.vxe-icon--eye:before,
.vxe-icon--funnel:after,
.vxe-icon--funnel:before,
.vxe-icon--info:after,
.vxe-icon--minus:before,
.vxe-icon--more:before,
.vxe-icon--plus:before,
.vxe-icon--print:after,
.vxe-icon--print:before,
.vxe-icon--question:after,
.vxe-icon--refresh:after,
.vxe-icon--refresh:before,
.vxe-icon--remove:after,
.vxe-icon--search:after,
.vxe-icon--search:before,
.vxe-icon--square:before,
.vxe-icon--success:after,
.vxe-icon--upload:after,
.vxe-icon--upload:before,
.vxe-icon--warning:after,
.vxe-icon--zoomin:after,
.vxe-icon--zoomin:before,
.vxe-icon--zoomout:after,
.vxe-icon--zoomout:before {
    content: '';
    position: absolute;
}
.vxe-icon--square:before {
    left: 0.05em;
    top: 0.05em;
    width: 0.9em;
    height: 0.9em;
}
.vxe-icon--square:before,
.vxe-icon--zoomin {
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--zoomin {
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-icon--zoomin:after,
.vxe-icon--zoomin:before {
    background-color: inherit;
}
.vxe-icon--zoomin:before {
    left: -0.1em;
    top: 0.2em;
    width: 1.1em;
    height: 0.4em;
}
.vxe-icon--zoomin:after {
    top: -0.1em;
    left: 0.2em;
    width: 0.4em;
    height: 1.1em;
}
.vxe-icon--zoomout {
    position: relative;
}
.vxe-icon--zoomout:before {
    right: 0;
    top: 0;
}
.vxe-icon--zoomout:after,
.vxe-icon--zoomout:before {
    width: 0.7em;
    height: 0.7em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--zoomout:after {
    left: 0.1em;
    bottom: 0.1em;
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-icon--menu:before {
    content: '';
    display: inline-block;
    width: 0.22em;
    height: 0.22em;
    box-shadow: 0 -0.36em 0, -0.36em -0.36em 0, 0.36em -0.36em 0, inset 0 0 0 1em, -0.36em 0 0, 0.36em 0 0, 0 0.36em 0,
        -0.36em 0.36em 0, 0.36em 0.36em 0;
    margin: 0.26em;
}
.vxe-icon--caret-bottom:before,
.vxe-icon--caret-left:before,
.vxe-icon--caret-right:before,
.vxe-icon--caret-top:before {
    border-width: 0.4em;
    border-style: solid;
    border-color: transparent;
}
.vxe-icon--caret-top:before {
    left: 0.1em;
    bottom: 0.3em;
    border-bottom-color: inherit;
}
.vxe-icon--caret-bottom:before {
    left: 0.1em;
    top: 0.3em;
    border-top-color: inherit;
}
.vxe-icon--caret-left:before {
    right: 0.3em;
    bottom: 0.1em;
    border-right-color: inherit;
}
.vxe-icon--caret-right:before {
    left: 0.3em;
    bottom: 0.1em;
    border-left-color: inherit;
}
.vxe-icon--arrow-bottom:before,
.vxe-icon--arrow-left:before,
.vxe-icon--arrow-right:before,
.vxe-icon--arrow-top:before {
    top: 0.4em;
    left: 0.14em;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: inherit;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-radius: 0.15em;
    transform: rotate(-45deg);
}
.vxe-icon--arrow-bottom:before {
    top: 0;
    left: 0.14em;
    transform: rotate(135deg);
}
.vxe-icon--arrow-left:before {
    top: 0.18em;
    left: 0.35em;
    transform: rotate(-135deg);
}
.vxe-icon--arrow-right:before {
    top: 0.18em;
    left: 0;
    transform: rotate(45deg);
}
.vxe-icon--d-arrow-left:before,
.vxe-icon--d-arrow-right:before {
    left: 0.15em;
}
.vxe-icon--d-arrow-left:after,
.vxe-icon--d-arrow-right:after {
    left: 0.58em;
}
.vxe-icon--d-arrow-left:after,
.vxe-icon--d-arrow-left:before,
.vxe-icon--d-arrow-right:after,
.vxe-icon--d-arrow-right:before {
    top: 0.18em;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: inherit;
    border-radius: 0.15em;
    transform: rotate(-45deg);
}
.vxe-icon--d-arrow-right:after,
.vxe-icon--d-arrow-right:before {
    transform: rotate(135deg);
}
.vxe-icon--d-arrow-right:before {
    left: -0.25em;
}
.vxe-icon--d-arrow-right:after {
    left: 0.18em;
}
.vxe-icon--funnel:before {
    top: 0.05em;
    left: 0;
    border-width: 0.5em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
}
.vxe-icon--funnel:after {
    left: 0.41em;
    top: 0.4em;
    width: 0;
    height: 0.5em;
    border-width: 0 0.2em 0 0;
    border-style: solid;
    border-right-color: inherit;
}
.vxe-icon--edit-outline:before {
    height: 0.84em;
    width: 0.86em;
    top: 0.1em;
    left: 0.02em;
    border-radius: 0.2em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--edit-outline:after {
    left: 0.6em;
    bottom: 0.2em;
    width: 0;
    height: 0.8em;
    border-radius: 0 0 80% 80%;
    border-width: 0 0 0 0.22em;
    border-style: solid;
    border-color: inherit;
    transform: rotate(45deg);
}
.vxe-icon--more:before {
    content: '...';
    top: 0;
    left: 0.1em;
    line-height: 0.5em;
    font-weight: 700;
}
.vxe-icon--plus:before {
    content: '+';
    left: -0.12em;
    bottom: -0.1em;
    line-height: 1em;
    font-size: 1.6em;
}
.vxe-icon--check:before {
    left: 0.25em;
    bottom: 0.2em;
    width: 0.5em;
    height: 0.9em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: transparent;
    border-right-color: inherit;
    border-bottom-color: inherit;
    border-radius: 0.15em;
    border-left-color: transparent;
    transform: rotate(45deg);
}
.vxe-icon--close:before {
    content: '+';
    left: -0.1em;
    bottom: -0.16em;
    line-height: 1em;
    font-size: 1.8em;
    transform: rotate(45deg);
}
.vxe-icon--minus:before {
    content: '\2500';
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    line-height: 0.9em;
    font-size: 1.2em;
}
.vxe-icon--refresh {
    border-width: 0.1em;
    border-style: solid;
    border-radius: 50%;
    border-right-color: transparent !important;
    border-left-color: transparent !important;
}
.vxe-icon--refresh:before {
    left: 50%;
    top: 0;
    transform: translateX(50%) rotate(-45deg);
}
.vxe-icon--refresh:after {
    right: 50%;
    bottom: 0;
    transform: translateX(-50%) rotate(135deg);
}
.vxe-icon--refresh:after,
.vxe-icon--refresh:before {
    width: 0;
    height: 0;
    border-width: 0.25em;
    border-style: solid;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
}
.vxe-icon--refresh.roll {
    animation: rollCircle 1s linear infinite;
}
.vxe-icon--circle-plus:before,
.vxe-icon--error:before,
.vxe-icon--info:before,
.vxe-icon--question:before,
.vxe-icon--remove:before,
.vxe-icon--success:before,
.vxe-icon--warning:before {
    content: '';
    border-radius: 50%;
    border-width: 0.5em;
    border-style: solid;
    border-color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    transform: scale(0.95);
}
.vxe-icon--info:after,
.vxe-icon--question:after,
.vxe-icon--warning:after {
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: var(--vxe-ui-layout-background-color);
    transform: rotate(-10deg) scale(0.75);
}
.vxe-icon--question:after {
    content: '?';
}
.vxe-icon--info:after {
    content: '¡';
}
.vxe-icon--warning:after {
    content: '!';
}
.vxe-icon--success:after {
    content: '\2713';
    left: 0.25em;
    bottom: 0;
    color: var(--vxe-ui-layout-background-color);
    font-size: 0.65em;
}
.vxe-icon--circle-plus:after {
    content: '+';
    line-height: 1.4em;
    font-size: 0.8em;
}
.vxe-icon--circle-plus:after,
.vxe-icon--remove:after {
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: var(--vxe-ui-layout-background-color);
}
.vxe-icon--remove:after {
    content: '\2500';
    line-height: 1.5em;
    font-size: 0.7em;
}
.vxe-icon--error:after {
    content: '×';
    left: 0;
    bottom: 0;
    width: 100%;
    line-height: 1.4em;
    text-align: center;
    color: var(--vxe-ui-layout-background-color);
    font-size: 0.8em;
}
.vxe-icon--download,
.vxe-icon--upload {
    overflow: hidden;
}
.vxe-icon--download:before,
.vxe-icon--upload:before {
    left: 0;
    width: 1em;
    border-width: 0;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--download:after,
.vxe-icon--upload:after {
    width: 100%;
    text-align: center;
    font-size: 2em;
}
.vxe-icon--upload:before {
    top: 0.1em;
    border-top-width: 0.1em;
}
.vxe-icon--upload:after {
    content: '\2191';
    left: 0;
    top: 0.15em;
}
.vxe-icon--download:before {
    bottom: 0.05em;
    border-bottom-width: 0.1em;
}
.vxe-icon--download:after {
    content: '\2191';
    left: 0;
    bottom: 0.15em;
    transform: rotate(180deg);
}
.vxe-icon--eye-slash:before,
.vxe-icon--eye:before {
    content: '\25cf';
    top: 0.16em;
    left: 0;
    width: 1em;
    height: 0.68em;
    line-height: 0.25em;
    border-radius: 50%;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
    text-align: center;
}
.vxe-icon--eye-slash:after {
    top: -0.1em;
    left: 0.45em;
    width: 0;
    height: 1.2em;
    border-width: 0;
    border-style: solid;
    border-color: inherit;
    border-left-width: 0.1em;
    transform: rotate(45deg);
}
.vxe-icon--calendar:before {
    top: 0.15em;
    left: 0;
    width: 1em;
    height: 0.8em;
    border-width: 0.2em 0.1em 0.1em 0.1em;
    border-radius: 0.1em 0.1em 0 0;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--calendar:after {
    left: 0.2em;
    top: 0;
    width: 0.6em;
    height: 0.3em;
    border-width: 0 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--dot:before {
    top: 0.25em;
    left: 0.25em;
    border-radius: 50%;
    border-width: 0.25em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--print {
    box-shadow: inset 0 0 0 0.1em;
    border-width: 0.2em 0;
    border-style: solid;
    border-color: transparent !important;
    border-radius: 0.3em 0.3em 0 0;
}
.vxe-icon--print:before {
    height: 0.3em;
    top: -0.2em;
}
.vxe-icon--print:after,
.vxe-icon--print:before {
    width: 0.6em;
    left: 0.2em;
    box-shadow: inset 0 0 0 0.1em;
}
.vxe-icon--print:after {
    height: 0.6em;
    bottom: -0.2em;
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-icon--search:before {
    top: 0;
    left: 0;
    width: 0.8em;
    height: 0.8em;
    border-width: 0.15em;
    border-style: solid;
    border-color: inherit;
    border-radius: 50%;
}
.vxe-icon--search:after {
    top: 0.75em;
    left: 0.6em;
    width: 0.35em;
    height: 0;
    border-width: 0.15em 0 0 0;
    border-style: solid;
    border-color: inherit;
    transform: rotate(45deg);
}
.vxe-icon-warnion-circle-fill:before {
    content: '\e848';
}
@font-face {
    font-family: vxetableiconfont;
    src: url('@/assets/css/iconfont.1749092482353.woff2') format('woff2'),
        url('@/assets/css/iconfont.1749092482353.woff') format('woff'),
        url('@/assets/css/iconfont.1749092482353.ttf') format('truetype');
}
@keyframes rollCircle {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(1turn);
    }
}
[class*='vxe-table-icon-'] {
    font-family: vxetableiconfont !important;
    font-style: normal;
    font-weight: 400;
    font-size: 1.1em;
    line-height: 1em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
[class*='vxe-table-icon-'].animat,
[class*='vxe-table-icon-'].roll {
    display: inline-block;
}
[class*='vxe-table-icon-'].animat {
    transition: transform 0.25s ease-in-out;
}
[class*='vxe-table-icon-'].rotate45 {
    transform: rotate(45deg);
}
[class*='vxe-table-icon-'].rotate90 {
    transform: rotate(90deg);
}
[class*='vxe-table-icon-'].rotate180 {
    transform: rotate(180deg);
}
[class*='vxe-table-icon-'].roll {
    animation: rollCircle 1s linear infinite;
}
[class*='vxe-table-icon-'].theme--primary {
    color: var(--vxe-ui-font-primary-color);
}
[class*='vxe-table-icon-'].theme--success {
    color: var(--vxe-ui-status-success-color);
}
[class*='vxe-table-icon-'].theme--info {
    color: var(--vxe-ui-status-info-color);
}
[class*='vxe-table-icon-'].theme--warning {
    color: var(--vxe-ui-status-warning-color);
}
[class*='vxe-table-icon-'].theme--danger {
    color: var(--vxe-ui-status-danger-color);
}
[class*='vxe-table-icon-'].theme--error {
    color: var(--vxe-ui-status-error-color);
}
.vxe-table-icon-add-sub:before {
    content: '\e6bc';
}
.vxe-table-icon-swap:before {
    content: '\e7f3';
}
.vxe-table-icon-sort:before {
    content: '\e93e';
}
.vxe-table-icon-no-drop:before {
    content: '\e658';
}
.vxe-table-icon-edit:before {
    content: '\e66e';
}
.vxe-table-icon-question-circle-fill:before {
    content: '\e690';
}
.vxe-table-icon-radio-checked:before {
    content: '\e75b';
}
.vxe-table-icon-radio-checked-fill:before {
    content: '\e763';
}
.vxe-table-icon-print:before {
    content: '\eba0';
}
.vxe-table-icon-checkbox-checked-fill:before {
    content: '\e67d';
}
.vxe-table-icon-custom-column:before {
    content: '\e62d';
}
.vxe-table-icon-radio-unchecked:before {
    content: '\e7c9';
}
.vxe-table-icon-caret-down:before {
    content: '\e8ed';
}
.vxe-table-icon-caret-up:before {
    content: '\e8ee';
}
.vxe-table-icon-caret-right:before {
    content: '\e8ef';
}
.vxe-table-icon-caret-left:before {
    content: '\e8f0';
}
.vxe-table-icon-fullscreen:before {
    content: '\e70e';
}
.vxe-table-icon-minimize:before {
    content: '\e749';
}
.vxe-table-icon-checkbox-unchecked:before {
    content: '\e727';
}
.vxe-table-icon-funnel:before {
    content: '\e8ec';
}
.vxe-table-icon-download:before {
    content: '\e61a';
}
.vxe-table-icon-spinner:before {
    content: '\e601';
}
.vxe-table-icon-arrow-right:before {
    content: '\e743';
}
.vxe-table-icon-repeat:before {
    content: '\ea4a';
}
.vxe-table-icon-drag-handle:before {
    content: '\e64e';
}
.vxe-table-icon-checkbox-indeterminate-fill:before {
    content: '\e8c4';
}
.vxe-table-icon-upload:before {
    content: '\e683';
}
.vxe-table-icon-fixed-left-fill:before {
    content: '\e9b9';
}
.vxe-table-icon-fixed-left:before {
    content: '\e9ba';
}
.vxe-table-icon-fixed-right-fill:before {
    content: '\f290';
}
.vxe-table-icon-fixed-right:before {
    content: '\f291';
}
.vxe-table-custom--option {
    position: relative;
    display: flex;
    flex-direction: row;
}
.vxe-table-custom--option.active--drag-origin {
    opacity: 0.5;
}
.vxe-table-custom--option.active--drag-target[drag-pos='top']:after {
    display: block;
    top: -2px;
}
.vxe-table-custom--option.active--drag-target[drag-pos='bottom']:after {
    display: block;
    bottom: -2px;
}
.vxe-table-custom--option:first-child[drag-pos='top']:after {
    top: 0;
}
.vxe-table-custom--option:last-child[drag-pos='bottom']:after {
    bottom: 0;
}
.vxe-table-custom--option:after {
    display: none;
    content: '';
    position: absolute;
    left: -1px;
    width: calc(100% + 1px);
    height: 2px;
    background-color: var(--vxe-ui-font-primary-color);
    z-index: 12;
}
.vxe-table-custom--option:last-child:after {
    width: 100%;
}
.vxe-table-custom-wrapper {
    display: none;
    flex-direction: column;
    position: absolute;
    text-align: left;
    background-color: var(--vxe-ui-layout-background-color);
    z-index: 19;
    border: 1px solid var(--vxe-ui-table-border-color);
    border-radius: var(--vxe-ui-border-radius);
    box-shadow: var(--vxe-ui-base-popup-box-shadow);
}
.vxe-table-custom-wrapper.placement--top-left {
    top: 2px;
    left: 2px;
}
.vxe-table-custom-wrapper.placement--top-right {
    top: 2px;
    right: 2px;
}
.vxe-table-custom-wrapper.placement--bottom-left {
    bottom: 2px;
    left: 2px;
}
.vxe-table-custom-wrapper.placement--bottom-right {
    bottom: 2px;
    right: 2px;
}
.vxe-table-custom-wrapper.placement--left {
    left: 2px;
}
.vxe-table-custom-wrapper.placement--right {
    right: 2px;
}
.vxe-table-custom-wrapper.placement--left,
.vxe-table-custom-wrapper.placement--right {
    top: 2px;
    height: calc(100% - 4px);
}
.vxe-table-custom-wrapper.is--active {
    display: flex;
}
.vxe-table-custom--body {
    position: relative;
    display: block;
    flex-grow: 1;
    overflow-x: hidden;
    overflow-y: auto;
}
.vxe-table-custom--panel-list {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
.vxe-table-custom--panel-list > li {
    max-width: 26em;
    min-width: 18em;
    padding: 0.2em 1em 0.2em 1em;
}
.vxe-table-custom--panel-list > li.level--2 {
    padding-left: 2.7em;
}
.vxe-table-custom--panel-list > li.level--3 {
    padding-left: 3.7em;
}
.vxe-table-custom--panel-list > li.level--4 {
    padding-left: 4.7em;
}
.vxe-table-custom--panel-list > li.level--5 {
    padding-left: 5.7em;
}
.vxe-table-custom--panel-list > li.level--6 {
    padding-left: 6.7em;
}
.vxe-table-custom--panel-list > li.level--7 {
    padding-left: 7.7em;
}
.vxe-table-custom--panel-list > li.level--8 {
    padding-left: 8.7em;
}
.vxe-table-custom--header {
    flex-shrink: 0;
    padding: 0.28em 0;
    font-weight: 700;
    border-bottom: 1px solid var(--vxe-ui-base-popup-border-color);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-custom--panel-list .vxe-table-custom--checkbox-option:hover {
    background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table-custom--footer-buttons {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    border-top: 1px solid var(--vxe-ui-base-popup-border-color);
}
.vxe-table-custom--footer-buttons button {
    flex-grow: 1;
    height: 2.8em;
}
.vxe-table-custom--checkbox-option .vxe-checkbox--icon {
    font-size: 1.34em;
    color: var(--vxe-ui-input-border-color);
    vertical-align: middle;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-custom--checkbox-option.is--checked,
.vxe-table-custom--checkbox-option.is--checked .vxe-checkbox--icon,
.vxe-table-custom--checkbox-option.is--indeterminate,
.vxe-table-custom--checkbox-option.is--indeterminate .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--checkbox-option:not(.is--disabled) {
    cursor: pointer;
}
.vxe-table-custom--checkbox-option:not(.is--disabled):hover .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--checkbox-option.is--hidden {
    cursor: default;
}
.vxe-table-custom--checkbox-option.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table-custom--checkbox-option.is--disabled .vxe-checkbox--icon {
    color: var(--vxe-ui-input-disabled-color);
}
.vxe-table-custom--checkbox-option .vxe-checkbox--label {
    padding-left: 0.5em;
    vertical-align: middle;
}
.vxe-table-custom--checkbox-option,
.vxe-table-custom--sort-option {
    padding-right: 0.4em;
    flex-shrink: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-custom--sort-option {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.vxe-table-custom--sort-btn {
    padding-left: 0.2em;
    padding-right: 0.4em;
}
.vxe-table-custom--sort-btn:not(.is--disabled) {
    cursor: grab;
}
.vxe-table-custom--sort-btn:not(.is--disabled):active {
    cursor: grabbing;
}
.vxe-table-custom--sort-btn:not(.is--disabled):hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--sort-btn.is--disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
}
.vxe-table-custom--name-option {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;
}
.vxe-table-custom--checkbox-label {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table-custom--fixed-option {
    flex-shrink: 0;
    padding-left: 0.5em;
    display: flex;
    flex-direction: row;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-custom-popup--body {
    position: relative;
    overflow: auto;
    height: 100%;
    outline: 0;
}
.vxe-table-custom-popup--table-wrapper {
    border-bottom: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-custom-popup--table-wrapper table {
    width: 100%;
    border: 0;
    table-layout: fixed;
    word-break: break-all;
    border-spacing: 0;
    border-collapse: separate;
}
.vxe-table-custom-popup--table-wrapper table th {
    position: sticky;
    top: 0;
    text-align: left;
    border-bottom: 1px solid var(--vxe-ui-table-border-color);
    background-color: var(--vxe-ui-table-header-background-color);
    z-index: 7;
}
.vxe-table-custom-popup--table-wrapper table td,
.vxe-table-custom-popup--table-wrapper table th {
    border-top: 1px solid var(--vxe-ui-table-border-color);
    border-left: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-custom-popup--table-wrapper table tr:first-child td {
    border-top: 0;
}
.vxe-table-custom-popup--table-wrapper table tr:hover {
    background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table-custom-popup--table-wrapper table td,
.vxe-table-custom-popup--table-wrapper table th {
    height: 44px;
    padding: 0 0.6em;
}
.vxe-table-custom-popup--table-wrapper table td:last-child,
.vxe-table-custom-popup--table-wrapper table th:last-child {
    border-right: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-custom-popup--drag-line {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 11;
    pointer-events: none;
    width: 100%;
    height: 1px;
    border: 2px solid transparent;
}
.vxe-table-custom-popup--drag-line[drag-pos='top'] {
    border-top-color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--drag-line[drag-pos='bottom'] {
    border-bottom-color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--drag-line[drag-to-child='y'] {
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: var(--vxe-ui-status-success-color);
}
.vxe-table-custom-popup--drag-line.is--guides {
    background-color: var(--vxe-ui-table-drag-over-background-color);
}
.vxe-table-custom-popup--drag-tip {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0.6em 1.4em;
    max-width: 50%;
    min-width: 100px;
    border-radius: var(--vxe-ui-border-radius);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    background-color: var(--vxe-ui-layout-background-color);
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    z-index: 33;
}
.vxe-table-custom-popup--drag-tip[drag-status='disabled'] .vxe-table-custom-popup--drag-tip-disabled-status,
.vxe-table-custom-popup--drag-tip[drag-status='normal'] .vxe-table-custom-popup--drag-tip-normal-status,
.vxe-table-custom-popup--drag-tip[drag-status='sub'] .vxe-table-custom-popup--drag-tip-sub-status {
    display: block;
}
.vxe-table-custom-popup--drag-tip-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-table-custom-popup--drag-tip-status {
    padding-right: 0.4em;
}
.vxe-table-custom-popup--drag-tip-disabled-status {
    display: none;
    flex-shrink: 0;
    color: var(--vxe-ui-status-error-color);
}
.vxe-table-custom-popup--drag-tip-normal-status,
.vxe-table-custom-popup--drag-tip-sub-status {
    display: none;
}
.vxe-table-custom-popup--drag-tip-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table-custom-popup--name {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-table-custom-popup--title {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table-custom-popup--row.level--2 .vxe-table-custom-popup--name {
    padding-left: 24px;
}
.vxe-table-custom-popup--row.level--3 .vxe-table-custom-popup--name {
    padding-left: 48px;
}
.vxe-table-custom-popup--row.level--4 .vxe-table-custom-popup--name {
    padding-left: 72px;
}
.vxe-table-custom-popup--row.level--5 .vxe-table-custom-popup--name {
    padding-left: 96px;
}
.vxe-table-custom-popup--row.level--6 .vxe-table-custom-popup--name {
    padding-left: 120px;
}
.vxe-table-custom-popup--row.level--7 .vxe-table-custom-popup--name {
    padding-left: 144px;
}
.vxe-table-custom-popup--row.level--8 .vxe-table-custom-popup--name {
    padding-left: 168px;
}
.vxe-table-custom-popup--row.level--9 .vxe-table-custom-popup--name {
    padding-left: 192px;
}
.vxe-table-custom-popup--column-item {
    position: relative;
}
.vxe-table-custom-popup--column-item.col--fixed,
.vxe-table-custom-popup--column-item.col--resizable,
.vxe-table-custom-popup--column-item.col--visible {
    text-align: center;
}
.vxe-table-custom-popup--column-item.col--resizable > .vxe-input,
.vxe-table-custom-popup--column-item.col--resizable > .vxe-number-input {
    width: 100%;
}
.vxe-table-custom-popup--row.active--drag-origin .vxe-table-custom-popup--column-item {
    opacity: 0.5;
}
.vxe-table-custom-popup--row.active--drag-target[drag-pos='top'] .vxe-table-custom-popup--column-item:after {
    display: block;
    top: -2px;
}
.vxe-table-custom-popup--row.active--drag-target[drag-pos='bottom'] .vxe-table-custom-popup--column-item:after {
    display: block;
    bottom: -2px;
}
.vxe-table-custom-popup--row:first-child[drag-pos='top'] .vxe-table-custom-popup--column-item:after {
    top: 0;
}
.vxe-table-custom-popup--row:last-child[drag-pos='bottom'] .vxe-table-custom-popup--column-item:after {
    bottom: 0;
}
.vxe-table-custom-popup--column-item:after {
    display: none;
    content: '';
    position: absolute;
    left: -1px;
    width: calc(100% + 1px);
    height: 2px;
    background-color: var(--vxe-ui-font-primary-color);
    z-index: 12;
}
.vxe-table-custom-popup--column-item:last-child:after {
    width: 100%;
}
.vxe-table-custom--list-move {
    transition-property: transform;
    transition-duration: 0.35s;
    transition-delay: 0.05s;
}
.vxe-table-custom-popup--column-sort-placeholder {
    padding: 0.2em 0.5em;
}
.vxe-table-custom-popup--column-sort-btn {
    font-size: 1.2em;
    padding: 0.2em 0.5em;
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled) {
    cursor: grab;
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled):hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled):active {
    cursor: grabbing;
}
.vxe-table-custom-popup--column-sort-btn.is--disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
}
.vxe-table-custom-popup--table-sort-help-icon,
.vxe-table-custom-popup--table-sort-help-title {
    vertical-align: middle;
}
.vxe-table-custom-popup--table-sort-help-icon {
    margin-left: 5px;
    cursor: help;
}
.vxe-table-custom-popup--table-col-seq,
.vxe-table-custom-popup--table-col-sort {
    width: 80px;
}
.vxe-table-custom-popup--table-col-title {
    min-width: 120px;
}
.vxe-table-custom-popup--table-col-width {
    width: 140px;
}
.vxe-table-custom-popup--table-col-fixed {
    width: 200px;
}
.vxe-table-export--panel-column > ul {
    list-style-type: none;
    overflow: auto;
    margin: 0;
    padding: 0;
    outline: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-export--panel-column > ul > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}
.vxe-table-export--panel .vxe-table-export--panel-table {
    width: 100%;
    border: 0;
    table-layout: fixed;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td {
    padding: 0 10px;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td:first-child {
    text-align: right;
    width: 30%;
    font-weight: 700;
    padding: 8px 10px;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td:nth-child(2) {
    width: 70%;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-input,
.vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-select {
    width: 80%;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-table-export--panel-option-row {
    padding: 0.25em 0;
}
.vxe-table-export--panel .vxe-table-export--panel-column {
    width: 80%;
    border: 1px solid var(--vxe-ui-input-border-color);
    margin: 3px 0;
    border-radius: var(--vxe-ui-border-radius);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li {
    padding: 0.2em 1em 0.2em 1em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--2 {
    padding-left: 3.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--2 .vxe-checkbox--icon {
    left: 1.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--3 {
    padding-left: 4.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--3 .vxe-checkbox--icon {
    left: 2.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--4 {
    padding-left: 5.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--4 .vxe-checkbox--icon {
    left: 3.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--5 {
    padding-left: 6.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--5 .vxe-checkbox--icon {
    left: 4.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--6 {
    padding-left: 7.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--6 .vxe-checkbox--icon {
    left: 5.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--7 {
    padding-left: 8.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--7 .vxe-checkbox--icon {
    left: 6.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--8 {
    padding-left: 9.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--8 .vxe-checkbox--icon {
    left: 7.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column .vxe-table-export--panel-column-header {
    padding: 0.1em 0;
    background-color: var(--vxe-ui-table-header-background-color);
    font-weight: 700;
    border-bottom: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-export--panel .vxe-table-export--panel-column .vxe-table-export--panel-column-body {
    padding: 0.2em 0;
    min-height: 10em;
    max-height: 17.6em;
}
.vxe-table-export--panel .vxe-table-export--panel-btns {
    text-align: right;
    padding: 0.8em 0.25em 0.25em 0.25em;
}
.vxe-table-export--panel .vxe-table-export--selected--file {
    padding-right: 40px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-export--panel .vxe-table-export--selected--file > i {
    display: none;
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    font-size: 16px;
    cursor: pointer;
}
.vxe-table-export--panel .vxe-table-export--selected--file:hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel .vxe-table-export--selected--file:hover > i {
    display: block;
}
.vxe-table-export--panel .vxe-table-export--select--file {
    width: 80%;
    border: 1px dashed var(--vxe-ui-input-border-color);
    padding: 6px 34px;
    outline: 0;
    border-radius: var(--vxe-ui-border-radius);
    background-color: var(--vxe-ui-layout-background-color);
    color: inherit;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: pointer;
}
.vxe-table-export--panel .vxe-table-export--select--file:focus {
    border-color: var(--vxe-ui-font-primary-color);
    box-shadow: 0 0 0.25em 0 var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel .vxe-table-export--select--file:hover {
    color: var(--vxe-ui-font-primary-color);
    border-color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option .vxe-checkbox--icon {
    font-size: 1.34em;
    color: var(--vxe-ui-input-border-color);
    vertical-align: middle;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table-export--panel-column-option.is--checked,
.vxe-table-export--panel-column-option.is--checked .vxe-checkbox--icon,
.vxe-table-export--panel-column-option.is--indeterminate,
.vxe-table-export--panel-column-option.is--indeterminate .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option:not(.is--disabled) {
    cursor: pointer;
}
.vxe-table-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option.is--hidden {
    cursor: default;
}
.vxe-table-export--panel-column-option.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table-export--panel-column-option.is--disabled .vxe-checkbox--icon {
    color: var(--vxe-ui-input-disabled-color);
}
.vxe-table-export--panel-column-option .vxe-checkbox--label {
    padding-left: 0.5em;
    vertical-align: middle;
}
.vxe-cell--filter {
    padding: 0 0.1em 0 0.2em;
    text-align: center;
    vertical-align: middle;
    display: inline-block;
    line-height: 0;
}
.vxe-cell--filter.col--filter .vxe-filter--btn {
    color: var(--vxe-ui-font-color);
}
.vxe-cell--filter .vxe-filter--btn {
    color: var(--vxe-ui-table-column-icon-border-color);
    cursor: pointer;
}
.vxe-cell--filter .vxe-filter--btn:hover {
    color: var(--vxe-ui-font-color);
}
.is--filter-active .vxe-cell--filter .vxe-filter--btn {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-wrapper {
    display: none;
    position: absolute;
    top: 0;
    min-width: 100px;
    font-size: var(--vxe-ui-font-size-default);
    border-radius: var(--vxe-ui-border-radius);
    background-color: var(--vxe-ui-layout-background-color);
    border: 1px solid var(--vxe-ui-base-popup-border-color);
    color: var(--vxe-ui-font-color);
    box-shadow: var(--vxe-ui-base-popup-box-shadow);
    z-index: 10;
}
.vxe-table--filter-wrapper:not(.is--multiple) {
    text-align: center;
}
.vxe-table--filter-wrapper.is--active {
    display: block;
}
.vxe-table--filter-wrapper .vxe-table--filter-body > li,
.vxe-table--filter-wrapper .vxe-table--filter-header > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 360px;
    padding: 0.25em 0.8em;
    cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-body > li.is--checked,
.vxe-table--filter-wrapper .vxe-table--filter-header > li.is--checked {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover,
.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover {
    background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-header {
    padding-top: 0.2em;
}
.vxe-table--filter-wrapper .vxe-table--filter-body {
    max-height: 200px;
    padding-bottom: 0.2em;
}
.vxe-table--filter-wrapper > ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    outline: 0;
    overflow: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--filter-wrapper.is--multiple > ul > li {
    padding: 0.25em 0.8em 0.25em 1em;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer {
    border-top: 1px solid var(--vxe-ui-base-popup-border-color);
    padding: 0.6em;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button {
    background-color: transparent;
    padding: 0 0.4em;
    border: 0;
    color: var(--vxe-ui-font-color);
    cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:focus {
    outline: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table--filter-option .vxe-checkbox--icon {
    font-size: 1.34em;
    color: var(--vxe-ui-input-border-color);
    vertical-align: middle;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--filter-option.is--checked,
.vxe-table--filter-option.is--checked .vxe-checkbox--icon,
.vxe-table--filter-option.is--indeterminate,
.vxe-table--filter-option.is--indeterminate .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-option:not(.is--disabled) {
    cursor: pointer;
}
.vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-option.is--hidden {
    cursor: default;
}
.vxe-table--filter-option.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table--filter-option.is--disabled .vxe-checkbox--icon {
    color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--filter-option .vxe-checkbox--label {
    padding-left: 0.5em;
    vertical-align: middle;
}
.vxe-table--filter-wrapper.size--medium {
    font-size: var(--vxe-ui-font-size-medium);
}
.vxe-table--filter-wrapper.size--small {
    font-size: var(--vxe-ui-font-size-small);
}
.vxe-table--filter-wrapper.size--mini {
    font-size: var(--vxe-ui-font-size-mini);
}
.vxe-table--context-menu-wrapper {
    display: none;
}
.vxe-table--context-menu-wrapper.is--visible {
    display: block;
}
.vxe-table--context-menu-clild-wrapper,
.vxe-table--context-menu-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    border: 1px solid var(--vxe-ui-base-popup-border-color);
    box-shadow: 3px 3px 4px -2px rgba(0, 0, 0, 0.6);
    padding: 0 1px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    color: var(--vxe-ui-font-color);
    font-family: var(--vxe-ui-font-family);
    background-color: var(--vxe-ui-table-menu-background-color);
}
.vxe-context-menu--link {
    display: flex;
    flex-direction: row;
    width: var(--vxe-ui-table-menu-item-width);
    line-height: 26px;
    padding: 0 0.8em;
    color: var(--vxe-ui-font-color);
    cursor: pointer;
}
.vxe-context-menu--link .vxe-context-menu--link-prefix,
.vxe-context-menu--link .vxe-context-menu--link-suffix {
    min-width: 2em;
    flex-shrink: 0;
    text-align: center;
    padding: 0 0.2em;
}
.vxe-context-menu--link .vxe-context-menu--link-content {
    flex-grow: 1;
    padding: 0 0.2em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-context-menu--option-wrapper,
.vxe-table--context-menu-clild-wrapper {
    margin: 0;
    padding: 0;
    list-style-type: none;
    border-bottom: 1px solid #e8eaed;
}
.vxe-context-menu--option-wrapper li,
.vxe-table--context-menu-clild-wrapper li {
    position: relative;
    margin: 1px 0;
    border: 1px solid transparent;
}
.vxe-context-menu--option-wrapper li:last-child,
.vxe-table--context-menu-clild-wrapper li:last-child {
    border: 0;
}
.vxe-context-menu--option-wrapper li.link--active,
.vxe-table--context-menu-clild-wrapper li.link--active {
    background-color: #c5c5c5;
    border-color: #c5c5c5;
}
.vxe-context-menu--option-wrapper li.link--active > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--active > .vxe-context-menu--link {
    color: #2b2b2b;
}
.vxe-context-menu--option-wrapper li.link--disabled > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--disabled > .vxe-context-menu--link {
    color: var(--vxe-ui-font-disabled-color);
    cursor: no-drop;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active {
    border-color: #c0c1c2;
    background-color: #eee;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active:hover,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active:hover {
    background-color: inherit;
}
.vxe-table--context-menu-clild-wrapper {
    display: none;
    top: 0;
    left: 100%;
}
.vxe-table--context-menu-clild-wrapper.is--show {
    display: block;
}
.vxe-table--file-form,
.vxe-table-slots {
    display: none;
}
.vxe-table-vars {
    height: 0;
    width: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
}
.vxe-table-vars .vxe-table-var-default {
    height: var(--vxe-ui-table-row-height-default);
}
.vxe-table-vars .vxe-table-var-medium {
    height: var(--vxe-ui-table-row-height-medium);
}
.vxe-table-vars .vxe-table-var-small {
    height: var(--vxe-ui-table-row-height-small);
}
.vxe-table-vars .vxe-table-var-mini {
    height: var(--vxe-ui-table-row-height-mini);
}
.vxe-table--print-frame {
    position: fixed;
    bottom: -100%;
    left: -100%;
    height: 0;
    width: 0;
    border: 0;
}
.vxe-table--layout-wrapper {
    display: flex;
    flex-direction: row;
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-table--viewport-wrapper {
    position: relative;
    overflow: hidden;
    flex-grow: 1;
}
.vxe-table--body-wrapper,
.vxe-table--fixed-left-body-wrapper,
.vxe-table--fixed-right-body-wrapper,
.vxe-table--footer-wrapper,
.vxe-table--header-wrapper {
    overflow: hidden;
    outline: 0;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
}
.vxe-table--body-inner-wrapper,
.vxe-table--footer-inner-wrapper,
.vxe-table--header-inner-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
}
.vxe-table--body-inner-wrapper::-webkit-scrollbar,
.vxe-table--footer-inner-wrapper::-webkit-scrollbar,
.vxe-table--header-inner-wrapper::-webkit-scrollbar {
    display: none;
}
.vxe-table--footer-inner-wrapper,
.vxe-table--header-inner-wrapper {
    overflow-y: hidden;
    overflow-x: scroll;
}
.vxe-table--body-inner-wrapper {
    overflow-y: scroll;
    overflow-x: scroll;
}
.vxe-loading--custom-wrapper {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-loading--custom-wrapper.is--visible {
    display: block;
}
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-date-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-date-range-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-ico-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-number-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-tree-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-date-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-date-range-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-ico-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-number-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-date-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-date-range-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-ico-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-number-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-tree-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-tree-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-range-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-textarea,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-ico-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-number-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-textarea,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-tree-select,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-picker,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-range-picker,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-select,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-textarea,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-ico-picker,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-number-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-select,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-textarea,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-tree-select,
.vxe-table--filter-template > .vxe-date-picker,
.vxe-table--filter-template > .vxe-date-range-picker,
.vxe-table--filter-template > .vxe-default-input,
.vxe-table--filter-template > .vxe-default-select,
.vxe-table--filter-template > .vxe-default-textarea,
.vxe-table--filter-template > .vxe-ico-picker,
.vxe-table--filter-template > .vxe-input,
.vxe-table--filter-template > .vxe-number-input,
.vxe-table--filter-template > .vxe-select,
.vxe-table--filter-template > .vxe-textarea,
.vxe-table--filter-template > .vxe-tree-select {
    width: 100%;
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-textarea {
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-select,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-select,
.vxe-table--filter-template .vxe-default-textarea {
    font-family: var(--vxe-ui-font-family);
    outline: 0;
    color: var(--vxe-ui-font-color);
    border-radius: var(--vxe-ui-border-radius);
    border: 1px solid var(--vxe-ui-input-border-color);
}
.vxe-cell .vxe-default-input:focus,
.vxe-cell .vxe-default-select:focus,
.vxe-cell .vxe-default-textarea:focus,
.vxe-table--filter-template .vxe-default-input:focus,
.vxe-table--filter-template .vxe-default-select:focus,
.vxe-table--filter-template .vxe-default-textarea:focus {
    border: 1px solid var(--vxe-ui-font-primary-color);
}
.vxe-cell .vxe-default-input[disabled],
.vxe-cell .vxe-default-select[disabled],
.vxe-cell .vxe-default-textarea[disabled],
.vxe-table--filter-template .vxe-default-input[disabled],
.vxe-table--filter-template .vxe-default-select[disabled],
.vxe-table--filter-template .vxe-default-textarea[disabled] {
    cursor: not-allowed;
    background-color: var(--vxe-ui-input-disabled-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-select,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-select,
.vxe-table--filter-template .vxe-default-textarea {
    height: var(--vxe-ui-input-height-default);
}
.vxe-cell .vxe-default-input[type='date']::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type='date']::-webkit-inner-spin-button {
    margin-top: 4px;
}
.vxe-cell .vxe-default-input[type='date']::-webkit-inner-spin-button,
.vxe-cell .vxe-default-input[type='number']::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type='date']::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type='number']::-webkit-inner-spin-button {
    height: 24px;
}
.vxe-cell .vxe-default-input::-moz-placeholder,
.vxe-table--filter-template .vxe-default-input::-moz-placeholder {
    color: var(--vxe-ui-input-placeholder-color);
}
.vxe-cell .vxe-default-input::placeholder,
.vxe-table--filter-template .vxe-default-input::placeholder {
    color: var(--vxe-ui-input-placeholder-color);
}
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-textarea {
    font-size: 1em;
    resize: none;
    vertical-align: middle;
}
.vxe-cell > .vxe-input > .vxe-input--inner,
.vxe-cell > .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-textarea > .vxe-textarea--inner {
    padding: 0 2px;
}
.vxe-cell > .vxe-default-textarea,
.vxe-cell > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-default-textarea,
.vxe-table--filter-template > .vxe-textarea--inner {
    resize: none;
}
.vxe-cell > .vxe-cell--tree-node .vxe-input > .vxe-input--inner,
.vxe-cell > .vxe-cell--tree-node .vxe-textarea > .vxe-textarea--inner,
.vxe-cell > .vxe-row-group--tree-node .vxe-input > .vxe-input--inner,
.vxe-cell > .vxe-row-group--tree-node .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-textarea > .vxe-textarea--inner {
    padding: 0 2px;
}
.vxe-cell > .vxe-cell--tree-node .vxe-default-textarea,
.vxe-cell > .vxe-cell--tree-node .vxe-textarea--inner,
.vxe-cell > .vxe-row-group--tree-node .vxe-default-textarea,
.vxe-cell > .vxe-row-group--tree-node .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-default-textarea,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-default-textarea,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-textarea--inner {
    resize: none;
}
.vxe-body--column.col--vertical-top.col--active > .vxe-cell > .vxe-cell--wrapper,
.vxe-body--column.col--vertical-top.col--active > .vxe-cell > .vxe-cell--wrapper > .vxe-default-textarea {
    height: 100%;
}
.vxe-table:not([data-calc-row]) .vxe-body--column.col--vertical-top:not(.col--active) > .vxe-cell > .vxe-cell--wrapper {
    min-height: 100%;
}
.vxe-cell--label.is--negative {
    color: var(--vxe-ui-table-cell-negative-color);
}
.vxe-table--cell-active-area,
.vxe-table--cell-col-status-area,
.vxe-table--cell-copy-area,
.vxe-table--cell-extend-area,
.vxe-table--cell-main-area,
.vxe-table--cell-row-status-area,
.vxe-table--checkbox-range {
    display: none;
    position: absolute;
    pointer-events: none;
    z-index: 1;
}
.vxe-table--header-wrapper .vxe-table--cell-col-status-area {
    top: 0;
    height: 100%;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-col-status-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-row-status-area,
.vxe-table--fixed-left-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-col-status-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-row-status-area,
.vxe-table--fixed-right-wrapper .vxe-table--checkbox-range {
    z-index: 2;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area[half='1'],
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area[half='1'],
.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area[half='1'] {
    border-right: 0;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area[half='1'] {
    background-size: var(--vxe-ui-table-cell-copy-area-border-width) 12px, 0 12px,
        12px var(--vxe-ui-table-cell-copy-area-border-width), 12px var(--vxe-ui-table-cell-copy-area-border-width);
}
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area[half='1'],
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area[half='1'],
.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area[half='1'] {
    border-left: 0;
}
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area[half='1'] {
    background-size: 0 12px, var(--vxe-ui-table-cell-copy-area-border-width) 12px,
        12px var(--vxe-ui-table-cell-copy-area-border-width), 12px var(--vxe-ui-table-cell-copy-area-border-width);
}
.vxe-table--checkbox-range {
    background-color: var(--vxe-ui-table-checkbox-range-background-color);
    border: var(--vxe-ui-table-checkbox-range-border-width) solid var(--vxe-ui-table-checkbox-range-border-color);
}
.vxe-table--cell-area {
    height: 0;
    font-size: 0;
    display: none;
}
.vxe-table--cell-area > .vxe-table--cell-main-area {
    background-color: var(--vxe-ui-table-cell-area-background-color);
    border: var(--vxe-ui-table-cell-area-border-width) solid var(--vxe-ui-table-cell-area-border-color);
}
.vxe-table--cell-area .vxe-table--cell-main-area-btn {
    display: none;
    position: absolute;
    right: -1px;
    bottom: -1px;
    width: 7px;
    height: 7px;
    border-style: solid;
    border-color: var(--vxe-ui-table-cell-main-area-extension-border-color);
    border-width: 1px 0 0 1px;
    background-color: var(--vxe-ui-table-cell-main-area-extension-background-color);
    pointer-events: auto;
    cursor: crosshair;
}
.vxe-table--cell-area .vxe-table--cell-extend-area {
    border: var(--vxe-ui-table-cell-extend-area-border-width) solid var(--vxe-ui-table-cell-extend-area-border-color);
}
.vxe-table--cell-area .vxe-table--cell-col-status-area,
.vxe-table--cell-area .vxe-table--cell-row-status-area {
    background-color: var(--vxe-ui-table-checkbox-range-background-color);
}
@keyframes moveCopyCellBorder {
    to {
        background-position: 0 -12px, 100% 12px, 12px 0, -12px 100%;
    }
}
.vxe-table--cell-copy-area {
    background: linear-gradient(0deg, transparent 6px, var(--vxe-ui-table-cell-copy-area-border-color) 6px) repeat-y,
        linear-gradient(0deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-y,
        linear-gradient(90deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-x,
        linear-gradient(90deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-x;
    background-size: var(--vxe-ui-table-cell-copy-area-border-width) 12px,
        var(--vxe-ui-table-cell-copy-area-border-width) 12px, 12px var(--vxe-ui-table-cell-copy-area-border-width),
        12px var(--vxe-ui-table-cell-copy-area-border-width);
    background-position: 0 0, 100% 0, 0 0, 0 100%;
    animation: moveCopyCellBorder 0.5s linear infinite;
}
.vxe-table--cell-active-area {
    border-color: var(--vxe-ui-table-cell-active-area-border-color);
    border-style: solid;
    border-width: var(--vxe-ui-table-cell-active-area-border-width);
    background-color: var(--vxe-ui-table-cell-active-area-background-color);
}
.vxe-table--cell-multi-area > .vxe-table--cell-main-area {
    background-color: var(--vxe-ui-table-cell-area-background-color);
}
.vxe-table--render-default.is--round .vxe-table--border-line,
.vxe-table--render-default.is--round .vxe-table--render-default.is--round,
.vxe-table--render-default.is--round:not(.is--header):not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
    border-radius: var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.body--wrapper,
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.body--wrapper {
    border-radius: var(--vxe-ui-table-border-radius) var(--vxe-ui-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-left--wrapper,
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--left .vxe-table--scroll-y-top-corner,
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--left .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.is--round.sx-pos--top .vxe-table--scroll-x-left-corner:before {
    border-radius: var(--vxe-ui-table-border-radius) 0 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--right .vxe-table--scroll-y-top-corner,
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--right .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.is--round.sx-pos--top .vxe-table--scroll-x-right-corner:before {
    border-radius: 0 var(--vxe-ui-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.body--wrapper,
.vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
    border-radius: 0 0 var(--vxe-ui-table-border-radius) var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default.is--round.sx-pos--bottom .vxe-table--scroll-x-left-corner:before {
    border-radius: 0 0 0 var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-right--wrapper,
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default.is--round.sx-pos--bottom .vxe-table--scroll-x-right-corner:before {
    border-radius: 0 0 var(--vxe-ui-table-border-radius) 0;
}
.vxe-table[data-calc-col] .vxe-body--column .vxe-cell > .vxe-cell--wrapper,
.vxe-table[data-calc-col] .vxe-footer--column .vxe-cell > .vxe-cell--wrapper,
.vxe-table[data-calc-col] .vxe-header--column .vxe-cell > .vxe-cell--wrapper {
    word-break: break-all;
    white-space: nowrap;
}
.vxe-table:not([data-calc-col]) .vxe-cell--wrapper {
    min-width: 100%;
}
.vxe-table.is--loading > .vxe-table--layout-wrapper > .vxe-table--scroll-y-virtual,
.vxe-table.is--loading > .vxe-table--scroll-x-virtual {
    visibility: hidden;
}
.vxe-table .vxe-table--scroll-x-virtual {
    height: 0;
}
.vxe-table .vxe-table--scroll-y-virtual {
    width: 0;
}
.vxe-table .vxe-table--scroll-x-virtual,
.vxe-table .vxe-table--scroll-y-virtual {
    visibility: hidden;
    position: relative;
    flex-shrink: 0;
    z-index: 7;
}
.vxe-table .vxe-table--scroll-x-handle,
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner,
.vxe-table .vxe-table--scroll-x-wrapper,
.vxe-table .vxe-table--scroll-y-bottom-corner,
.vxe-table .vxe-table--scroll-y-handle,
.vxe-table .vxe-table--scroll-y-top-corner,
.vxe-table .vxe-table--scroll-y-wrapper {
    position: absolute;
}
.vxe-table .vxe-table--scroll-x-handle,
.vxe-table .vxe-table--scroll-x-wrapper {
    width: 100%;
    left: 0;
    bottom: 0;
}
.vxe-table .vxe-table--scroll-x-handle {
    overflow-y: hidden;
    overflow-x: scroll;
    height: 18px;
}
.vxe-table .vxe-table--scroll-x-wrapper {
    height: 100%;
}
.vxe-table .vxe-table--scroll-y-handle,
.vxe-table .vxe-table--scroll-y-wrapper {
    width: 100%;
    height: 100%;
    right: 0;
    top: 0;
}
.vxe-table .vxe-table--scroll-y-handle {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 18px;
    height: 100%;
}
.vxe-table .vxe-table--scroll-x-space {
    height: 1px;
}
.vxe-table .vxe-table--scroll-y-space {
    width: 1px;
}
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner,
.vxe-table .vxe-table--scroll-y-bottom-corner,
.vxe-table .vxe-table--scroll-y-top-corner {
    display: none;
    position: absolute;
}
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner {
    bottom: 0;
    width: 0;
    height: 100%;
}
.vxe-table .vxe-table--scroll-x-left-corner:before,
.vxe-table .vxe-table--scroll-x-right-corner:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    border-width: var(--vxe-ui-table-border-width);
    border-style: solid;
    border-color: var(--vxe-ui-table-border-color);
}
.vxe-table .vxe-table--scroll-x-left-corner {
    left: 0;
}
.vxe-table .vxe-table--scroll-x-right-corner {
    right: 0;
}
.vxe-table.sy-pos--right .vxe-table--scroll-x-right-corner {
    right: 1px;
}
.vxe-table.sy-pos--right .vxe-table--scroll-x-right-corner:before {
    border-right: 0;
}
.vxe-table.sx-pos--bottom .vxe-table--scroll-x-right-corner {
    bottom: 1px;
}
.vxe-table.sx-pos--bottom .vxe-table--scroll-x-right-corner:before {
    border-bottom: 0;
}
.vxe-table .vxe-table--scroll-y-top-corner {
    background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table .vxe-table--scroll-y-bottom-corner,
.vxe-table .vxe-table--scroll-y-top-corner {
    top: 0;
    right: 0;
    width: 100%;
    height: 0;
}
.vxe-table .vxe-table--scroll-y-bottom-corner {
    margin-top: -1px;
}
.vxe-table .vxe-table--header-wrapper {
    color: var(--vxe-ui-table-header-font-color);
}
.vxe-table .vxe-cell--sort {
    text-align: center;
    position: relative;
    padding: 0 0.1em 0 0.2em;
}
.vxe-table .vxe-cell--sort-vertical-layout {
    display: inline-flex;
    flex-direction: column;
    height: 1.8em;
    vertical-align: middle;
}
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--desc-btn {
    height: 0.6em;
}
.vxe-table .vxe-cell--sort-horizontal-layout {
    display: inline-flex;
    flex-direction: row;
}
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--desc-btn {
    width: 0.5em;
}
.vxe-table .vxe-sort--asc-btn,
.vxe-table .vxe-sort--desc-btn {
    color: var(--vxe-ui-table-column-icon-border-color);
    cursor: pointer;
}
.vxe-table .vxe-sort--asc-btn:hover,
.vxe-table .vxe-sort--desc-btn:hover {
    color: var(--vxe-ui-font-color);
}
.vxe-table .vxe-sort--asc-btn.sort--active,
.vxe-table .vxe-sort--desc-btn.sort--active {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-filter--btn:after,
.vxe-filter--btn:before,
.vxe-sort--asc-btn:after,
.vxe-sort--asc-btn:before,
.vxe-sort--desc-btn:after,
.vxe-sort--desc-btn:before {
    transition: border 0.1s ease-in-out;
}
.vxe-header--column {
    position: relative;
    font-weight: var(--vxe-ui-table-header-font-weight);
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper {
    display: flex;
    align-items: center;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--checkbox,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--drag-handle,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--filter,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--required-icon,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--sort,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell-title-prefix-icon,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell-title-suffix-icon {
    flex-shrink: 0;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-header--column .vxe-cell--required-icon {
    display: inline-block;
    color: var(--vxe-ui-table-validate-error-color);
    width: 0.8em;
    height: 1em;
    line-height: 1em;
    position: relative;
}
.vxe-header--column .vxe-cell--required-icon > i {
    font-family: Verdana, Arial, Tahoma;
    font-weight: 400;
}
.vxe-header--column .vxe-cell--required-icon > i:before {
    content: '*';
    position: absolute;
    left: 0;
    top: 0.2em;
}
.vxe-header--column .vxe-cell--required-icon {
    padding-right: 0.1em;
}
.vxe-header--column .vxe-cell--edit-icon,
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
    padding: 0.2em;
}
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
    cursor: help;
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--primary,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--primary {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--success,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--success {
    color: var(--vxe-ui-status-success-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--info,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--info {
    color: var(--vxe-ui-status-info-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--warning,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--warning {
    color: var(--vxe-ui-status-warning-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--danger,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--danger {
    color: var(--vxe-ui-status-danger-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--error,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--error {
    color: var(--vxe-ui-status-error-color);
}
.vxe-cell--col-resizable {
    position: absolute;
    right: -0.3em;
    bottom: 0;
    width: 0.6em;
    height: 100%;
    text-align: center;
    z-index: 1;
    cursor: col-resize;
}
.vxe-cell--col-resizable.is--line:after,
.vxe-cell--col-resizable.is--line:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
}
.vxe-cell--col-resizable.is--line:before {
    width: 1px;
    height: 50%;
    background-color: var(--vxe-ui-table-resizable-line-color);
}
.vxe-cell--col-resizable.is--line:after {
    width: 0;
    height: 100%;
}
.vxe-header--column:last-child > .vxe-cell--col-resizable {
    right: 0;
}
.vxe-table--fixed-right-wrapper .vxe-cell--col-resizable {
    right: auto;
    left: -0.3em;
}
.vxe-table--fixed-right-wrapper .vxe-header--column:last-child > .vxe-cell--col-resizable {
    left: 0;
}
.vxe-body--column .vxe-cell--row-resizable {
    position: absolute;
    left: 0;
    bottom: -0.4em;
    height: 0.8em;
    width: 100%;
    text-align: center;
    z-index: 1;
    cursor: row-resize;
}
.vxe-body--row:last-child .vxe-body--column .vxe-cell--row-resizable {
    height: 0.4em;
    bottom: 0;
}
.vxe-table--render-default {
    position: relative;
    font-size: var(--vxe-ui-font-size-default);
    color: var(--vxe-ui-font-color);
    font-family: var(--vxe-ui-font-family);
    direction: ltr;
}
.vxe-table--render-default .vxe-table--body-wrapper {
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-table--render-default .vxe-table--footer-wrapper {
    margin-top: calc(var(--vxe-ui-table-border-width) * -1);
    background-color: var(--vxe-ui-table-footer-background-color);
}
.vxe-table--render-default .vxe-table--body,
.vxe-table--render-default .vxe-table--footer,
.vxe-table--render-default .vxe-table--header {
    border: 0;
    border-spacing: 0;
    border-collapse: separate;
    table-layout: fixed;
}
.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper {
    outline: 0;
}
.vxe-table--render-default.col-drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.col-drag--resize .vxe-table--fixed-right-wrapper *,
.vxe-table--render-default.col-drag--resize .vxe-table--main-wrapper * {
    cursor: col-resize;
}
.vxe-table--render-default.row-drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.row-drag--resize .vxe-table--fixed-right-wrapper *,
.vxe-table--render-default.row-drag--resize .vxe-table--main-wrapper * {
    cursor: row-resize;
}
.vxe-table--render-default.drag--area .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-right-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-right-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--main-wrapper * {
    cursor: default;
}
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-right-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--main-wrapper * {
    cursor: crosshair;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active {
    cursor: grab;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active:active {
    cursor: grabbing;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active:hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
}
.vxe-table--render-default.body-cell--area .vxe-table--body-wrapper,
.vxe-table--render-default.checkbox--range .vxe-body--column.col--checkbox,
.vxe-table--render-default.drag--range .vxe-body--column,
.vxe-table--render-default.header-cell--area .vxe-table--header-wrapper {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-body--column.col--center,
.vxe-table--render-default .vxe-footer--column.col--center,
.vxe-table--render-default .vxe-header--column.col--center {
    text-align: center;
}
.vxe-table--render-default .vxe-body--column.col--center > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--center > .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--center > .vxe-cell {
    justify-content: center;
}
.vxe-table--render-default .vxe-body--column.col--right,
.vxe-table--render-default .vxe-footer--column.col--right,
.vxe-table--render-default .vxe-header--column.col--right {
    text-align: right;
}
.vxe-table--render-default .vxe-body--column.col--right > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--right > .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--right > .vxe-cell {
    justify-content: right;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell--wrapper {
    justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell--wrapper {
    justify-content: right;
}
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--center .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell {
    justify-content: center;
}
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--right .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell {
    justify-content: flex-end;
}
.vxe-table--render-default .vxe-body--row.row--stripe > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-striped-background-color);
}
.vxe-table--render-default.column--highlight .vxe-header--column:hover {
    background-color: var(--vxe-ui-table-column-hover-background-color);
}
.vxe-table--render-default.column--highlight .vxe-header--column:hover.col--current {
    background-color: var(--vxe-ui-table-column-hover-current-background-color);
}
.vxe-table--render-default .vxe-body--column,
.vxe-table--render-default .vxe-footer--column,
.vxe-table--render-default .vxe-header--column {
    position: relative;
    line-height: var(--vxe-ui-table-row-line-height);
    text-align: left;
}
.vxe-table--render-default .vxe-body--column.col--current,
.vxe-table--render-default .vxe-footer--column.col--current,
.vxe-table--render-default .vxe-header--column.col--current {
    background-color: var(--vxe-ui-table-column-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--radio > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--checked > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--current > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover > .vxe-body--column.col--current {
    background-color: var(--vxe-ui-table-column-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-hover-striped-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--radio > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-hover-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--checked > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-hover-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--current > .vxe-body--column {
    background-color: var(--vxe-ui-table-row-hover-current-background-color);
}
.vxe-table--render-default .vxe-table--footer-wrapper {
    border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--inner .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--inner .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--outer .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--outer .vxe-table--scroll-y-top-corner:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    border-width: 0;
    border-style: solid;
    border-color: var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--inner .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--outer .vxe-table--scroll-y-top-corner:before {
    border-bottom-width: var(--vxe-ui-table-border-width);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner,
.vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner,
.vxe-table--render-default.border--inner .vxe-table--scroll-y-bottom-corner,
.vxe-table--render-default.border--outer .vxe-table--scroll-y-bottom-corner {
    border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--full .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--inner .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--outer .vxe-table--scroll-x-wrapper:after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}
.vxe-table--render-default.border--default.sx-pos--top .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--full.sx-pos--top .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--inner.sx-pos--top .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--outer.sx-pos--top .vxe-table--scroll-x-wrapper:after {
    top: 0;
    border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default.sx-pos--bottom .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--full.sx-pos--bottom .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--inner.sx-pos--bottom .vxe-table--scroll-x-wrapper:after,
.vxe-table--render-default.border--outer.sx-pos--bottom .vxe-table--scroll-x-wrapper:after {
    bottom: 0;
    height: calc(100% + var(--vxe-ui-table-border-width));
    border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--default .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--inner .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--inner .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--none .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--none .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--outer .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--outer .vxe-cell--col-resizable:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--inner .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--none .vxe-cell--col-resizable:before,
.vxe-table--render-default.border--outer .vxe-cell--col-resizable:before {
    width: 1px;
    height: 50%;
    background-color: var(--vxe-ui-table-resizable-line-color);
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--inner .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--none .vxe-cell--col-resizable:after,
.vxe-table--render-default.border--outer .vxe-cell--col-resizable:after {
    width: 0;
    height: 100%;
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper,
.vxe-table--render-default.border--full .vxe-table--header-wrapper,
.vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table--render-default.border--default .vxe-body--column,
.vxe-table--render-default.border--default .vxe-footer--column,
.vxe-table--render-default.border--default .vxe-header--column,
.vxe-table--render-default.border--inner .vxe-body--column,
.vxe-table--render-default.border--inner .vxe-footer--column,
.vxe-table--render-default.border--inner .vxe-header--column {
    background-image: linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color));
    background-repeat: no-repeat;
    background-size: 100% var(--vxe-ui-table-border-width);
    background-position: 100% 100%;
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner:before {
    border-left-width: var(--vxe-ui-table-border-width);
    border-right-width: var(--vxe-ui-table-border-width);
}
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-top-corner:before,
.vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-bottom-corner:before,
.vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-top-corner:before {
    width: calc(100% + 1px);
    left: -1px;
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-wrapper:after,
.vxe-table--render-default.border--full .vxe-table--scroll-y-wrapper:after {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}
.vxe-table--render-default.border--default.sy-pos--left .vxe-table--scroll-y-wrapper:after,
.vxe-table--render-default.border--full.sy-pos--left .vxe-table--scroll-y-wrapper:after {
    left: 0;
    border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-wrapper:after,
.vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-wrapper:after {
    right: 0;
    width: calc(100% + var(--vxe-ui-table-border-width));
    border-left: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--full .vxe-body--column,
.vxe-table--render-default.border--full .vxe-footer--column,
.vxe-table--render-default.border--full .vxe-header--column {
    background-image: linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color)),
        linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color));
    background-repeat: no-repeat;
    background-size: var(--vxe-ui-table-border-width) 100%, 100% var(--vxe-ui-table-border-width);
    background-position: 100% 0, 100% 100%;
}
.vxe-table--render-default.border--full .vxe-table--fixed-left-wrapper .vxe-body--column {
    border-right-color: var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--inner .vxe-table--header-wrapper,
.vxe-table--render-default.border--none .vxe-table--header-wrapper {
    background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table--render-default.border--inner .vxe-table--fixed-left-wrapper,
.vxe-table--render-default.border--none .vxe-table--fixed-left-wrapper {
    border-right: 0;
}
.vxe-table--render-default.border--inner .vxe-table--border-line {
    border-width: 0 0 1px 0;
}
.vxe-table--render-default.border--none .vxe-table--border-line {
    display: none;
}
.vxe-table--render-default.size--medium {
    font-size: var(--vxe-ui-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-table--empty-block,
.vxe-table--render-default.size--medium .vxe-table--empty-placeholder {
    min-height: var(--vxe-ui-table-row-height-medium);
}
.vxe-table--render-default.size--medium .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--medium .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-footer--column.is--padding .vxe-cell,
.vxe-table--render-default.size--medium .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--medium .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea {
    padding: var(--vxe-ui-table-cell-padding-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-select,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-textarea {
    height: var(--vxe-ui-input-height-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input[type='date']::-webkit-inner-spin-button {
    margin-top: 3px;
}
.vxe-table--render-default.size--medium .vxe-cell--valid-error-tip {
    padding: 0 var(--vxe-ui-table-cell-padding-medium);
}
.vxe-table--render-default.size--small {
    font-size: var(--vxe-ui-font-size-small);
}
.vxe-table--render-default.size--small .vxe-table--empty-block,
.vxe-table--render-default.size--small .vxe-table--empty-placeholder {
    min-height: var(--vxe-ui-table-row-height-small);
}
.vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-footer--column.is--padding .vxe-cell,
.vxe-table--render-default.size--small .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--small .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea {
    padding: var(--vxe-ui-table-cell-padding-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-select,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-textarea {
    height: var(--vxe-ui-input-height-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input[type='date']::-webkit-inner-spin-button {
    margin-top: 2px;
}
.vxe-table--render-default.size--small .vxe-cell--valid-error-tip {
    padding: 0 var(--vxe-ui-table-cell-padding-small);
}
.vxe-table--render-default.size--mini {
    font-size: var(--vxe-ui-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-table--empty-block,
.vxe-table--render-default.size--mini .vxe-table--empty-placeholder {
    min-height: var(--vxe-ui-table-row-height-mini);
}
.vxe-table--render-default.size--mini .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--mini .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-footer--column.is--padding .vxe-cell,
.vxe-table--render-default.size--mini .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--mini .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea {
    padding: var(--vxe-ui-table-cell-padding-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-select,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-textarea {
    height: var(--vxe-ui-input-height-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input[type='date']::-webkit-inner-spin-button {
    margin-top: 1px;
}
.vxe-table--render-default.size--mini .vxe-cell--valid-error-tip {
    padding: 0 var(--vxe-ui-table-cell-padding-mini);
}
.vxe-table--render-default .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default .vxe-footer--column.is--padding .vxe-cell,
.vxe-table--render-default .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea {
    padding: var(--vxe-ui-table-cell-padding-default);
}
.vxe-table--render-default .vxe-cell {
    white-space: pre-line;
    word-break: break-all;
}
.vxe-table--render-default .vxe-cell--placeholder {
    color: var(--vxe-ui-table-cell-placeholder-color);
}
.vxe-table--render-default .vxe-cell--radio {
    cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    font-size: 1.4em;
    color: var(--vxe-ui-input-border-color);
    vertical-align: middle;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-cell--radio.is--checked,
.vxe-table--render-default .vxe-cell--radio.is--checked .vxe-radio--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled) {
    cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--radio.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--radio.is--disabled .vxe-radio--icon {
    color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--label {
    padding-left: 0.5em;
    vertical-align: middle;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    font-size: 1.34em;
    color: var(--vxe-ui-input-border-color);
    vertical-align: middle;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-cell--checkbox.is--checked,
.vxe-table--render-default .vxe-cell--checkbox.is--checked .vxe-checkbox--icon,
.vxe-table--render-default .vxe-cell--checkbox.is--indeterminate,
.vxe-table--render-default .vxe-cell--checkbox.is--indeterminate .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled) {
    cursor: pointer;
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox.is--hidden {
    cursor: default;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled {
    color: var(--vxe-ui-font-disabled-color);
    cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled .vxe-checkbox--icon {
    color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--label {
    padding-left: 0.5em;
    vertical-align: middle;
}
.vxe-table--render-default .fixed--hidden {
    visibility: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
    width: 100%;
    position: absolute;
    top: 0;
    z-index: 5;
    overflow: hidden;
    background-color: inherit;
    transition: box-shadow 0.3s;
    outline: 0;
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper {
    outline: 0;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper {
    width: calc(100% + 40px);
}
.vxe-table--render-default.is--header .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper:before,
.vxe-table--render-default.is--header .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper:before {
    display: none;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper {
    left: 0;
    width: 200px;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper.scrolling--middle {
    box-shadow: var(--vxe-ui-table-fixed-left-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
    right: 0;
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper.scrolling--middle {
    box-shadow: var(--vxe-ui-table-fixed-right-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper,
.vxe-table--render-default .vxe-table--header-wrapper {
    position: relative;
    width: 100%;
}
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper {
    position: absolute;
    top: 0;
    outline: 0;
}
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper {
    left: 0;
}
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper {
    right: 0;
}
.vxe-table--render-default .vxe-body--x-space {
    width: 100%;
    height: 1px;
    margin-bottom: -1px;
}
.vxe-table--render-default .vxe-body--y-space {
    width: 0;
    float: left;
}
.vxe-table--render-default .vxe-table--resizable-col-bar,
.vxe-table--render-default .vxe-table--resizable-row-bar {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-table--resizable-col-bar:before,
.vxe-table--render-default .vxe-table--resizable-row-bar:before {
    content: '';
    display: block;
    background-color: var(--vxe-ui-table-resizable-drag-line-color);
}
.vxe-table--render-default .vxe-table--resizable-col-bar .vxe-table--resizable-number-tip,
.vxe-table--render-default .vxe-table--resizable-row-bar .vxe-table--resizable-number-tip {
    position: absolute;
    padding: 0.25em 0.25em;
    font-size: 12px;
    border-radius: var(--vxe-ui-border-radius);
    white-space: nowrap;
    color: #fff;
    background-color: var(--vxe-ui-table-resizable-drag-line-color);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
}
.vxe-table--render-default .vxe-table--resizable-col-bar {
    width: 1px;
    height: 100%;
    cursor: col-resize;
}
.vxe-table--render-default .vxe-table--resizable-col-bar:before {
    width: 1px;
    height: 100%;
}
.vxe-table--render-default .vxe-table--resizable-col-bar .vxe-table--resizable-number-tip {
    left: 0;
    top: 1em;
}
.vxe-table--render-default .vxe-table--resizable-row-bar {
    height: 1px;
    width: 100%;
    cursor: row-resize;
}
.vxe-table--render-default .vxe-table--resizable-row-bar:before {
    height: 1px;
    width: 100%;
}
.vxe-table--render-default .vxe-table--resizable-row-bar .vxe-table--resizable-number-tip {
    top: 0;
    left: 0;
}
.vxe-table--render-default .vxe-table--border-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none;
    border: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-tree--line-wrapper {
    position: relative;
    display: block;
    height: 0;
}
.vxe-table--render-default .vxe-tree--line {
    content: '';
    position: absolute;
    bottom: -1.5em;
    width: 0.8em;
    border-width: 0 0 1px 1px;
    border-style: var(--vxe-ui-table-tree-node-line-style);
    border-color: var(--vxe-ui-table-tree-node-line-color);
    pointer-events: none;
}
.vxe-table--render-default .vxe-cell--tree-node,
.vxe-table--render-default .vxe-row-group--tree-node {
    position: relative;
}
.vxe-table--render-default .vxe-cell--tree-btn:hover {
    color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-cell--tree-btn > i {
    display: block;
    color: var(--vxe-ui-font-lighten-color);
    transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-row-group-cell,
.vxe-table--render-default .vxe-tree-cell {
    display: block;
    padding-left: 1.5em;
}
.vxe-table--render-default .vxe-cell--tree-btn,
.vxe-table--render-default .vxe-row-group--node-btn {
    position: absolute;
    top: 50%;
    width: 1em;
    height: 1em;
    text-align: center;
    transform: translateY(-50%);
    z-index: 1;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: pointer;
}
.vxe-table--render-default .vxe-row-group--node-btn:hover {
    color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-row-group--node-btn > i {
    display: block;
    color: var(--vxe-ui-font-lighten-color);
    transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-row-group-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-body--column.col--auto-height,
.vxe-table--render-default .vxe-body--column.col--cs-height,
.vxe-table--render-default .vxe-body--column.col--rs-height {
    overflow: hidden;
}
.vxe-table--render-default .vxe-body--column.col--auto-height.col--tree-node,
.vxe-table--render-default .vxe-body--column.col--auto-height.col--valid-error,
.vxe-table--render-default .vxe-body--column.col--cs-height.col--tree-node,
.vxe-table--render-default .vxe-body--column.col--cs-height.col--valid-error,
.vxe-table--render-default .vxe-body--column.col--rs-height.col--tree-node,
.vxe-table--render-default .vxe-body--column.col--rs-height.col--valid-error {
    overflow: unset;
}
.vxe-table--render-default .vxe-body--column.col--auto-height > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--cs-height > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--rs-height > .vxe-cell {
    overflow: hidden;
}
.vxe-table--render-default .vxe-body--column > .vxe-cell {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell {
    overflow: hidden;
}
.vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-body--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-header--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-header--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-header--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper {
    white-space: pre-line;
}
.vxe-table--render-default .vxe-footer--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper,
.vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-footer--column > .vxe-cell,
.vxe-table--render-default .vxe-header--column > .vxe-cell {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-table--render-default .vxe-table--row-expanded-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}
.vxe-table--render-default .vxe-body--row-expanded-cell {
    position: absolute;
    z-index: 5;
    top: 0;
    left: 0;
    width: 100%;
    overflow: auto;
    background-color: var(--vxe-ui-layout-background-color);
    pointer-events: all;
}
.vxe-table--render-default .vxe-body--row-expanded-cell.is--padding {
    padding: var(--vxe-ui-table-expand-padding-default);
}
.vxe-table--render-default .vxe-body--row-expanded-place-column {
    border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
    border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-table--expanded {
    cursor: pointer;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
    display: inline-block;
    width: 1em;
    height: 1em;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    color: var(--vxe-ui-font-lighten-color);
    transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn:hover {
    color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-table--expanded + .vxe-table--expand-label {
    padding-left: 0.5em;
}
.vxe-table--render-default .vxe-body--expanded-row.is--padding > .vxe-body--expanded-column > .vxe-body--expanded-cell {
    padding: var(--vxe-ui-table-expand-padding-default);
}
.vxe-table--render-default .vxe-body--expanded-column {
    border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
    border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-body--expanded-column.col--ellipsis > .vxe-body--expanded-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-body--expanded-cell {
    position: relative;
    z-index: 1;
}
.vxe-table--render-default .vxe-body--expanded-cell.is--ellipsis {
    overflow: auto;
    outline: 0;
}
.vxe-table--render-default .vxe-table--drag-col-line {
    height: 100%;
    width: 1px;
    border: 2px solid transparent;
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-pos='left'] {
    border-left-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-pos='right'] {
    border-right-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-to-child='y'] {
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-color: var(--vxe-ui-status-success-color);
}
.vxe-table--render-default .vxe-table--drag-col-line.is--guides {
    background-color: var(--vxe-ui-table-drag-over-background-color);
}
.vxe-table--render-default .vxe-body--column.col--drag-origin > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--drag-origin > .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--drag-origin > .vxe-cell {
    opacity: 0.5;
}
.vxe-table--render-default .vxe-header--col-list-move {
    transition-property: transform;
    transition-duration: 0.35s;
}
.vxe-table--render-default .vxe-table--drag-col-line,
.vxe-table--render-default .vxe-table--drag-row-line {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 11;
    pointer-events: none;
}
.vxe-table--render-default .vxe-cell--drag-handle {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-cell--drag-handle + span {
    padding-left: 0.5em;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled) {
    cursor: grab;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled):active {
    cursor: grabbing;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled):hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--drag-handle.is--disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
}
.vxe-table--render-default .vxe-table--drag-row-line {
    width: 100%;
    height: 1px;
    border: 2px solid transparent;
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-pos='top'] {
    border-top-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-pos='bottom'] {
    border-bottom-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-to-child='y'] {
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: var(--vxe-ui-status-success-color);
}
.vxe-table--render-default .vxe-table--drag-row-line.is--guides {
    background-color: var(--vxe-ui-table-drag-over-background-color);
}
.vxe-table--render-default .vxe-body--row.row--drag-origin > .vxe-body--column > .vxe-cell {
    opacity: 0.5;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled) {
    cursor: grab;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled):active {
    cursor: grabbing;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled):hover {
    color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-body--column.is--drag-cell.is--drag-disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
}
.vxe-table--render-default .vxe-body--row-list-move {
    transition-property: transform;
    transition-duration: 0.35s;
}
.vxe-table--render-default .vxe-table--drag-sort-tip {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0.6em 1.4em;
    max-width: 50%;
    min-width: 100px;
    border-radius: var(--vxe-ui-border-radius);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    background-color: var(--vxe-ui-layout-background-color);
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    z-index: 33;
}
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status='disabled'] .vxe-table--drag-sort-tip-disabled-status,
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status='normal'] .vxe-table--drag-sort-tip-normal-status,
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status='sub'] .vxe-table--drag-sort-tip-sub-status {
    display: block;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-status {
    padding-right: 0.4em;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-disabled-status {
    display: none;
    flex-shrink: 0;
    color: var(--vxe-ui-status-error-color);
}
.vxe-table--render-default .vxe-table--drag-sort-tip-normal-status,
.vxe-table--render-default .vxe-table--drag-sort-tip-sub-status {
    display: none;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default.size--medium .vxe-cell--checkbox {
    font-size: var(--vxe-checkbox-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-cell--radio {
    font-size: var(--vxe-radio-font-size-medium);
}
.vxe-table--render-default.size--small .vxe-cell--checkbox {
    font-size: var(--vxe-checkbox-font-size-small);
}
.vxe-table--render-default.size--small .vxe-cell--radio {
    font-size: var(--vxe-radio-font-size-small);
}
.vxe-table--render-default.size--mini .vxe-cell--checkbox {
    font-size: var(--vxe-checkbox-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-cell--radio {
    font-size: var(--vxe-radio-font-size-mini);
}
.vxe-table--render-default .vxe-table--empty-block,
.vxe-table--render-default .vxe-table--empty-placeholder {
    color: var(--vxe-ui-input-placeholder-color);
    min-height: var(--vxe-ui-table-row-height-default);
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
    width: 100%;
    pointer-events: none;
    outline: 0;
}
.vxe-table--render-default .vxe-table--empty-block {
    display: none;
    visibility: hidden;
}
.vxe-table--render-default .vxe-table--empty-placeholder {
    display: none;
    position: absolute;
    top: 0;
    z-index: 5;
}
.vxe-table--render-default .vxe-table--empty-content {
    display: block;
    width: 50%;
    pointer-events: auto;
}
.vxe-table--render-default.is--empty .vxe-table--empty-block,
.vxe-table--render-default.is--empty .vxe-table--empty-placeholder {
    display: flex;
}
.vxe-table--render-default .vxe-body--column.col--selected {
    box-shadow: inset 0 0 0 2px var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-tip {
    width: 100%;
    position: absolute;
    left: 50%;
    font-size: 12px;
    line-height: 1.2em;
    transform: translateX(-50%);
    text-align: left;
    z-index: 4;
    padding: 0 var(--vxe-ui-table-cell-padding-default);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-wrapper {
    display: inline-block;
    border-radius: var(--vxe-ui-border-radius);
    pointer-events: auto;
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-beautify {
    padding: 0.2em 0.6em 0.3em 0.6em;
    color: #fff;
    background-color: var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-beautify .vxe-cell--valid-error-msg {
    background: transparent;
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-normal {
    color: var(--vxe-ui-table-validate-error-color);
    background-color: var(--vxe-ui-table-validate-error-background-color);
}
.vxe-table--render-default .vxe-body--column.col--active,
.vxe-table--render-default .vxe-body--column.col--selected {
    position: relative;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-input,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-select,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-textarea,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-ico-picker,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-input {
    border-color: var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child .vxe-cell--valid-error-tip {
    bottom: 100%;
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child:first-child .vxe-cell--valid-error-tip {
    bottom: auto;
}
.vxe-table--render-default.valid-msg--full .vxe-body--row:last-child .vxe-cell--valid-error-tip {
    top: calc(100% - 1.3em);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column.col--valid-error .vxe-cell--valid-error-tip {
    width: 320px;
    position: absolute;
    bottom: calc(100% + 4px);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    pointer-events: none;
    z-index: 4;
}
.vxe-table--render-default.old-cell-valid
    .vxe-body--column.col--valid-error
    .vxe-cell--valid-error-tip
    .vxe-cell--valid-error-msg {
    display: inline-block;
    border-radius: -var(-vxe-border-radius);
    padding: 8px 12px;
    color: #fff;
    background-color: #f56c6c;
    pointer-events: auto;
}
.vxe-table--render-default.old-cell-valid .vxe-body--row:first-child .vxe-cell--valid-error-tip {
    bottom: auto;
    top: calc(100% + 4px);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column:first-child .vxe-cell--valid-error-tip {
    left: 10px;
    transform: translateX(0);
    text-align: left;
}
.vxe-table--render-default .vxe-body--row.row--pending {
    color: var(--vxe-ui-table-validate-error-color);
    text-decoration: line-through;
    cursor: no-drop;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column {
    position: relative;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0;
    border-bottom: 1px solid var(--vxe-ui-table-validate-error-color);
    z-index: 1;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column {
    position: relative;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column:before {
    content: '';
    top: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
    left: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
    position: absolute;
    border-width: var(--vxe-ui-table-cell-dirty-width);
    border-style: solid;
    border-color: transparent var(--vxe-ui-table-cell-dirty-insert-color) transparent transparent;
    transform: rotate(45deg);
}
.vxe-table--render-default .vxe-body--column.col--dirty {
    position: relative;
}
.vxe-table--render-default .vxe-body--column.col--dirty:before {
    content: '';
    top: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
    left: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
    position: absolute;
    border-width: var(--vxe-ui-table-cell-dirty-width);
    border-style: solid;
    border-color: transparent var(--vxe-ui-table-cell-dirty-update-color) transparent transparent;
    transform: rotate(45deg);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active {
    box-shadow: inset 0 0 0 2px var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active.col--valid-error {
    box-shadow: inset 0 0 0 2px var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-input,
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-textarea {
    border: 0;
    padding: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight
    .vxe-body--column.col--active
    .vxe-cell
    .vxe-input
    .vxe-input--inner {
    border: 0;
    padding-left: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column {
    padding: 0;
}
div.vxe-table--tooltip-wrapper.vxe-table--valid-error {
    padding: 0;
    color: var(--vxe-ui-table-validate-error-color);
    background-color: var(--vxe-ui-table-validate-error-background-color);
}
div.vxe-table--tooltip-wrapper.vxe-table--valid-error.old-cell-valid {
    padding: 8px 12px;
    background-color: #f56c6c;
    color: #fff;
}
.vxe-table--footer-wrapper {
    color: var(--vxe-ui-table-footer-font-color);
}
.vxe-table--footer-wrapper.body--wrapper {
    outline: 0;
}
.vxe-grid {
    position: relative;
    overflow: auto;
    display: flex;
    flex-direction: column;
}
.vxe-grid.is--loading:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: var(--vxe-ui-loading-background-color);
}
.vxe-grid.is--loading > .vxe-table .vxe-loading {
    background-color: transparent;
}
.vxe-grid.is--maximize {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0.5em 1em;
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-grid .vxe-grid--bottom-wrapper,
.vxe-grid .vxe-grid--form-wrapper,
.vxe-grid .vxe-grid--top-wrapper {
    position: relative;
}
.vxe-grid .vxe-grid--table-container {
    display: flex;
    flex-direction: row;
}
.vxe-grid .vxe-grid--left-wrapper,
.vxe-grid .vxe-grid--right-wrapper {
    flex-shrink: 0;
    overflow: auto;
    outline: 0;
}
.vxe-grid .vxe-grid--table-wrapper {
    flex-grow: 1;
    overflow: hidden;
}
.vxe-grid--layout-body-wrapper {
    display: flex;
    flex-direction: row;
    overflow: auto;
    flex-grow: 1;
}
.vxe-grid--layout-body-content-wrapper {
    flex-grow: 1;
    overflow: auto;
}
.vxe-grid--layout-aside-left-wrapper,
.vxe-grid--layout-footer-wrapper,
.vxe-grid--layout-header-wrapper {
    flex-shrink: 0;
    overflow: auto;
}
.vxe-grid {
    font-size: var(--vxe-ui-font-size-default);
}
.vxe-grid.size--medium {
    font-size: var(--vxe-ui-font-size-medium);
}
.vxe-grid.size--small {
    font-size: var(--vxe-ui-font-size-small);
}
.vxe-grid.size--mini {
    font-size: var(--vxe-ui-font-size-mini);
}
.vxe-toolbar {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.6em 0;
    color: var(--vxe-ui-font-color);
    font-family: var(--vxe-ui-font-family);
    background-color: var(--vxe-ui-layout-background-color);
}
.vxe-toolbar:after {
    content: '';
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-toolbar.is--perfect {
    border: 1px solid var(--vxe-ui-table-border-color);
    border-bottom-width: 0;
    background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-toolbar.is--loading:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: var(--vxe-ui-loading-background-color);
}
.vxe-toolbar .vxe-buttons--wrapper {
    flex-grow: 1;
}
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button + .vxe-button--item,
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item + .vxe-button,
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item + .vxe-button--item {
    margin-left: 0.8em;
}
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item {
    display: inline-block;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-button + .vxe-tool--item,
.vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item + .vxe-button,
.vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item + .vxe-tool--item {
    margin-left: 0.8em;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item {
    display: inline-block;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-button {
    display: flex;
    align-items: center;
    justify-content: center;
}
.vxe-toolbar .vxe-buttons--wrapper,
.vxe-toolbar .vxe-tools--operate,
.vxe-toolbar .vxe-tools--wrapper {
    display: flex;
    align-items: center;
}
.vxe-toolbar .vxe-buttons--wrapper,
.vxe-toolbar .vxe-tools--wrapper {
    flex-wrap: wrap;
}
.vxe-toolbar .vxe-tools--operate {
    flex-shrink: 0;
}
.vxe-toolbar .vxe-custom--wrapper {
    position: relative;
}
.vxe-toolbar .vxe-custom--wrapper.is--active > .vxe-button {
    background-color: var(--vxe-ui-toolbar-custom-active-background-color);
    border-radius: 50%;
}
.vxe-toolbar {
    font-size: var(--vxe-ui-font-size-default);
}
.vxe-toolbar.size--medium {
    font-size: var(--vxe-ui-font-size-medium);
}
.vxe-toolbar.size--small {
    font-size: var(--vxe-ui-font-size-small);
}
.vxe-toolbar.size--mini {
    font-size: var(--vxe-ui-font-size-mini);
}
