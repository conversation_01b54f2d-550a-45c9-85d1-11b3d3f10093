.alert-dialog-custom-theme {
    overflow: hidden;
    &::before {
        content: '';
        background: linear-gradient(0deg, #ffffff 7%, #eaf1ff 74%);
        width: 100%;
        height: 50px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 0;
    }
    .n-dialog__close {
        color: #fff;
        border-radius: 100%;
        font-size: 13px;
        background: linear-gradient(272deg, #4a9bff 6%, #005eff 98%);
        &:hover {
            background: linear-gradient(0deg, #4a9bff 6%, #005eff 98%);
            color: #fff !important;
        }
    }
    .n-dialog__content {
        position: relative;
        overflow-x: hidden;
        max-height: var(--alert-dialog-custom-theme-content-height);
        .n-form-item-blank {
            & * {
                --n-text-color-disabled: var(--n-text-color);
            }
        }
        .useFormDialog {
            &.n-form {
                @borderColor: 1px solid #ebebeb;
                @h: 34px;
                --n-height: @h;
                .n-form-item-label {
                    background: #f9fafc;
                    color: #181616;
                    font-size: 14px;
                    font-weight: bold;
                    border: @borderColor;
                    border-radius: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 0 12px;
                    height: 100%;
                    .n-form-item-label__text {
                        height: @h;
                        display: flex;
                        align-items: center;
                    }
                    & + .n-form-item-blank {
                        border-left: 0;
                    }
                    .n-form-item-label__asterisk {
                        align-self: center;
                    }
                }
                .n-form-item-blank {
                    border: @borderColor;
                    height: 100%;
                    & * {
                        --n-border: 1px solid #fff0 !important;
                        --n-border-disabled: none !important;
                        --n-dot-color-disabled: var(--n-dot-color-active);
                        --n-text-color-disabled: var(--n-text-color);
                        --n-placeholder-color-disabled: var(--n-placeholder-color);
                        --n-color-disabled: var(--n-color);
                    }
                    .n-checkbox {
                        * {
                            --n-border: 1px solid rgb(224, 224, 230) !important;
                        }
                    }
                }
                .whiteFormTheme,
                &.whiteFormTheme {
                    .n-form-item-label {
                        background: #fff;
                        border: 0;
                    }
                    .n-form-item-blank {
                        border: 0;
                    }
                }
                .n-form-item {
                    &.notBorder {
                        .n-form-item-blank {
                            border: 0;
                        }
                    }
                }
            }
        }
    }
}
