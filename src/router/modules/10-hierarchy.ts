import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: 'hierarchy',
        name: 'Hierarchy',
        meta: {
            title: '体系管理'
        },
        redirect: '/hierarchy/file-management',
        children: [
            {
                path: 'file-management',
                name: 'FileManagement',
                meta: {
                    title: '文件管理',
                    icon: 'menus-library'
                },
                redirect: '/hierarchy/file-management/internal-file-library',
                children: [
                    {
                        path: 'internal-file-library',
                        name: 'InternalFileLibrary',
                        meta: {
                            title: '内部文件库',
                            permissions: [
                                { name: '新增', code: 'internalFileAdd' },
                                { name: '导入', code: 'internalFileImport' },
                                { name: '导出', code: 'internalFileExport' },
                                { name: '修订', code: 'internalFileRevision' },
                                { name: '详情', code: 'internalFileDetail' },
                                { name: '发放回收', code: 'internalFileDistribute' },
                                { name: '作废', code: 'internalFileInvalid' },
                                { name: '借阅', code: 'internalFileBorrow' },
                                { name: '查阅', code: 'internalFileView' },
                                { name: '下载', code: 'internalFileDownload' },
                                { name: '变更记录', code: 'internalFileChangeLog' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/internal-file-library/index.vue'),
                        children: [
                            {
                                path: 'distribute-file',
                                name: 'DistributeFile',
                                meta: {
                                    title: '发放回收',
                                    hidden: true
                                },
                                component: () => import('@/views/hierarchy/file-management/models/distribute-file.vue')
                            }
                        ]
                    },
                    {
                        path: 'external-file-library',
                        name: 'ExternalFileLibrary',
                        meta: {
                            title: '外部文件库',
                            permissions: [
                                { name: '集团库Tab', code: 'externalFileGroupTab' },
                                { name: '集团库新增', code: 'externalFileGroupAdd' },
                                { name: '集团库导入', code: 'externalFileGroupImport' },
                                { name: '集团库导出', code: 'externalFileGroupExport' },
                                { name: '集团库批量纳入', code: 'externalFileGroupBatchInclude' },
                                { name: '集团库修订', code: 'externalFileGroupRevision' },
                                { name: '集团库详情', code: 'externalFileGroupDetail' },
                                { name: '集团库发放回收', code: 'externalFileGroupDistribute' },
                                { name: '集团库纳入子公司', code: 'externalFileGroupInclude' },
                                { name: '集团库作废', code: 'externalFileGroupInvalid' },
                                { name: '集团库借阅', code: 'externalFileGroupBorrow' },
                                { name: '集团库查阅', code: 'externalFileGroupView' },
                                { name: '集团库下载', code: 'externalFileGroupDownload' },
                                { name: '集团库变更记录', code: 'externalFileGroupChangeLog' },
                                { name: '公司库Tab', code: 'externalFileCompanyTab' },
                                { name: '公司库导出', code: 'externalFileCompanyExport' },
                                { name: '公司库详情', code: 'externalFileCompanyDetail' },
                                { name: '公司库发放回收', code: 'externalFileCompanyDistribute' },
                                { name: '公司库作废', code: 'externalFileCompanyInvalid' },
                                { name: '公司库借阅', code: 'externalFileCompanyBorrow' },
                                { name: '公司库查阅', code: 'externalFileCompanyView' },
                                { name: '公司库下载', code: 'externalFileCompanyDownload' },
                                { name: '公司库变更记录', code: 'externalFileCompanyChangeLog' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/external-file-library/index.vue')
                    },

                    {
                        path: 'book-library',
                        name: 'BookLibrary',
                        meta: {
                            title: '书籍库',
                            permissions: [
                                { name: '新增', code: 'bookLibraryAdd' },
                                { name: '导入', code: 'bookLibraryImport' },
                                { name: '导出', code: 'bookLibraryExport' },
                                { name: '编辑', code: 'bookLibraryEdit' },
                                { name: '删除', code: 'bookLibraryDelete' },
                                { name: '预览', code: 'bookLibraryPreview' },
                                { name: '领用', code: 'bookLibraryReceive' },
                                { name: '借用', code: 'bookLibraryBorrow' },
                                { name: '领用归还', code: 'bookLibraryReceiveReturn' },
                                { name: '借用归还', code: 'bookLibraryBorrowReturn' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/book-library/index.vue')
                    },
                    {
                        path: 'issuance-application',
                        name: 'IssuanceApplication',
                        meta: {
                            title: '发放回收和处置',
                            permissions: [
                                { name: '新增', code: 'issuanceApplicationAdd' },
                                { name: '导出', code: 'issuanceApplicationExport' },
                                { name: '纸质文件处置', code: 'issuanceApplicationPaperDispose' },
                                { name: '发放回收详情', code: 'issuanceApplicationDistributeDetail' },
                                { name: '纸质文件处置详情', code: 'issuanceApplicationPaperDetail' },
                                { name: '撤销', code: 'issuanceApplicationCancel' },
                                { name: '编辑', code: 'issuanceApplicationEdit' },
                                { name: '回收', code: 'issuanceApplicationRecycle' },
                                { name: '删除', code: 'issuanceApplicationDelete' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/issuance-application/index.vue')
                    },
                    {
                        path: 'borrowing-application',
                        name: 'BorrowingApplication',
                        meta: {
                            title: '借阅和交还',
                            permissions: [
                                { name: '新增', code: 'borrowingApplicationAdd' },
                                { name: '导出', code: 'borrowingApplicationExport' },
                                { name: '编辑', code: 'borrowingApplicationEdit' },
                                { name: '借阅详情', code: 'borrowingApplicationDetail' },
                                { name: '撤销', code: 'borrowingApplicationRevoke' },
                                { name: '回收', code: 'borrowingApplicationRecycle' },
                                { name: '删除', code: 'borrowingApplicationDelete' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/borrowing-application/index.vue')
                    },
                    {
                        path: 'invalid-application',
                        name: 'InvalidApplication',
                        meta: {
                            title: '作废申请',
                            permissions: [
                                { name: '新增', code: 'internalInvalidFileAdd' },
                                { name: '导出', code: 'internalInvalidFileExport' },
                                { name: '撤销', code: 'internalInvalidFileRevoke' },
                                { name: '详情', code: 'internalInvalidFileDetail' },
                                { name: '删除', code: 'internalInvalidFileDelete' }
                            ]
                        },
                        component: () => import('@/views/hierarchy/file-management/invalid-application/index.vue')
                    },
                    {
                        path: 'internal-invalid-file-library',
                        name: 'InternalInvalidFileLibrary',
                        meta: {
                            title: '内部作废文件库',
                            permissions: [
                                { name: '导出', code: 'internalInvalidFileExport' },
                                { name: '借阅', code: 'internalInvalidFileBorrow' },
                                { name: '作废详情', code: 'internalInvalidFileDetail' }
                            ]
                        },
                        component: () =>
                            import('@/views/hierarchy/file-management/internal-invalid-file-library/index.vue')
                    },
                    {
                        path: 'external-invalid-file-library',
                        name: 'ExternalInvalidFileLibrary',
                        meta: {
                            title: '外部作废文件库',
                            permissions: [
                                { name: '集团库Tab', code: 'externalInvalidFileGroupTab' },
                                { name: '公司库Tab', code: 'externalInvalidFileCompanyTab' },
                                { name: '导出', code: 'externalInvalidFileExport' },
                                { name: '借阅', code: 'externalInvalidFileBorrow' },
                                { name: '作废详情', code: 'externalInvalidFileDetail' }
                            ]
                        },
                        component: () =>
                            import('@/views/hierarchy/file-management/external-invalid-file-library/index.vue')
                    }
                ]
            },
            {
                path: 'institution-management',
                name: 'InstitutionManagement',
                meta: {
                    title: '机构管理',
                    icon: 'menus-orgin'
                },
                component: () => import('@/views/hierarchy/institution-management/index.vue')
            }
        ]
    }
];
export default routes;
