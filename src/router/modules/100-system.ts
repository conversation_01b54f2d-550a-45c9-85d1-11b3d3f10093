import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: 'system',
        name: 'System',
        meta: {
            title: '系统管理'
        },
        redirect: '/system/organization',
        children: [
            {
                path: 'organization',
                name: 'Organization',
                meta: {
                    title: '组织架构',
                    icon: 'menus-setting',
                    permissions: [
                        { name: '新建单位', code: 'addOrganization' },
                        { name: '新建子单位', code: 'addSubOrganization' },
                        { name: '新增部门', code: 'addDepartment' },
                        { name: '编辑组织架构', code: 'editOrganization' },
                        { name: '设置负责人', code: 'setUpLeaders' },
                        { name: '删除组织架构', code: 'deleteDepartment' },
                        { name: '新增用户', code: 'addUser' },
                        { name: '绑定用户', code: 'bindUser' },
                        { name: '人员排序', code: 'userSort' },
                        { name: '编辑用户', code: 'editUser' },
                        { name: '删除用户', code: 'deleteUser' },
                        { name: '批量密码修改', code: 'batchUpdatePassword' },
                        { name: '关闭模态框', code: 'afterLeave' }
                    ]
                },
                component: () => import('@/views/system/organization/index.vue')
            },
            {
                path: 'online-user',
                name: 'OnlineUser',
                meta: {
                    title: '在线用户',
                    icon: 'menus-user',
                    permissions: [{ name: '强制登出', code: 'onlineUserLogout' }]
                },
                component: () => import('@/views/system/online-user/index.vue')
            },
            {
                path: 'position',
                name: 'Position',
                meta: {
                    title: '岗位管理',
                    icon: 'menus-position',
                    permissions: [
                        { name: '新增岗位', code: 'addPosition' },
                        { name: '编辑岗位', code: 'editPosition' },
                        { name: '删除岗位', code: 'deletePosition' }
                    ]
                },
                component: () => import('@/views/system/position/index.vue')
            },
            {
                path: 'menu',
                name: 'Menu',
                meta: {
                    title: '菜单管理',
                    icon: 'menus-menu',
                    permissions: [
                        { name: '新增菜单', code: 'addMenu' },
                        { name: '编辑菜单', code: 'editMenu' },
                        { name: '删除菜单', code: 'deleteMenu' },
                        { name: '按钮设置', code: 'buttonSet' }
                    ]
                },
                component: () => import('@/views/system/menu/index.vue')
            },
            {
                path: 'system-log',
                name: 'SystemLog',
                meta: {
                    title: '日志管理',
                    icon: 'menus-note'
                },
                redirect: '/system/system-log/login-log',
                children: [
                    {
                        path: 'login-log',
                        name: 'loginLog',
                        meta: {
                            title: '登录日志'
                        },
                        component: () => import('@/views/system/system-log/index.vue')
                    },
                    {
                        path: 'operation-log',
                        name: 'operationLog',
                        meta: {
                            title: '操作日志'
                        },
                        component: () => import('@/views/system/system-log/index.vue')
                    }
                ]
            },
            {
                path: 'role',
                name: 'Role',
                meta: {
                    title: '角色管理',
                    icon: 'menus-role',
                    permissions: [
                        { name: '新增角色', code: 'addRole' },
                        { name: '编辑角色', code: 'editRole' },
                        { name: '删除角色', code: 'deleteRole' },
                        { name: '绑定人员', code: 'bindUser' },
                        { name: '菜单权限', code: 'menuAuth' }
                    ]
                },
                component: () => import('@/views/system/role/index.vue')
            },
            {
                path: 'dictionary',
                name: 'Dictionary',
                meta: {
                    title: '字典管理',
                    icon: 'menus-dict',
                    permissions: [
                        { name: '新增字典', code: 'dictionaryAdd' },
                        { name: '编辑字典', code: 'dictionaryEdit' },
                        { name: '查看字典', code: 'dictionaryView' },
                        {
                            name: '新增字典节点',
                            code: 'dictionaryNodeAdd'
                        },
                        {
                            name: '编辑字典节点',
                            code: 'dictionaryNodeEdit'
                        },
                        {
                            name: '删除字典节点',
                            code: 'dictionaryNodeDelete'
                        },
                        {
                            name: '全部展开节点',
                            code: 'dictionaryNodeExpendAll'
                        },
                        { name: '重置字典节点', code: 'dictionaryNodeReset' }
                    ]
                },
                component: () => import('@/views/system/dictionary/index.vue')
            },
            {
                path: 'more-help',
                name: 'MoreHelp',
                meta: {
                    title: '更多帮助',
                    icon: 'menus-more',
                    permissions: [
                        { name: '新增帮助', code: 'addMoreHelp' },
                        { name: '编辑帮助', code: 'editMoreHelp' },
                        { name: '删除帮助', code: 'deleteMoreHelp' }
                    ]
                },
                component: () => import('@/views/system/more-help/index.vue')
            },
            {
                path: 'signature',
                name: 'Signature',
                meta: {
                    title: '签名管理',
                    hidden: true,
                    permissions: [{ name: '签名配置', code: 'signatureConfig' }]
                },
                component: () => import('@/views/system/signature/index.vue')
            },
            {
                path: 'system-dict',
                name: 'SystemDict',
                meta: {
                    title: '系统字典',
                    icon: 'menus-library',
                    permissions: [
                        { name: '新增系统字典', code: 'addSystemDict' },
                        { name: '编辑系统字典', code: 'editSystemDict' },
                        { name: '删除系统字典', code: 'deleteSystemDict' }
                    ]
                },
                component: () => import('@/views/system/system-dict/index.vue')
            },
            {
                path: 'tenant',
                name: 'Tenant',
                meta: {
                    title: '租户管理',
                    icon: 'menus-process1',
                    isActive: false,
                    hidden: true,
                    hiddenInTab: true,
                    permissions: [
                        { name: '用户列表', code: 'userList' },
                        { name: '新增用户', code: 'addUser' },
                        { name: '编辑用户', code: 'editUser' },
                        { name: '删除用户', code: 'deleteUser' },
                        { name: '绑定用户', code: 'bindUser' },
                        { name: '新增租户', code: 'addTenant' },
                        { name: '编辑租户', code: 'editTenant' },
                        { name: '删除租户', code: 'deleteTenant' }
                    ]
                },
                component: () => import('@/views/system/tenant/index.vue')
            }
        ]
    }
];

export default routes;
