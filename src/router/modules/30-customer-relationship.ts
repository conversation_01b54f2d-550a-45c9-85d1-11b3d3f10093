import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    // {
    //     path: 'customer-relationship',
    //     name: 'CustomerRelationship',
    //     meta: {
    //         title: '客户关系管理',
    //         icon: 'menus-setting',
    //     },
    //     component: () => import('@/views/customer-relationship/index.vue'),
    //     children: [
    //         {
    //             path: 'customer',
    //             name: 'Customer',
    //             meta: {
    //                 title: '客户管理',
    //                 icon: 'menus-customer',
    //             },
    //             component: () =>
    //                 import('@/views/customer-relationship/index.vue'),
    //         },
    //         {
    //             path: 'customer-visit',
    //             name: 'CustomerVisit',
    //             meta: {
    //                 title: '客户交互管理',
    //                 icon: 'menus-customer-visit',
    //             },
    //             component: () =>
    //                 import('@/views/customer-relationship/index.vue'),
    //         },
    //         {
    //             path: 'customer-visit/:id',
    //             name: 'CustomerVisitDetail',
    //             meta: {
    //                 title: '销售管理',
    //                 icon: 'menus-customer-visit',
    //             },
    //             component: () =>
    //                 import('@/views/customer-relationship/index.vue'),
    //         },
    //     ],
    // },
];

export default routes;
