import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: 'approval',
        name: 'Approval',
        meta: {
            title: '审批管理'
        },
        redirect: '/approval/approval-process',
        children: [
            {
                path: 'process-preset',
                name: 'ProcessPreset',
                meta: {
                    title: '流程模版管理',
                    icon: 'menus-process2',
                    permissions: [
                        { name: '新增流程', code: 'processPresetAdd' },
                        { name: '编辑流程', code: 'processPresetEdit' },
                        { name: '分发流程', code: 'processPresetDispense' }
                    ]
                },
                component: () => import('@/views/approval/process-preset.vue')
            },
            {
                path: 'process-management',
                name: 'ProcessManagement',
                meta: {
                    title: '审批流程管理',
                    icon: 'menus-process7',
                    permissions: [
                        { name: '详情', code: 'processManagementView' },
                        { name: '编辑', code: 'processManagementEdit' },
                        { name: '发布记录', code: 'processManagementPublish' }
                    ]
                },
                component: () => import('@/views/approval/process-management.vue')
            },
            {
                path: 'process-todo',
                name: 'ProcessToDo',
                meta: {
                    title: '审批待办',
                    icon: 'menus-process6',
                    permissions: [
                        { name: '审批待办处理', code: 'processToDoHandle' },
                        { name: '审批待办查看', code: 'processToDoView' },
                        { name: '审批待办撤回', code: 'processToDoRecall' }
                    ]
                },
                component: () => import('@/views/approval/process-todo.vue')
            },
            {
                path: 'process-monitoring',
                name: 'ProcessMonitoring',
                meta: {
                    title: '流程监控',
                    icon: 'menus-process5',
                    permissions: [
                        { name: '查看', code: 'processMonitoringView' },
                        { name: '节点管理', code: 'processMonitoringNodeView' }
                    ]
                },
                component: () => import('@/views/approval/process-monitoring.vue')
            }
        ]
    }
];
export default routes;
