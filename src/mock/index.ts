import { mock } from 'mockjs';
import { merge, get } from 'lodash';

mock(new RegExp(/mockList/), (o) => {
    return mock({
        code: 0,
        data: {
            'data|10': [
                merge(
                    {
                        a: /\d{4}-T\d{2}/
                    },
                    Object.fromEntries(
                        Object.entries(JSON.parse(get(o || {}, 'body', '{}') || '{}')).map(([key, value]: any) => {
                            return [key, /^\/.*\/$/.test(value) ? new RegExp(value.slice(1, -1)) : value];
                        })
                    )
                )
            ],
            'total|1-100': 1
        }
    });
});

mock(new RegExp(/mockData/), (config) => {
    const { params = {}, data = {} } = JSON.parse(config.body || '{}');
    const { page = 1, pageSize = 10, total = 100, noPage = false } = params;

    // 根据 noPage 决定生成条数
    const count = noPage ? total : pageSize;

    // 生成 mock 数据
    const list = mock({
        [`data|${count}`]: [data]
    });

    return {
        code: 0,
        data: {
            page,
            pageSize,
            total,
            noPage,
            data: list.data
        }
    };
});
