<template>
    <alert-content
        :buttons="{
            cancel: { text: '关闭' }, // 隐藏取消按钮
            save: { show: false } // 隐藏保存按钮
        }"
    >
        <n-search-table-page
            v-if="id"
            ref="searchTablePageRef"
            :data-table-props="{ columns: columns, size: 'small' }"
            :data-api="api.sass.api.v1.button.list"
            :params="{
                ...searchParams,
                menuId: id
            }"
            :search-table-space="{
                size: 20
            }"
            :searchProps="{
                showInput: false
            }"
            @add="openForm(null)"
            @reset="reset"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #search_form_middle>
                <n-space align="center">
                    <n-input class="w-128px!" v-model:value="searchParams.name" clearable placeholder="请输入名称" />
                    <n-input class="w-128px!" v-model:value="searchParams.code" clearable placeholder="请输入CODE" />
                </n-space>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-button size="tiny" @click="openForm(row)" type="success"> 编辑 </n-button>
                    <n-button size="tiny" @click="handleDelete(row)" type="error"> 删除 </n-button>
                </n-space>
            </template>
        </n-search-table-page>
    </alert-content>
</template>

<script lang="ts" setup>
import { ButtonListData } from '@/api/sass/api/v1/button';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const props = defineProps<{
    id: string;
}>();

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        fixed: 'left',
        width: '60',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '名称', key: 'name', align: 'center', fixed: 'left' },
    { title: '编码', key: 'code', align: 'center' },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right' }
]);

const searchParams = ref<Record<string, any>>({
    name: '',
    code: ''
});

const openForm = (row: ButtonListData | null) => {
    $alert.dialog({
        title: row ? '编辑' : '新增',
        width: '400px',
        content: import('./button-form.vue'),
        props: {
            row,
            id: props.id,
            onSave: () => searchTablePageRef.value?.initData()
        }
    });
};

const reset = () => {
    searchParams.value = {
        name: '',
        code: ''
    };
    nextTick(() => {
        searchTablePageRef.value.initData();
    });
};

function handleDelete(node: ButtonListData) {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.button.delete([node.id as string]);
            window.$message.success('删除成功');
            searchTablePageRef.value?.initData();
        }
    });
}

const searchTablePageRef = ref();
</script>

<style scoped></style>
