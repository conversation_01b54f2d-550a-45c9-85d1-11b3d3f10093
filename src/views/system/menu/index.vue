<template>
    <div class="menu">
        <n-cascade-menu
            ref="cascadeMenuRef"
            v-model:tree="tree"
            :load-tree="loadTree"
            :editable="store.permissions.indexOf('addMenu') > -1"
            :config="{
                addNodeText: '新增菜单'
            }"
            :tree-options="{
                label: 'title'
            }"
            @node:add="handleAdd"
        >
            <template #node_menu="{ node }">
                <n-dropdown
                    trigger="hover"
                    @select="select($event, node)"
                    :options="[
                        {
                            label: '按钮设置',
                            key: 'button',
                            disabled: store.permissions.indexOf('buttonSet') < 0
                        },
                        {
                            label: '编辑',
                            key: 'edit',
                            disabled: store.permissions.indexOf('editMenu') < 0
                        },
                        {
                            label: '删除',
                            key: 'delete',
                            disabled: store.permissions.indexOf('deleteMenu') < 0
                        }
                    ]"
                >
                    <n-icon :style="{ margin: 'auto 8px' }">
                        <SvgIcon name="svgs-qita" />
                    </n-icon>
                </n-dropdown>
            </template>
        </n-cascade-menu>
        <div class="abs top-14px right-12px" v-if="store.userInfo.isAdmin">
            <n-upload
                ref="uploadRef"
                :max="1"
                :accept="'.json'"
                :custom-request="customRequest"
                :show-file-list="false"
            >
                <n-button> 上传菜单JSON文件 </n-button>
            </n-upload>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { MenuListData } from '@/api/sass/api/v1/menu';
import useStore from '@/store/modules/main';

const store = useStore();

const tree = ref<MenuListData[] | any>([]);

const init = () => {
    nextTick(() => {
        cascadeMenuRef.value?.refresh();
    });
};

const select = (event: any, row: MenuListData) => {
    switch (event) {
        case 'button':
            addButton(row);
            break;
        case 'edit':
            handleEdit(row);
            break;
        case 'delete':
            handleDelete(row);
            break;
    }
};

// 菜单新增
function handleAdd(parent: MenuListData | any) {
    $alert.dialog({
        title: '新增',
        width: '600px',
        content: import('./models/menu-form.vue'),
        props: {
            parent: parent,
            menus: tree.value,
            onSave: () => init()
        }
    });
}

// 菜单编辑
function handleEdit(node: MenuListData) {
    $alert.dialog({
        title: '编辑',
        width: '600px',
        content: import('./models/menu-form.vue'),
        props: {
            row: node,
            menus: tree.value,
            onSave: () => init()
        }
    });
}

// 菜单删除
function handleDelete(node: MenuListData) {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.menu.delete([node.id]);
            window.$message.success('删除成功');
            init();
        }
    });
}

// 按钮操作
function addButton(menu: MenuListData) {
    $alert.dialog({
        title: '按钮权限配置',
        width: '800px',
        content: import('./models/button-list.vue'),
        props: {
            id: menu.id,
            onSave: () => init()
        }
    });
}

const loadTree = async () => {
    const res = await window.api.sass.api.v1.menu.tree.list();
    return res.data.data;
};

/**
 * 上传菜单JSON文件
 */
const uploadRef = ref();
const customRequest = async (options: any) => {
    console.log(options);
    await api.sass.api.v1.menu.upload(options.file.file);
    window.$message.success('上传成功');
    uploadRef.value.clear();
    init();
};

const cascadeMenuRef = ref();
</script>

<style scoped></style>
