<template>
    <n-modal v-model:show="show" preset="card" title="用户列表" :bordered="false" :style="{ width: '80%' }" segmented>
        <n-search-table-page
            v-if="tId"
            ref="searchTablePageRef"
            :data-table-props="{ columns: columns }"
            :data-api="api.sass.api.v1.tenantUserInfo.list"
            :params="{
                tenantId: tId,
                ignoreTenant: true,
                kind: 'common'
            }"
            :search-table-space="{
                size: 10
            }"
            :search-props="{
                inputWidth: '228px',
                searchInputPlaceholder: '请输入用户名 / 姓名 / 手机号',
                addText: '新增用户',
                showAdd: store.permissions.indexOf('addUser') > -1
            }"
            padding="0px"
            @add="addUser(null)"
            @reset="init"
        >
            <template #search_handle_after>
                <n-permission has="bindUser">
                    <n-button type="warning" @click="bindUsersRef?.open()">绑定用户</n-button>
                </n-permission>
            </template>
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_avatar="{ row }">
                <n-image v-if="row.avatar.url" width="24" height="24" object-fit="contain" :src="row.avatar.url" />
                <n-image v-else width="24" height="24" object-fit="contain" :src="defaultAvatar" />
            </template>
            <template #table_mobile="{ row }">
                <span v-if="row.mobile">{{ utils.hideString(row.mobile, 3, 4) }}</span>
            </template>
            <template #table_status="{ row }">
                <n-tag size="small" :type="row.status ? 'success' : 'error'">{{ row.status ? '启用' : '禁用' }}</n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="editUser">
                        <n-button size="tiny" @click="addUser(row)" type="success">编辑</n-button>
                    </n-permission>
                    <n-permission has="deleteUser">
                        <n-button size="tiny" @click="deleteUser(row)" type="error">删除</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
        <user-form v-if="tId" ref="userFormRef" @submit="init" :tId="tId" />
        <bind-users v-if="tId" ref="bindUsersRef" @submit="init" :tId="tId" />
    </n-modal>
</template>

<script lang="ts" setup>
import { useDialog, useMessage } from 'naive-ui';
import { TenantUserListData } from '@/api/sass/api/v1/tenant-user-info';
import UserForm from '@/views/system/tenant/models/user-form.vue';
import BindUsers from '@/views/system/tenant/models/bind-users.vue';
import useStore from '@/store/modules/main';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import defaultAvatar from '@/assets/images/avatar.png';
import { utils } from 'wp-utils';

const dialog = useDialog();
const message = useMessage();
const store = useStore();

const show = ref(false);
const tId = ref<string | null>(null);

// 接口
const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '用户名', key: 'username', align: 'center' },
    { title: '昵称', key: 'nickname', align: 'center' },
    { title: '头像', key: 'avatar', align: 'center' },
    { title: '手机', key: 'mobile', align: 'center' },
    { title: '是否有效', key: 'status', align: 'center' },
    { title: '操作', key: 'todo', align: 'center' }
]);
const addUser = (row: TenantUserListData | null) => {
    userFormRef.value.open(row);
};
const deleteUser = (row: TenantUserListData) => {
    dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            if (!tId.value) return;
            await window.api.sass.api.v1.tenantUserInfo.delete([row.id], tId.value);
            message.success('删除成功');
            init();
        }
    });
};

const open = (id: string) => {
    show.value = true;
    tId.value = id;
};

const init = () => {
    nextTick(() => searchTablePageRef.value?.initData());
};

const userFormRef = ref();
const bindUsersRef = ref();
const searchTablePageRef = ref();

defineExpose({ open });
</script>

<style scoped></style>
