<template>
    <n-modal v-model:show="show" preset="card" title="绑定用户" :bordered="false" :style="{ width: '60%' }" segmented>
        <n-transfer v-model:value="selected" virtual-scroll :options="users" source-filterable />
        <template #footer>
            <n-space justify="center">
                <n-button @click="show = false">取消</n-button>
                <n-button @click="submit" type="primary">保存</n-button>
            </n-space>
        </template>
    </n-modal>
</template>

<script lang="ts" setup>
import { useMessage } from 'naive-ui';
import { UserListData } from '@/api/sass/api/v1/user';
import { TenantUserListData } from '@/api/sass/api/v1/tenant-user-info';

const message = useMessage();

const props = defineProps<{
    tId: string;
}>();
const emit = defineEmits<{
    (e: 'submit'): void;
}>();

const show = ref(false);
const selected = ref<string[]>([]);
const users = ref<{ label: string; value: string; disabled: boolean }[]>([]);

const open = () => {
    show.value = true;
    init();
};

const init = async () => {
    const res = await window.api.sass.api.v1.user.list({
        ignoreTenant: true,
        kind: 'common',
        noPage: true
    });
    users.value = res.data.data.map((v: UserListData) => ({
        ...v,
        label: v.nickname || v.username,
        value: v.id,
        disabled: !v.status
    }));
    selected.value = (
        await window.api.sass.api.v1.tenantUserInfo.list({
            tenantId: props.tId,
            kind: 'common',
            noPage: true
        })
    ).data.data.map((v: TenantUserListData) => v.id);
};

const submit = async () => {
    const res = await window.api.sass.api.v1.tenant.update_users(props.tId, selected.value);
    await message.success(res.msg as string);
    show.value = false;
    emit('submit');
};

defineExpose({ open });
</script>

<style scoped></style>
