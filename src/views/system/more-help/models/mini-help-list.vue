<template>
    <div class="min-h-300px mini-hellp-list">
        <n-data-table :show="false" max-height="450" :columns="columnData" :data="tableData" :row-props="rowProps">
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import type { RowVO } from '@/api/sass/api/v1/dict';

const tableData = ref<RowVO[]>([]);

const columnData = [
    {
        title: '序号',
        key: 'serialNumber',
        width: '60px',
        render: (row: RowVO, index: number) => index + 1
    },
    {
        title: '文件名称',
        key: 'fileName',
        ellipsis: { tooltip: true },
        className: 'color-blue'
    }
];

const rowProps = (row: RowVO) => ({
    onClick: () => {
        handleSelect(row);
    }
});

const handleSelect = (row: RowVO) => {
    if (!row) return;
    $alert.dialog({
        title: `文件预览: ${row.fileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: row.fileId,
            name: row.fileName,
            format: row.fileType
        }
    });
};

const getFile = async () => {
    const res = await api.sass.api.v1.dict.helpList();
    tableData.value = res.data.data;
};

onMounted(() => {
    getFile();
});
</script>

<style scoped lang="less">
:deep(tr td.color-blue) {
    color: #005eff;
}
</style>
