<template>
    <alert-content :on-default-save="save">
        <minio-upload
            v-model:file-list="fileInfo"
            :uploadProps="{
                max: 1,
                accept: '.doc,.docx,.pdf',
                onPreview: handlePreview,
                onRemove: handleRemove
            }"
            @success="handleFinish"
        >
            <template #drag>
                <div class="w-full h-full flex-v justify-center items-center p-50px">
                    <span class="text-18px font-600"> 上传文件 </span>
                    <img :src="uploadFileImg" class="w-60px h-60px mt-20px" />
                    <span class="text-14px mt-20px"> 点击或者拖动文件到该区域来上传 </span>
                    <span class="text-12px c-#838383 mt-30px"> 只能上传word 、pdf，且只能上传一个文件</span>
                </div>
            </template>
        </minio-upload>
    </alert-content>
</template>

<script setup lang="ts">
import uploadFileImg from '@/assets/images/file/upload-file.webp';

const props = defineProps({
    type: {
        type: String,
        default: 'add'
    },
    row: {
        type: Object,
        default: null
    }
});

const fileInfo = ref<any[]>([]);
const isAdd = ref(props.type === 'add');
const isUploadStatus = ref(false);

const save = async () => {
    if (!fileInfo.value.length || fileInfo.value[0].id === undefined) {
        window.$message.warning('请先上传文件');
    } else if (!isUploadStatus.value) {
        window.$message.warning('请先等待文件上传完成');
    } else {
        if (isAdd.value) {
            await api.sass.api.v1.dict.addFileList(fileInfo.value[0].id);
        } else {
            await api.sass.api.v1.dict.updateFileList(props.row.id, fileInfo.value[0].id);
        }
    }
};

const handlePreview = () => {
    $alert.dialog({
        title: `文件预览: ${fileInfo.value[0]?.name}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: fileInfo.value[0]?.id || props.row.fileId,
            name: fileInfo.value[0]?.name || props.row.fileName,
            format: fileInfo.value[0]?.format || props.row.fileType
        }
    });
};

const handleRemove = () => {
    isUploadStatus.value = false;
};

const handleFinish = async (file: any) => {
    isUploadStatus.value = true;

    fileInfo.value = [
        {
            id: file.id,
            name: file.name,
            status: 'finished',
            format: file.format
        }
    ];
};

const getFile = async () => {
    if (props.type !== 'add') {
        const data = [
            {
                id: props.row.fileId,
                name: props.row.fileName,
                status: 'finished'
            }
        ];
        fileInfo.value = data;
        isUploadStatus.value = true;
    }
};

onMounted(() => {
    getFile();
});
</script>

<style scoped></style>
