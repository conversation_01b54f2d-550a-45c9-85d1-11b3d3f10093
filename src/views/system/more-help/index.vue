<template>
    <vxe-search-table-page
        ref="vxeSearchTablePageRef"
        :columns="columns"
        :dataApi="api.sass.api.v1.dict.helpList"
        :pagination="false"
        :search-props="{
            showInput: false,
            showSearch: false,
            showReset: false,
            showAdd: store.permissions.indexOf('addMoreHelp') > -1
        }"
        :vxe-table-props="{
            onRowDragend: rowDragendEvent,
            rowConfig: {
                drag: true
            }
        }"
        @add="resultEvent('add')"
    >
        <template #table_fileName="{ row }">
            <span class="c-var-primary-color cursor-pointer" @click="filePreview(row)">{{ row.fileName }}</span>
        </template>
        <template #table_createdAt="{ row }">
            <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
        </template>
        <template #table_updatedAt="{ row }">
            <n-time :time="row.updatedAt" format="yyyy-MM-dd HH:mm:ss" />
        </template>
        <template #table_todo="{ row }">
            <n-space justify="center">
                <n-permission has="editMoreHelp">
                    <n-button type="success" size="tiny" @click="resultEvent('edit', row)">编辑</n-button>
                </n-permission>
                <n-permission has="deleteMoreHelp">
                    <n-button type="error" size="tiny" @click="delEvent(row)">删除</n-button>
                </n-permission>
            </n-space>
        </template>
    </vxe-search-table-page>
</template>

<script lang="ts" setup>
import type { RowVO } from '@/api/sass/api/v1/dict';
import { VxeTableEvents } from 'vxe-table';
import useStore from '@/store/modules/main';

const store = useStore();

const vxeSearchTablePageRef = ref();

// 列配置
const columns = [
    {
        type: 'seq',
        width: 60
    },
    {
        title: '文件名称',
        field: 'fileName',
        align: 'left',
        minWidth: 200,
        dragSort: true
    },
    {
        title: '创建时间',
        field: 'createdAt'
    },
    {
        title: '创建人',
        field: 'createdBy'
    },
    {
        title: '编辑时间',
        field: 'updatedAt'
    },
    {
        title: '操作人',
        field: 'updatedBy'
    },
    {
        title: '操作',
        field: 'todo',
        width: 140
    }
];

const filePreview = (row: Record<string, any>) => {
    $alert.dialog({
        title: `文件预览: ${row.fileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: row.fileId,
            name: row.fileName,
            format: row.fileType
        }
    });
};

const resultEvent = (typeText: string, row?: RowVO) => {
    $alert.dialog({
        title: `${typeText === 'add' ? '新增' : '编辑'}`,
        width: '600px',
        content: import('./models/file-dialog.vue'),
        props: {
            row: row !== undefined ? row : {},
            type: typeText,
            onSave: () => init()
        }
    });
};

const delEvent = (row: RowVO) => {
    window.$dialog.warning({
        title: '提示',
        content: '确定删除当前项吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await api.sass.api.v1.dict.deleteFileList(row.id);
            window.$message.success('删除成功！');
            init();
        }
    });
};

const rowDragendEvent: VxeTableEvents.RowDragend = async ({ newRow, oldRow, dragPos }) => {
    await api.sass.api.v1.dict.sortFileList(oldRow.id, newRow.id, dragPos);
    window.$message.success('排序成功！');
    init();
};

const init = () => {
    nextTick(() => {
        vxeSearchTablePageRef.value?.initData();
    });
};
</script>

<style scoped lang="less"></style>
