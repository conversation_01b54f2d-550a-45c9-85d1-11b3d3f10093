<template>
    <div class="system-user bg-#fff p-10px py-20px">
        <n-data-table :columns="columns" :data="onLineUser" :pagination="pagination" bordered> </n-data-table>
    </div>
</template>
<script lang="ts" setup>
import { NButton, NTag, NTime } from 'naive-ui';
import { TableColumn } from 'naive-ui/es/data-table/src/interface';
import { utils } from 'wp-utils';

const searchParams = ref<Record<string, any>>({});

const columns = ref<TableColumn[]>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        fixed: 'left',
        width: '60',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '用户名', key: 'username', align: 'center' },
    { title: '姓名', key: 'nickname', align: 'center' },
    {
        title: '手机号',
        key: 'mobile',
        align: 'center',
        render(row) {
            return h(
                'span',
                {},
                {
                    default: () => {
                        return utils.hideString(row.mobile as string, 3, 4);
                    }
                }
            );
        }
    },
    { title: '所属租户', key: 'tenantName', align: 'center' },
    {
        title: '登录端',
        key: 'deviceKind',
        align: 'center',
        render(row) {
            return h(
                NTag,
                {
                    size: 'small',
                    type: kindOption.find((item) => item.label === row.deviceKind)?.type as any
                },
                { default: () => kindOption.find((item) => item.label === row.deviceKind)?.label }
            );
        }
    },
    { title: '登录IP', key: 'ip', align: 'center' },
    {
        title: '登录时间',
        key: 'loginDate',
        align: 'center',
        render(row) {
            return h(
                NTime,
                {
                    time: row.loginDate as Date
                },
                { default: () => row.loginDate }
            );
        }
    },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        render(row) {
            return h(
                NButton,
                {
                    size: 'tiny',
                    type: 'primary',
                    onClick: () => logout(row)
                },
                { default: () => '登出' }
            );
        }
    }
]);
const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    onChange: (page: number) => {
        pagination.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
        pagination.pageSize = pageSize;
        pagination.page = 1;
    },
    prefix: () => `共 ${onLineUser.value.length || 0} 项`
});

const kindOption = [
    {
        label: 'app',
        type: 'primary'
    },
    {
        label: 'web',
        type: 'warning'
    }
];

const onLineUser = ref([]);
const getData = async () => {
    const res = await api.sass.api.v1.user.online.list();
    onLineUser.value = res.data.data;
};
getData();

// 统一弹窗确认
const dialogCheck = (info?: string) => {
    return new Promise((resolve) => {
        window.$dialog.warning({
            title: '警告',
            content: info || '确定删除所选数据么？',
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => resolve(true)
        });
    });
};

const logout = async (row: any) => {
    await dialogCheck('确定要登出该设备吗？');
    const data = {
        deviceKind: row.deviceKind,
        userId: row.userID,
        tokenId: row.tokenId
    };
    await api.sass.api.v1.user.online.force_logout(data);
    window.$message.success('操作成功');
    init();
};

// 初始化
const init = async () => {
    searchParams.value = {
        username: '',
        nickname: '',
        mobile: '',
        tenantId: null,
        loginType: null
    };
    await nextTick(() => getData());
};
</script>

<style lang="less" scoped></style>
