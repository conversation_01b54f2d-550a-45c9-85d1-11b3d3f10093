<template>
    <div class="dictionary-managed">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns: columns,
                size: 'small'
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :dataApi="$apis.nebula.api.v1.businessDictionary.list"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: '请输入模块 / 字段名称'
            }"
            :search-table-space="{ size: 20 }"
            @add="handleOperate(null)"
            @reset="init"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_view="{ row }">
                <n-permission has="dictionaryView">
                    <n-button @click="handleOperate(row, 'view')" size="tiny"> 详情 </n-button>
                </n-permission>
            </template>
            <template #table_updatedAt="{ row }">
                <n-time :time="row.updatedAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_todo="{ row }">
                <n-permission has="dictionaryEdit">
                    <n-button @click="handleOperate(row, 'edit')" size="tiny" type="success"> 编辑 </n-button>
                </n-permission>
            </template>
        </n-search-table-page>
    </div>
</template>
<script lang="ts" setup>
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: '45px',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '模块名称',
        key: 'moduleName',
        align: 'center',
        fixed: 'left',
        minWidth: 160
    },
    { title: '字段', key: 'fieldName', align: 'center', minWidth: 120 },
    { title: '字典详情', key: 'view', align: 'center', minWidth: 80 },
    { title: '最近修改人', key: 'updatedBy', align: 'center', minWidth: 120 },
    { title: '最近修改时间', key: 'updatedAt', align: 'center', minWidth: 80 },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        minWidth: 120
    }
]);

const handleOperate = (row: any, type?: string) => {
    $alert.dialog({
        title: `字典${type === 'edit' ? '编辑' : '详情'}`,
        width: '60%',
        content: import('./models/dictionary-node-details.vue'),
        props: {
            id: row?.id,
            type: type
        }
    });
};

const init = async () => {
    nextTick(() => searchTablePageRef.value?.initData());
};

const searchTablePageRef = ref();
</script>
