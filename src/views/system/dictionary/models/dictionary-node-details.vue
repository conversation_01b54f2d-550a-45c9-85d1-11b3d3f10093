<template>
    <vxe-search-table-page
        ref="vxeSearchTablePageRef"
        class="p-0! pr-2px!"
        :columns="columns"
        :params="{ dictionaryId: id }"
        :dataApi="$apis.nebula.api.v1.businessDictionary.node.list"
        :pagination="false"
        :showItemCount="true"
        :search-props="{
            showInput: false,
            showSearch: false,
            showReset: false,
            showAdd: store.permissions.indexOf('dictionaryNodeAdd') > -1 && isEdit
        }"
        :search-table-space="{
            size: 10
        }"
        :vxe-table-props="{
            onRowDragend: rowDragendEvent,
            rowConfig: {
                drag: isEdit
            },
            rowDragConfig: {
                isPeerDrag: true
            },
            treeConfig: {
                lazy: true,
                async loadMethod({ row }) {
                    const res = await $apis.nebula.api.v1.businessDictionary.node.list({
                        dictionaryId: props.id,
                        parentId: row.id as string
                    });
                    const children = processNodeData(res.data.data || []);
                    
                    // 返回处理后的子节点数据
                    return children;
                }
            }
        }"
        :table-props="{ showPagination: false }"
        @add="handleNodeOperate(null, 'add')"
    >
        <template #table_status="{ row }">
            <n-switch
                v-model:value="row.status"
                :disabled="!isEdit"
                @click.stop
                @update:value="(value) => handleChangeStatus(value, row)"
            >
                <template #checked> 是 </template>
                <template #unchecked> 否 </template>
            </n-switch>
        </template>
        <template v-if="isEdit" #table_todo="{ row }">
            <n-space justify="center">
                <n-permission has="dictionaryNodeEdit" :useRoute="() => $route">
                    <n-button size="tiny" type="primary" @click="handleNodeOperate(row, 'add')"> 新增 </n-button>
                </n-permission>
                <n-permission has="dictionaryNodeEdit" :useRoute="() => $route">
                    <n-button size="tiny" type="success" @click="handleNodeOperate(row, 'edit')"> 编辑 </n-button>
                </n-permission>
                <n-permission has="dictionaryNodeDelete" :useRoute="() => $route">
                    <n-button size="tiny" type="error" @click="handleNodeDelete(row)"> 删除 </n-button>
                </n-permission>
            </n-space>
        </template>
    </vxe-search-table-page>
</template>

<script setup lang="ts">
import { DictionaryNodeList } from '@/api/apis/nebula/api/v1/business-dictionary';
import useStore from '@/store/modules/main';
import VxeSearchTablePage from '@/components/vxe-search-table-page.vue';

const store = useStore();

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    type: {
        type: String,
        default: 'edit'
    }
});
const isEdit = computed(() => props.type === 'edit');
// 列配置
const columns = [
    {
        title: '名称',
        field: 'name',
        align: 'left',
        minWidth: 120,
        treeNode: true,
        dragSort: true,
        fixed: 'left'
    },
    {
        title: '代码',
        field: 'code'
    },
    {
        title: '备注',
        field: 'remark',
        minWidth: 80
    },
    {
        title: '是否可选',
        field: 'status',
        width: 100
    }
].concat(
    (isEdit.value
        ? [
              {
                  title: '操作',
                  field: 'todo',
                  width: 180,
                  fixed: 'right'
              }
          ]
        : []) as any
);

const vxeSearchTablePageRef = ref<any>();

// 数据处理
const processNodeData = (data: DictionaryNodeList[]): DictionaryNodeList[] => {
    return data?.map((element) => ({
        ...element,
        hasChild: true,
        children: Array.isArray(element.children) ? processNodeData(element.children) : undefined
    }));
};

// 刷新节点
const reloadNode = async () => {
    nextTick(() => {
        vxeSearchTablePageRef.value.initData();
    });
};

// 添加收起所有子级的方法
const collapseNode = (node: DictionaryNodeList) => {
    const table = vxeSearchTablePageRef.value.tableRef;
    if (!table) return;
    table.setTreeExpand(node, false);
};

// 修改状态切换方法
const handleChangeStatus = async (value: boolean, node: DictionaryNodeList) => {
    $apis.nebula.api.v1.businessDictionary.node
        .update({
            ...node,
            status: value
        })
        .catch(async () => {
            node.status = !value;
            window.$message.error('状态更新失败');
        });
};

// 修改拖拽排序方法
const rowDragendEvent = async ({ newRow, oldRow }: { newRow: DictionaryNodeList; oldRow: DictionaryNodeList }) => {
    $apis.nebula.api.v1.businessDictionary.node
        .move({
            id: oldRow.id,
            sort: newRow.sort
        })
        .catch(async () => {
            window.$message.error('排序更新失败');
            await reloadNode();
        });
};

// 修改节点操作方法
const handleNodeOperate = (node: DictionaryNodeList | null, type: 'add' | 'edit') => {
    $alert.dialog({
        title: `${type === 'add' ? '新增' : '编辑'}`,
        width: '500px',
        content: import('./dictionary-node-form.vue'),
        props: {
            row: node,
            type,
            dictionaryId: props.id,
            onClose: async () => {
                // 等待对话框关闭后再刷新数据
                if (type === 'edit') {
                    await collapseNode(node as DictionaryNodeList);
                }
                await reloadNode();
            }
        }
    });
};

// 修改删除节点方法
const handleNodeDelete = async (node: DictionaryNodeList) => {
    window.$dialog.warning({
        title: '提示',
        content: '确定删除该条数据吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.businessDictionary.node.delete(node.id as string);
            window.$message.success('删除成功');
            await reloadNode();
        }
    });
};
</script>

<style scoped lang="less"></style>
