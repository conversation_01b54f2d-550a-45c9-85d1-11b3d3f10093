<template>
    <div class="dictionary-node-form">
        <alert-content :on-default-save="save">
            <form-validate
                v-model="form"
                :field="fields"
                ref="formRef"
                :config="{
                    labelPlacement: 'left',
                    labelWidth: 55,
                    requireMarkPlacement: 'left',
                    showFeedback: false
                }"
                :grid-props="{ yGap: 10 }"
            ></form-validate>
        </alert-content>
    </div>
</template>

<script setup lang="ts">
import { DictionaryNodeForm, DictionaryNodeList } from '@/api/apis/nebula/api/v1/business-dictionary';

const props = defineProps({
    type: {
        type: String,
        default: 'edit'
    },
    row: {
        type: Object,
        default: null
    },
    dictionaryId: {
        type: String,
        default: ''
    }
});

const form = ref<DictionaryNodeForm | DictionaryNodeList>({
    dictionaryId: '',
    name: '',
    remark: ''
});
const fields = ref<FormValidateField>([
    {
        label: '名称',
        component: 'input',
        field: 'name',
        props: {
            placeholder: '请输入节点名称',
            maxlength: 50,
            showCount: true
        },
        rules: [
            {
                required: true,
                message: '请输入节点名称',
                trigger: 'blur'
            }
        ]
    },
    {
        label: '代码',
        component: 'input',
        field: 'code',
        props: {
            placeholder: '请输入代码',
            maxlength: 50,
            showCount: true
        }
    },
    {
        label: '备注',
        component: 'input',
        field: 'remark',
        props: {
            placeholder: '请输入备注',
            type: 'textarea',
            maxlength: 50,
            showCount: true
        }
    }
]);

const save = async () => {
    await formRef.value.validate();
    let api;
    if (isEdit.value) {
        api = $apis.nebula.api.v1.businessDictionary.node.update;
        await api(form.value as DictionaryNodeList);
    } else {
        api = $apis.nebula.api.v1.businessDictionary.node.create;
        await api(form.value as DictionaryNodeForm);
    }
    window.$message.success('操作成功');
};

const isEdit = computed(() => {
    return props.type === 'edit';
});
onMounted(() => {
    if (isEdit.value) {
        form.value = {
            ...(props?.row as DictionaryNodeList),
            dictionaryId: props.dictionaryId
        } as DictionaryNodeList;
    } else {
        form.value.dictionaryId = props.dictionaryId;
        form.value.parentId = props.row?.id;
    }
});

const formRef = ref();
</script>

<style scoped></style>
