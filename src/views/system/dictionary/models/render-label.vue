<template>
    <n-ellipsis
        max-width="600px"
        :tooltip="{
            contentStyle: {
                maxHeight: '60vh',
                overflow: 'auto',
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgba(255, 255, 255, 0.8) rgba(34, 34, 34, 0.2)'
            }
        }"
    >
        {{ option.name }}
    </n-ellipsis>
</template>

<script setup lang="ts">
defineProps({
    option: {
        type: Object,
        required: true
    }
});
</script>
