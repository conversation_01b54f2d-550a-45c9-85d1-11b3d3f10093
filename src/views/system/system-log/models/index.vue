<template>
    <div class="flex-col justify-between mt-10px p-5px pr-10px">
        <json-viewer
            ref="copyBtnRef"
            :value="viewerData"
            :copyable="{ copyText: '复制', copiedText: '复制完成' }"
            boxed
            expanded
            sort
            :expand-depth="9"
            @copied="onCopied"
        />
    </div>
</template>

<script lang="ts" setup>
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/vue3-json-viewer.css';
import copy from 'copy-to-clipboard';

const props = defineProps<{
    row: any;
}>();

const viewerData = ref({
    requestBody: JSON.parse(props.row.requestBody),
    responseBody: JSON.parse(props.row.responseBody)
});

const copyBtnRef = ref();
const onCopied = async ({ text }: any) => {
    await copy(text);
    window.$message.success('复制成功');
};
</script>

<style scoped lang="less"></style>
