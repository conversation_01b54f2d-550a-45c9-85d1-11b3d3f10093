<template>
    <div class="log">
        <n-search-table-page
            ref="tableRef"
            :data-api="dataApi"
            :search-table-space="{ size: 20 }"
            :params="{ ...searchParams }"
            :data-table-props="{
                columns: columns,
                maxHeight: 'calc(100vh - 340px)'
            }"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: searchInputPlaceholder('search')
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="refresh"
        >
            <template #search_form_middle>
                <n-space align="center" v-if="routeName === 'operationLog'">
                    <n-select
                        class="w-150px"
                        placeholder="请选择操作结果"
                        v-model:value="searchParams.isOK"
                        :options="statusOptions"
                        clearable
                        consistent-menu-width
                    />
                    <n-select
                        class="w-150px"
                        placeholder="请选择操作类型"
                        v-model:value="searchParams.apiKind"
                        :options="apiKindOptions"
                        value-field="dictName"
                        label-field="dictName"
                        clearable
                        consistent-menu-width
                    />
                    <n-select
                        class="w-150px"
                        placeholder="请选择操作模块"
                        v-model:value="searchParams.apiModule"
                        :options="apiModuleOptions"
                        value-field="dictName"
                        label-field="dictName"
                        clearable
                        consistent-menu-width
                    />
                </n-space>
                <n-space align="center" v-if="routeName === 'loginLog'">
                    <n-date-picker
                        v-model:value="searchParams.createdAt"
                        type="datetimerange"
                        clearable
                        placeholder="请选择登录时间"
                    />
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { utils } from 'wp-utils';
import { NTag, NButton } from 'naive-ui';

const tableRef = ref();

const searchParams = ref<any>({});

const route = useRoute();
const routeName = route.name as string;
const statusOptions = ref<any>([
    { label: '成功', value: true },
    { label: '失败', value: false }
]);
const apiKindOptions = ref([]);
const apiModuleOptions = ref([]);

const dataApi = computed(() => {
    return api.sass.api.v1.system[routeName === 'operationLog' ? 'operation' : 'login'].list;
});

const refresh = () => {
    searchParams.value = {
        isOK: null,
        apiKind: null,
        apiModule: null,
        createdAt: null,
        location: ''
    };
    nextTick(() => tableRef.value?.initData());
};

onMounted(() => {
    const requests = ['api_kind', 'api_module'].map(async (type: string) => {
        return api.sass.api.v1.dict.list({ dictType: type, noPage: true });
    });
    Promise.all(requests).then((responses) => {
        apiKindOptions.value = responses[0].data.data || [];
        apiModuleOptions.value = responses[1].data.data || [];
    });
});

const kindOption = [
    {
        label: 'app',
        type: 'primary'
    },
    {
        label: 'web',
        type: 'warning'
    }
];

const columns = computed<any>(() => {
    const tableColumArr = {
        loginLog: [
            {
                title: '序号',
                key: 'key',
                align: 'center',
                fixed: 'left',
                width: '60',
                render: (_: any, index: number) => {
                    return `${index + 1}`;
                }
            },
            {
                title: `用户名`,
                key: 'loginUserName',
                align: 'left',
                fixed: 'left'
            },
            {
                title: `登录用户`,
                key: 'userName',
                align: 'center'
            },
            {
                title: `登录方式`,
                key: 'deviceKind',
                align: 'center',
                render(row: any) {
                    return h(
                        NTag,
                        {
                            size: 'small',
                            type: kindOption.find((item) => item.label === row.deviceKind)?.type as any,
                            round: true,
                            bordered: false
                        },
                        { default: () => kindOption.find((item) => item.label === row.deviceKind)?.label || '' }
                    );
                }
            },
            {
                title: `登录ip`,
                key: 'ip',
                align: 'center',
                width: '160px',
                ellipsis: {
                    tooltip: true
                }
            },
            {
                title: `登录时间`,
                key: 'createdAt',
                align: 'center',
                width: '180px',
                render(row: any) {
                    return utils.dateFormat(row.createdAt, 'YYYY-MM-DD HH:mm:ss');
                }
            }
        ],
        operationLog: [
            {
                title: '序号',
                key: 'key',
                align: 'center',
                fixed: 'left',
                width: '60',
                render: (_: any, index: number) => {
                    return `${index + 1}`;
                }
            },
            {
                title: `操作用户`,
                key: 'userName',
                align: 'left',
                fixed: 'left',
                minWidth: '80px',
                ellipsis: {
                    tooltip: true
                }
            },
            {
                title: '操作模块',
                key: 'apiModule',
                align: 'center',
                minWidth: '80px',
                ellipsis: {
                    tooltip: true
                }
            },
            {
                title: '操作类型',
                key: 'apiKind',
                align: 'center',
                minWidth: '80px',
                ellipsis: {
                    tooltip: true
                }
            },
            {
                title: '操作描述',
                key: 'landingLocation',
                align: 'center',
                minWidth: '80px',
                ellipsis: {
                    tooltip: true
                },
                render(row: any) {
                    const { userName, apiModule, apiKind, apiDescription } = row;
                    const str = apiModule === '通用模块' ? '' : `在${apiModule}中`;
                    return `${userName}${str}${apiKind}${apiDescription}`;
                }
            },
            {
                title: `操作ip`,
                key: 'ip',
                align: 'center',
                width: '160px',
                ellipsis: {
                    tooltip: true
                }
            },
            {
                title: `操作时间`,
                key: 'createdAt',
                align: 'center',
                width: '180px',
                render(row: any) {
                    return utils.dateFormat(row.createdAt, 'YYYY-MM-DD HH:mm:ss');
                }
            },
            {
                title: '操作结果',
                key: 'isOK',
                align: 'center',
                width: '80px',
                render(row: any) {
                    return h(
                        NTag,
                        {
                            type: row.isOK ? 'primary' : 'error',
                            size: 'small',
                            round: true,
                            bordered: false
                        },
                        {
                            default: () => (row.isOK ? '成功' : '失败')
                        }
                    );
                }
            },
            {
                title: '报文',
                key: 'landingLocation',
                align: 'center',
                render(row: any) {
                    return h(
                        NButton,
                        {
                            size: 'tiny',
                            onClick: () => messageView(row)
                        },
                        {
                            default: () => '点击查看'
                        }
                    );
                }
            }
        ]
    } as any;
    return tableColumArr[routeName];
});

const messageView = (row: any) => {
    $alert.dialog({
        title: '查看报文',
        width: '50%',
        content: import('./models/index.vue'),
        props: { row }
    });
};

const searchInputPlaceholder = (searchType: string) => {
    if (searchType === 'search') return `请输入${routeName === 'loginLog' ? '登录' : '操作'}用户`;
};
</script>

<style scoped lang="less">
.index {
}
</style>
