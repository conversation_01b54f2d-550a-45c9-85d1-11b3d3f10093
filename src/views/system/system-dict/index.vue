<template>
    <div class="system-dict">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns: columns,
                size: 'small',
                scrollX: 1600
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :dataApi="api.sass.api.v1.dict.list"
            :search-props="{
                showAdd: store.permissions.indexOf('addSystemDict') > -1 && store.userInfo.isAdmin,
                searchInputPlaceholder: '请输入字典名称'
            }"
            :search-table-space="{ size: 20 }"
            @add="handleOperate(null)"
            @reset="init"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_updatedAt="{ row }">
                <n-time :time="row.updatedAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_createdAt="{ row }">
                <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_isOpen="{ row }">
                <n-switch v-model:value="row.isOpen" size="small" disabled />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="editSystemDict">
                        <n-button @click="handleOperate(row)" size="tiny" type="success"> 编辑 </n-button>
                    </n-permission>
                    <n-permission has="deleteSystemDict">
                        <n-button @click="handleDelete(row)" size="tiny" type="error"> 删除 </n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>
<script lang="ts" setup>
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import useStore from '@/store/modules/main';

const store = useStore();

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: '45px',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '字典名称',
        key: 'dictName',
        align: 'center',
        fixed: 'left',
        minWidth: 160,
        ellipsis: { tooltip: true }
    },
    { title: '字典类型', key: 'dictType', align: 'center', ellipsis: { tooltip: true } },
    { title: '字段代码', key: 'dictCode', align: 'center', minWidth: 120, ellipsis: { tooltip: true } },
    { title: '排序', key: 'dictOrder', align: 'center', minWidth: 60 },
    { title: '描述', key: 'description', align: 'center', minWidth: 180, ellipsis: { tooltip: true } },
    { title: '更新时间', key: 'updatedAt', align: 'center', width: 180 },
    { title: '创建时间', key: 'createdAt', align: 'center', width: 180 },
    { title: '是否启用', key: 'isOpen', align: 'center', width: 80 },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 140
    }
]);

const handleOperate = (row?: any) => {
    if (!store.userInfo.isAdmin) {
        window.$message.error('您没有权限操作');
        return;
    }
    $alert.dialog({
        title: `系统字典${row ? '编辑' : '新增'}`,
        width: '600px',
        content: import('./models/dict-form.vue'),
        props: {
            row,
            onSave: () => init()
        }
    });
};

const handleDelete = (row: any) => {
    if (!store.userInfo.isAdmin) {
        window.$message.error('您没有权限操作');
        return;
    }
    window.$dialog.warning({
        title: '确认删除',
        content: '确认后将删除该字典，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await api.sass.api.v1.dict.delete(row.id);
            window.$message.success('删除成功');
            init();
        }
    });
};
const init = async () => {
    nextTick(() => searchTablePageRef.value?.initData());
};

const searchTablePageRef = ref();
</script>
