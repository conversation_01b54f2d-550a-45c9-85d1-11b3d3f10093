<template>
    <div class="position">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{ columns: columns, size: 'small' }"
            :data-api="api.sass.api.v1.position.list"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :search-props="{
                addText: '新增',
                showAdd: store.permissions.indexOf('addPosition') > -1
            }"
            @add="addPosition(null, 'add')"
            @reset="init"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="row.status ? 'success' : 'error'">
                    {{ row.status ? '启用' : '禁用' }}
                </n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="editPosition">
                        <n-button size="tiny" @click="addPosition(row, 'edit')" type="success">编辑</n-button>
                    </n-permission>
                    <n-permission has="deletePosition">
                        <n-button size="tiny" @click="deletePosition(row)" type="error">删除</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script lang="ts" setup>
import { PositionListData } from '@/api/sass/api/v1/position';
import useStore from '@/store/modules/main';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const store = useStore();

// 接口
const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        width: '60',
        align: 'center',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '岗位名称',
        key: 'name',
        align: 'center',
        fixed: 'left',
        minWidth: 160
    },
    { title: '岗位编码', key: 'code', align: 'center' },
    { title: '状态', key: 'status', align: 'center' },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        minWidth: 120
    }
]);

const addPosition = (row: PositionListData | null, type: 'add' | 'edit') => {
    $alert.dialog({
        title: type === 'add' ? '新增' : '编辑',
        width: '600px',
        content: import('@/views/system/position/models/position-form.vue'),
        props: {
            row: row,
            onSave: () => init()
        }
    });
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const deletePosition = (row: PositionListData) => {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.position.delete([row.id ?? '']);
            window.$message.success('删除成功');
            init();
        }
    });
};

const searchTablePageRef = ref();
</script>

<style scoped></style>
