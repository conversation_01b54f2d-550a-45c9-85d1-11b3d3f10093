<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="80"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item label="用户名" path="username">
                <n-input v-model:value="form.username" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item label="姓名" path="nickname">
                <n-input v-model:value="form.nickname" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item label="手机" path="mobile">
                <n-input v-model:value="form.mobile" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item label="岗位">
                <n-select
                    v-model:value="form.positionIds"
                    :options="options"
                    placeholder="请选择岗位 / 可输入名称检索"
                    filterable
                    multiple
                    clearable
                    max-tag-count="responsive"
                />
            </n-form-item>
            <n-form-item label="密码" path="password" v-if="!form.id">
                <n-input v-model:value="form.password" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item label="是否有效" required>
                <n-switch v-model:value="form.status"></n-switch>
            </n-form-item>
            <n-form-item label="头像">
                <minio-upload
                    v-model:file-list="form.avatar"
                    :uploadProps="{
                        max: 1,
                        accept: 'image/*',
                        listType: 'image-card'
                    }"
                />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { UserForm, UserListData } from '@/api/sass/api/v1/user';

const props = defineProps<{
    oId: string;
    row: UserListData;
}>();

const form = ref<UserForm>({
    avatar: [],
    nickname: '',
    username: '',
    password: '',
    mobile: '',
    status: true
});
const formRef = ref();
const rules = {
    username: {
        required: true,
        message: '请输入用户名',
        trigger: 'input'
    },
    nickname: {
        required: true,
        message: '请输入姓名',
        trigger: 'input'
    },
    mobile: {
        required: true,
        message: '请输入手机号',
        trigger: 'input'
    },
    password: {
        required: true,
        message: '请输入密码',
        trigger: 'input'
    }
};

const open = (row: UserListData) => {
    form.value = {
        avatar: [],
        nickname: '',
        username: '',
        password: '',
        mobile: '',
        positionIds: null,
        status: true,
        kind: 'common'
    };
    if (row && row.id) {
        form.value = { ...row };
        if (row.organizationUserPosition)
            form.value.positionIds = row.organizationUserPosition.map((item: any) => item.positionId);
        if (form.value.avatar) form.value.avatar = [{ ...form.value.avatar, status: 'finished' }];
    }
    // 防止无kind字段
    if (!form.value.kind) form.value.kind = 'common';
};

// 获取岗位列表
const options = ref<{ label: string; value: string }[]>([]);
const getPosition = async () => {
    const res = await api.sass.api.v1.position.list({
        noPage: true,
        status: 'true'
    });
    if (res.data.total === 0) {
        options.value = [];
        return;
    } else {
        options.value = res.data.data.map((item: any) => ({
            label: item.name,
            value: item.id
        }));
    }
};

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (error: any) {
        window.$message.error(error[0][0].message);
        return Promise.reject();
    }

    const _data = {
        organizationId: props.oId,
        ...form.value,
        avatarId:
            form.value.avatar && form.value.avatar?.length > 0
                ? form.value.avatar[0].response
                    ? form.value.avatar[0].response.id
                    : form.value.avatar[0].id
                : null
    };
    const res =
        form.value && form.value.id
            ? await api.sass.api.v1.organizationUserInfo.update(_data)
            : await api.sass.api.v1.organizationUserInfo.create(_data);
    res.message && window.$message.success(res.message);
};
onMounted(async () => {
    await getPosition();
    open(props.row);
});
</script>

<style scoped></style>
