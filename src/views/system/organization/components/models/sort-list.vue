<template>
    <div>
        <vxe-search-table-page
            ref="vxeSearchTablePageRef"
            :columns="columns"
            :dataApi="api.sass.api.v1.organizationUserInfo.list"
            :params="{
                organizationId: props.oId
            }"
            :pagination="false"
            :search-props="{
                show: false
            }"
            :vxe-table-props="{
                maxHeight: '500',
                onRowDragend: rowDragendEvent,
                rowConfig: {
                    drag: true
                }
            }"
        >
            <template #table_avatar="{ row }">
                <n-image v-if="row.avatar.url" width="20" height="20" object-fit="contain" :src="row.avatar.url" />
                <n-image v-else width="20" height="20" object-fit="contain" :src="fallbackAvatar" />
            </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="row.status ? 'success' : 'error'">{{
                    row.status ? '启用' : '禁用'
                }}</n-tag>
            </template>
        </vxe-search-table-page>
    </div>
</template>

<script lang="ts" setup>
import { VxeTableEvents } from 'vxe-table';
import fallbackAvatar from '@/assets/images/avatar.png';
const emit = defineEmits<{
    (e: 'init'): void;
}>();
const show = ref(false);

const props = defineProps<{
    oId: string;
}>();

const columns = [
    {
        type: 'seq',
        width: 60,
        fixed: 'left'
    },
    {
        title: '用户名',
        field: 'username',
        align: 'left',
        minWidth: 120,
        showOverflow: true,
        dragSort: true
    },
    {
        title: '姓名',
        field: 'nickname',
        align: 'center',
        showOverflow: true,
        minWidth: 120
    },
    { title: '头像', field: 'avatar', align: 'center', minWidth: 120 },
    {
        title: '用户岗位',
        field: 'positionIds',
        align: 'center',
        showOverflow: true,
        minWidth: '120'
    },
    { title: '手机', field: 'mobile', align: 'center', minWidth: 120, showOverflow: true },
    { title: '是否有效', field: 'status', align: 'center', minWidth: 80 }
];

const open = () => {
    show.value = true;
};

const rowDragendEvent: VxeTableEvents.RowDragend = async ({ newRow, oldRow, dragPos }) => {
    await api.sass.api.v1.organizationUserInfo.userDragSort({
        organizationId: props.oId,
        userId: oldRow.id,
        targetId: newRow.id,
        position: dragPos
    });
    window.$message.success('排序成功！');
    vxeSearchTablePageRef.value?.initData();
    emit('init');
};
const vxeSearchTablePageRef = ref();
defineExpose({ open });
</script>

<style scoped lang="less"></style>
