<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :rules="rules"
            :model="form"
            label-placement="top"
            require-mark-placement="left"
            :show-feedback="false"
        >
            <n-form-item label="原密码" path="operatorPW">
                <n-input v-model:value="form.operatorPW" type="password" placeholder="请输入当前账号的原密码"></n-input>
            </n-form-item>
            <n-form-item label="新密码" path="newPass">
                <n-input v-model:value="form.newPass" type="password" placeholder="请输入新密码"></n-input>
            </n-form-item>
            <n-form-item label="确认新密码" path="newPassCopy">
                <n-input v-model:value="form.newPassCopy" type="password" placeholder="请输入新密码"></n-input>
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { UserUpdatePassword } from '@/api/sass/api/v1/user';
import { FormItemRule } from 'naive-ui';
import { utils } from 'wp-utils';

const props = defineProps<{
    ids: string[];
}>();

const form = ref<Record<string, any>>({});
const rules = ref<any>({
    operatorPW: {
        required: true,
        message: '请输入当前密码',
        trigger: ['blur', 'input']
    },
    newPass: {
        required: true,
        trigger: ['input', 'blur'],
        validator(rule: FormItemRule, value: string) {
            if (!value) {
                return new Error('请输入新密码');
            } else if (!utils.checkPassword(value)) {
                return new Error('请输入8-20位,且同时包含大小写字母、数字以及特殊字符的密码！');
            }
            return true;
        }
    },
    newPassCopy: {
        required: true,
        trigger: ['input', 'blur'],
        validator(rule: FormItemRule, value: string) {
            if (!value) {
                return new Error('请输入确认新密码');
            } else if (value !== form.value.newPass) {
                return new Error('两次输入的新密码不一致');
            }
            return true;
        }
    }
});

const formRef = ref();
const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (error: any) {
        window.$message.error(error[0][0].message);
        return Promise.reject();
    }

    const res = await window.api.sass.api.v1.user.update_users_password({
        ...form.value,
        userIds: props.ids
    } as UserUpdatePassword);
    window.$message.success(res.msg as string);
};
</script>

<style scoped></style>
