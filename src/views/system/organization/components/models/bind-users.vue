<template>
    <alert-content :on-default-save="submit">
        <n-transfer v-model:value="selected" virtual-scroll :options="users" source-filterable />
    </alert-content>
</template>

<script lang="ts" setup>
import { UserListData } from '@/api/sass/api/v1/user';
const props = defineProps<{
    oId: string;
}>();

const selected = ref<string[]>([]);
const users = ref<UserListData[]>([]);
const cloneSelected = ref<string[]>([]);

const init = async () => {
    const res = await window.api.sass.api.v1.user.list({ pageSize: 10000 });

    const selectedIds = (
        await window.api.sass.api.v1.organizationUserInfo.list({
            organizationId: props.oId,
            page: 1,
            pageSize: 10000
        })
    ).data.data.map((v: { id: string }) => v.id);
    cloneSelected.value = [...selectedIds];
    selected.value = selectedIds;

    users.value = res.data.data.map((v: any) => ({
        ...v,
        label: v.username,
        value: v.id,

        disabled: selectedIds.length > 0 ? selectedIds.includes(v.id) : false
    }));
};

const submit = async () => {
    // 找出新选中的用户
    const newSelected = selected.value.filter((id) => !cloneSelected.value.includes(id));
    if (newSelected.length === 0) {
        window.$message.info('没有新选中的用户');
        return;
    }
    const res = await window.api.sass.api.v1.organization.update_users(props.oId, newSelected);
    await window.$message.success(res.msg ?? '操作成功');
};

onMounted(async () => {
    await init();
});
</script>

<style scoped></style>
