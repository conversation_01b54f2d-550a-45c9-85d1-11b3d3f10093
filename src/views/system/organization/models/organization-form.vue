<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="80"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item :label="`${typeName}名称`" path="name">
                <n-input maxlength="50" show-count v-model:value="form.name" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item :label="`${typeName}编码`" path="code">
                <n-input maxlength="50" show-count v-model:value="form.code" placeholder="请输入"></n-input>
            </n-form-item>
            <n-form-item label="是否启用" path="status">
                <n-switch v-model:value="form.status" :disabled="!form.nodeType"></n-switch>
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { OrganizationListData } from '@/api/sass/api/v1/organization';
import useStore from '@/store/modules/main';

const props = defineProps<{
    nodeType: 0 | 1 | 2;
    typeName: string;
    parentId?: string;
    row?: OrganizationListData | null;
}>();

const formRef = ref();
const form = ref<Partial<OrganizationListData>>({});
const rules = {
    name: [{ required: true, message: '请输入名称', trigger: ['blur', 'input'] }],
    code: [{ required: true, message: '请输入编码', trigger: ['blur', 'input'] }]
};

const store = useStore();

const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            if (!form.value.nodeType) {
                //在是集团时无法关闭
                form.value.status = true;
            }
            form.value?.id
                ? await api.sass.api.v1.organization.update(form.value as OrganizationListData)
                : await api.sass.api.v1.organization.create(form.value as OrganizationListData);
            window.$message.success('操作成功');
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].msg);
            return Promise.reject();
        });
};

onMounted(() => {
    form.value = {
        tenantId: store.userInfo.defaultTenantID,
        nodeType: props.nodeType,
        parentId: props.parentId ?? null,
        status: true
    };
    if (props.row) {
        form.value = { ...props.row };
    }
});
</script>

<style scoped></style>
