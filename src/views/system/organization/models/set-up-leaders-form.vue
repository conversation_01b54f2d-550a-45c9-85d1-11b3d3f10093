<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="90"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item :label="`${typeName}领导`">
                <n-select
                    v-model:value="form.leaderIds"
                    :options="options"
                    placeholder="请选择人员 / 可输入名称检索"
                    multiple
                    filterable
                    clearable
                    max-tag-count="responsive"
                />
            </n-form-item>
            <n-form-item v-if="nodeType !== 2" :label="`${typeName}管理员`">
                <n-select
                    v-model:value="form.adminIds"
                    :options="options"
                    placeholder="请选择人员 / 可输入名称检索"
                    multiple
                    filterable
                    clearable
                    max-tag-count="responsive"
                />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { addLeaderAdminForm } from '@/api/sass/api/v1/organization-user-info';
import { SelectOption } from 'naive-ui';

const props = defineProps({
    typeName: {
        type: String,
        default: ''
    },
    nodeType: {
        type: Number,
        default: 0
    },
    id: {
        type: String,
        default: ''
    }
});

const formRef = ref();
const form = ref<addLeaderAdminForm>({
    organizationId: '',
    leaderIds: [],
    adminIds: []
});
const rules = {
    leader: [{ required: true, message: '请选择人员', trigger: ['blur', 'input'] }],
    admin: [{ required: true, message: '请选择人员', trigger: ['blur', 'input'] }]
};

const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            await window.api.sass.api.v1.organizationUserInfo.addLeaderAdmin(form.value);
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};

// 获取用户信息列表
const options = ref<SelectOption[]>([]);
const getUserInfoList = async () => {
    const res = await window.api.sass.api.v1.organizationUserInfo.list({
        noPage: true,
        organizationId: props.id
    });
    if (res.data.total === 0) {
        options.value = [];
        window.$message.warning('当前组织暂无人员');
        setTimeout(() => {
            $alert.dialog.close();
        }, 3000);
        return;
    } else {
        options.value = res.data.data.map((item: any) => ({
            label: item.nickname,
            value: item.id
        }));
    }
};

onMounted(async () => {
    await getUserInfoList();
    // 获取组织信息
    const res = await window.api.sass.api.v1.organization.get(props.id);
    form.value = {
        organizationId: res.data.id,
        leaderIds: res.data.organizationLeaders?.map((item: any) => item.leaderId) ?? [],
        adminIds: res.data.organizationAdmins?.map((item: any) => item.adminId) ?? []
    };
});
</script>

<style scoped></style>
