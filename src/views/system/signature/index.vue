<template>
    <div class="flex-v">
        <div class="bg-white p-24px flex gap-24px items-start w-full">
            <div class="flex items-center">
                <div
                    class="w-300px h-140px border border-gray-200 flex items-center justify-center text-gray-400 text-16px bg-gray-100 rounded-4px"
                >
                    <signature-image
                        v-model:model-value="currentSignature.signatureBase64"
                        :width="300"
                        :height="140"
                        :no-preview="true"
                    />
                </div>
                <div class="flex-v gap-2px text-14px w-full ml-20px">
                    <span>
                        当前签名生效日期：{{
                            currentSignature.effectiveDate
                                ? dayjs(currentSignature.effectiveDate).format('YYYY-MM-DD')
                                : '-'
                        }}
                    </span>
                    <span>
                        当前签名批准日期：{{
                            currentSignature.effectiveDate
                                ? dayjs(currentSignature.effectiveDate).format('YYYY-MM-DD')
                                : '-'
                        }}
                    </span>
                    <span>当前签名批准人：{{ currentSignature.approverName || '-' }}</span>
                    <span>
                        <n-button
                            v-if="currentSignature.authLetterFileId"
                            type="primary"
                            text
                            @click="filePreview(currentSignature)"
                        >
                            {{
                                currentSignature.authLetterFileName ||
                                `签名授权书${dayjs(currentSignature.effectiveDate).format('YYYY-MM-DD')}.pdf`
                            }}
                        </n-button>
                    </span>
                    <n-permission has="signatureConfig">
                        <n-button type="primary" class="w-100px mt-10px" @click="handleConfigSignature">
                            {{ currentSignature.id ? '发起修改' : '配置签名' }}
                        </n-button>
                    </n-permission>
                </div>
            </div>
        </div>
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                maxHeight: 'calc(100vh - 560px)',
                size: 'small'
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :data-api="$apis.nebula.api.v1.signature.getSignatureList"
            :search-props="{
                showInput: false,
                showReset: false,
                showSearch: false,
                showAdd: false
            }"
            :search-table-space="{
                size: 10
            }"
        >
            <template #search_form>
                <span class="text-18px font-bold">历史签名台账</span>
            </template>
            <template #table_effectiveDate="{ row }">
                <n-time :time="row.effectiveDate" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_expirationDate="{ row }">
                <n-time :time="row.expirationDate" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_signatureBase64="{ row }">
                <signature-image v-model:model-value="row.signatureBase64" :width="100" :height="30" />
            </template>
            <template #table_authLetterFileName="{ row }">
                <n-button type="primary" text @click="filePreview(row)">{{ row.authLetterFileName }}</n-button>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { CurrentSignatureInfo } from '@/api/apis/nebula/api/v1/signature';
import { DataTableColumns } from 'naive-ui';
import dayjs from 'dayjs';

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '生效日期',
        key: 'effectiveDate',
        align: 'center',
        width: 180
    },
    {
        title: '失效日期',
        key: 'expirationDate',
        align: 'center',
        width: 180
    },
    {
        title: '电子签名预览',
        key: 'signatureBase64',
        align: 'center',
        minWidth: 200
    },
    {
        title: '授权书',
        key: 'authLetterFileName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    }
];

const currentSignature = ref<CurrentSignatureInfo>({});
const getCurrentSignature = async () => {
    const res = await $apis.nebula.api.v1.signature.getSignature();
    if (res.data.id) {
        currentSignature.value = res.data;
    }
};

const handleConfigSignature = async () => {
    $alert.dialog({
        title: '配置签名',
        content: import('@/views/system/signature/models/signature-form.vue')
    });
};

const filePreview = (row: Record<string, any>) => {
    $alert.dialog({
        title: `文件预览: ${row.authLetterFileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: row.authLetterFileId,
            name: row.authLetterFileName,
            format: 'pdf'
        }
    });
};

onMounted(() => {
    getCurrentSignature();
});
</script>

<style scoped lang="less"></style>
