<template>
    <alert-content :on-default-save="save">
        <div class="signature-form flex items-center gap-20px">
            <div class="flex-v items-center justify-between">
                <div class="w-240px h-180px flex items-center justify-center bg-gray-100 rounded-4px">
                    <n-image
                        v-if="signatureBase64"
                        :src="signatureBase64"
                        width="240px"
                        height="180px"
                        object-fit="scale-down"
                    />
                    <div v-else class="text-16px text-gray-400">未配置签名</div>
                </div>
                <span class="text">签名效果预览</span>
            </div>
            <div class="flex-v items-center justify-between">
                <div class="qr-container w-180px h-180px relative">
                    <n-qr-code v-model="qrCode" :qr-props="qrProps" :padding="0" :borderSize="0" />
                    <div
                        v-if="signatureBase64"
                        class="success-mask absolute left-0 top-0 w-full h-full flex items-center justify-center"
                    >
                        <svg-icon name="svgs-success" size="80" />
                    </div>
                </div>
                <span class="text">扫码配置签名</span>
            </div>
        </div>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';

const store = useStore();
const signatureBase64 = ref('');
const qrCode = ref('');
const qrProps = ref({
    size: 180
});
let interval: any;

const taskId = ref('');
const startTask = async () => {
    const res = await $apis.nebula.api.v1.signature.startTask();
    taskId.value = res.data.taskId;
};

const getTaskStatus = async () => {
    const res = await $apis.nebula.api.v1.signature.getTaskStatus(taskId.value);
    if (res.data.signatureBase64) {
        signatureBase64.value = res.data.signatureBase64;
        clearInterval(interval);
    }
};

// 保存签名，发起审批
const save = async () => {
    if (!signatureBase64.value) {
        window.$message.error('请先在移动端上配置签名');
        return Promise.reject();
    }
    const formContent = JSON.stringify({
        businessId: 'SIGNATURE_APPROVAL',
        version: '1.0.0',
        data: {
            nickname: store.userInfo.nickname,
            signatureBase64: signatureBase64.value,
            reason: '配置新的签名'
        }
    });
    await $hooks.useApprovalProcess('SIGNATURE_APPROVAL', formContent);
    window.$message.success('配置成功');
};

onMounted(async () => {
    await startTask();
    qrCode.value = `${window.location.origin}${import.meta.env.PROD ? '/front' : ''}/#/sign-set?token=${
        store.token
    }&taskId=${taskId.value}`;
    console.log('🚀 ~ onMounted ~ qrCode.value:', qrCode.value);
    nextTick(() => {
        interval = setInterval(() => {
            getTaskStatus();
        }, 5000);
    });
});

onUnmounted(() => {
    clearInterval(interval);
});
</script>

<style scoped lang="less">
.signature-form {
    .text {
        font-size: 16px;
        font-weight: bold;
        margin-top: 10px;
    }

    .qr-container {
        position: relative;
        .success-mask {
            background-color: rgba(255, 255, 255, 0.8);
        }
    }
}
</style>
