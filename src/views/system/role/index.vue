<template>
    <div class="role">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{ columns: columns, size: 'small' }"
            :data-api="api.sass.api.v1.role.list"
            :search-table-space="{
                size: 20
            }"
            :search-props="{
                addText: '新增',
                showAdd: store.permissions.indexOf('addRole') > -1
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="init()"
            @add="openForm(null)"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="row.status ? 'success' : 'error'">{{
                    row.status ? '启用' : '禁用'
                }}</n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="menuAuth">
                        <n-button size="tiny" @click="openMenuAuth(row)" type="primary">菜单权限</n-button>
                    </n-permission>
                    <n-permission has="bindUser">
                        <n-button size="tiny" @click="openBindUsers(row)" type="primary">绑定人员</n-button>
                    </n-permission>
                    <n-permission has="editRole">
                        <n-button size="tiny" @click="openForm(row)" type="success">编辑</n-button>
                    </n-permission>
                    <n-permission has="deleteRole">
                        <n-button size="tiny" @click="handleDelete(row)" type="error">删除</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script lang="ts" setup>
import { RoleForm } from '@/api/sass/api/v1/role';
import { useDialog, useMessage } from 'naive-ui';
import useStore from '@/store/modules/main';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const store = useStore();

const dialog = useDialog();
const message = useMessage();

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '名称',
        key: 'name',
        align: 'center',
        fixed: 'left',
        minWidth: 100
    },
    { title: '编码', key: 'code', align: 'center' },
    { title: '状态', key: 'status', align: 'center' },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 300
    }
]);

const openForm = (row: RoleForm | null) => {
    $alert.dialog({
        title: '角色编辑',
        width: '500px',
        content: import('@/views/system/role/models/role-form.vue'),
        props: {
            row: row,
            onSave: () => init()
        }
    });
};

// 绑定人员
const openBindUsers = (row: RoleForm) => {
    $alert.dialog({
        title: '绑定人员',
        width: '600px',
        content: import('@/views/system/role/models/bind-users.vue'),
        props: {
            id: row.id,
            onSave: () => init()
        }
    });
};

// 菜单权限
const openMenuAuth = (row: RoleForm) => {
    $alert.dialog({
        title: '菜单权限',
        width: '900px',
        content: import('@/views/system/role/models/menu-auth.vue'),
        props: {
            id: row.id,
            onSave: () => init()
        }
    });
};

function handleDelete(node: RoleForm) {
    dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.role.delete([node.id as string]);
            message.success('删除成功');
            searchTablePageRef.value?.initData();
        }
    });
}

const init = () => {
    nextTick(() => searchTablePageRef.value.initData());
};

const searchTablePageRef = ref();
</script>

<style scoped></style>
