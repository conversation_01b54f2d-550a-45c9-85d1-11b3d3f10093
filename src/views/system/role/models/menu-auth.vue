<template>
    <alert-content :on-default-save="submit">
        <n-scrollbar x-scrollable>
            <n-cascade-menu
                ref="cascadeMenuRef"
                v-model:tree="tree"
                v-model:selected="selected"
                :load-tree="loadTree"
                :load-selected="loadTreeSelected"
                selectable
                multiple
                identifier="id"
                :tree-options="{
                    label: 'title'
                }"
            >
            </n-cascade-menu>
        </n-scrollbar>
    </alert-content>
</template>

<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        id: string;
    }>(),
    {
        id: ''
    }
);

const tree = ref<any[]>([]);
const selected = ref([]);
const rId = ref('');

const open = (id: string) => {
    tree.value = [];
    selected.value = [];
    rId.value = id;
};

const loadTree = async () => {
    const res = await window.api.sass.api.v1.menu.tree.list();
    let data = res.data.data;
    const recombinationData = (list: any) => {
        for (let i = 0; i < list.length; i++) {
            const row = list[i];
            let permissions =
                row.permissions && row.permissions.length > 0
                    ? row.permissions.map((v: any) => ({
                          ...v,
                          title: v.name,
                          children: []
                      }))
                    : [];
            row.children = row.children.concat(permissions);
            if (row.children.length > 0) recombinationData(row.children);
        }
    };
    recombinationData(data);
    return data;
};

const loadTreeSelected = async () => {
    const res = await window.api.sass.api.v1.authority.menu.role(rId.value);
    const buttons = res.data.buttonIds.map((v: any) => ({
        id: v,
        nodeType: 'button'
    }));
    const menus = res.data.menuIds.map((v: any) => ({ id: v, nodeType: 'menu' }));
    return [...buttons, ...menus];
};

const submit = async () => {
    const res = await window.api.sass.api.v1.authority.menu.create_or_update({
        buttonIds: selected.value.filter((v: any) => v.nodeType === 'button').map((v: any) => v.id),
        menuIds: selected.value.filter((v: any) => v.nodeType === 'menu').map((v: any) => v.id),
        roleId: rId.value
    });
    window.$message.success(res.msg as string);
};

onMounted(() => {
    open(props.id);
});
</script>

<style scoped></style>
