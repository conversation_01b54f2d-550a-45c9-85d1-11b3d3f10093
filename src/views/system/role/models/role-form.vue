<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="90"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item label="角色名称" path="name">
                <n-input v-model:value="form.name" placeholder="请输入" clearable />
            </n-form-item>
            <n-form-item label="角色CODE" path="code">
                <n-input v-model:value="form.code" placeholder="请输入" clearable />
            </n-form-item>
            <n-form-item label="状态">
                <n-switch v-model:value="form.status"></n-switch>
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { RoleForm } from '@/api/sass/api/v1/role';

const props = withDefaults(
    defineProps<{
        row?: any;
    }>(),
    {
        row: () => ({})
    }
);

// 按钮表单
const form = ref<RoleForm>({
    name: '',
    code: '',
    status: true,
    remark: '',
    tenantId: ''
});

const rules = {
    name: [{ required: true, message: '请输入角色名称', trigger: ['blur', 'input'] }],
    code: [{ required: true, message: '请输入角色编码', trigger: ['blur', 'input'] }]
};

const formRef = ref();
const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (error: any) {
        window.$message.error(error[0][0].message);
        return Promise.reject();
    }

    const res = form.value.id
        ? await window.api.sass.api.v1.role.update(form.value)
        : await window.api.sass.api.v1.role.create(form.value);
    window.$message.success(res.msg as string);
};

onMounted(() => {
    form.value = props.row
        ? { ...props.row }
        : {
              status: true
          };
});
</script>

<style scoped></style>
