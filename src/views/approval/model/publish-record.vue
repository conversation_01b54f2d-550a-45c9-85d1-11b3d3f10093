<template>
    <div class="publishRecord">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-api="(params) => api.sass.api.v1.workflow.templates.version({ ...params, id })"
            :search-props="{
                show: false
            }"
            :data-table-props="{
                columns,
                size: 'small'
            }"
            padding="0px"
        >
            <template #table_versionNo="{ row }"> V{{ row.versionNo }} </template>
            <template #table_status="{ row }">
                <n-tag size="small" :bordered="false" :type="row.status ? 'success' : 'default'" round>{{
                    row.status ? '启用' : '历史'
                }}</n-tag>
            </template>
            <template #table_createdAt="{ row }">
                <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-button size="tiny" @click="handlePreview(row, '详情')">详情</n-button>
                    <n-button type="primary" size="tiny" v-if="!row.status" @click="handleEnable(row)">启用</n-button>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>
<script setup lang="ts">
import { TemplatesList, VersionRecordList } from '@/api/sass/api/v1/workflow/templates';
import { DataTableColumns } from 'naive-ui';

const emit = defineEmits(['enable']);
defineProps<{
    id: string;
}>();

const columns = ref<DataTableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '版本号', key: 'versionNo', align: 'center', fixed: 'left', minWidth: 180 },
    { title: '状态', key: 'status', align: 'center', minWidth: 80 },
    { title: '发布人', key: 'createdBy', align: 'center', minWidth: 120 },
    { title: '发布日期', key: 'createdAt', align: 'center', minWidth: 180 },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', minWidth: 120 }
]);

const handlePreview = async (row: TemplatesList, type: string) => {
    $alert.dialog({
        title: type,
        content: import('../model/process-form.vue'),
        width: '80%',
        props: {
            vId: row.id,
            type
        }
    });
};

const handleEnable = async (row: VersionRecordList) => {
    await api.sass.api.v1.workflow.templates.enable({ versionId: row.id });
    window.$message.success('启用成功');
    init();
    emit('enable');
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const searchTablePageRef = ref();
</script>
<style scoped lang="less"></style>
