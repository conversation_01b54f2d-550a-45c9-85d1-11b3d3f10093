<template>
    <alert-content :on-default-save="save">
        <form-validate
            ref="formRef"
            v-model="data"
            :field="formFields"
            :config="{ labelPlacement: 'left', showFeedback: false, requireMarkPlacement: 'left', labelWidth: 100 }"
            :grid-props="{ yGap: 8 }"
        >
        </form-validate>
    </alert-content>
</template>

<script setup lang="ts">
import { SaveForm } from '@/api/sass/api/v1/workflow/template-preset';

const props = withDefaults(
    defineProps<{
        row?: SaveForm;
        type: string;
        options: { label: string; value: string; type: 'info' | 'success' }[];
    }>(),
    {
        type: '编辑'
    }
);

const data = ref<SaveForm>({} as SaveForm);
const formFields = ref<FormValidateField>([
    {
        label: '唯一识别码',
        field: 'businessId',
        component: 'input',
        rules: {
            required: true,
            message: '请输入唯一识别码'
        },
        props: {
            maxlength: 50,
            showCount: true
        }
    },
    {
        label: '流程名称',
        field: 'name',
        component: 'input',
        rules: {
            required: true,
            message: '请输入流程名称'
        },
        props: {
            maxlength: 50,
            showCount: true
        }
    },
    {
        label: '使用范围',
        field: 'kind',
        component: 'select',
        props: {
            options: props.options
        },
        rules: {
            required: true,
            message: '请选择使用范围'
        }
    }
]);

onMounted(async () => {
    if (props.row) {
        data.value = { ...props.row };
    }
});

const formRef = ref();
const save = async () => {
    await formRef.value?.validate();
    await api.sass.api.v1.workflow.templatePreset.save(data.value);
    window.$message.success('更新成功');
};
</script>

<style scoped></style>
