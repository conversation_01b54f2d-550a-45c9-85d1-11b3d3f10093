<template>
    <alert-content :buttons="buttonConfig">
        <form-validate
            ref="formValidateRef"
            class="useFormDialog"
            v-model="data"
            :field="formFields"
            :config="{ labelPlacement: 'left', showFeedback: false, labelWidth: '120px', requireMarkPlacement: 'left' }"
            :grid-props="{ yGap: 8, cols: 3 }"
        >
        </form-validate>
    </alert-content>
</template>

<script setup lang="ts">
import { TemplatesList } from '@/api/sass/api/v1/workflow/templates';
import { omit } from 'lodash';
import ApprovalStage from '../components/approval-stage.vue';
import ApprovalTimeoutWarning from '../components/approval-timeout-warning.vue';

const props = withDefaults(
    defineProps<{
        id?: string;
        bId?: string;
        vId?: string;
        type: string;
    }>(),
    {
        id: '',
        bId: '',
        vId: '',
        type: '编辑'
    }
);

const isPreview = computed(() => props.type === '详情');
const data = ref<TemplatesList>({} as TemplatesList);
const configFormRef = ref();

// 按钮配置
const buttonConfig = computed(() => ({
    cancel: {
        text: isPreview.value ? '关闭' : '取消',
        show: true
    },
    save: {
        text: '保存',
        show: !isPreview.value,
        onClick: () => save()
    }
}));

const personnelTypeOptions = computed(() => [
    {
        label: '指定人员',
        value: 'designate'
    },
    {
        label: '指定角色',
        value: 'role'
    },
    {
        label: '指定岗位',
        value: 'position'
    },
    {
        label: '自定义人员',
        value: 'custom'
    },
    {
        label: '指定集团人员',
        value: 'group_designate',
        disabled: data.value.presetKind === 'department'
    }
]);
const approvalFormOptions = computed<
    Array<{
        label: string;
        value: string;
        fields: FormValidateField;
    }>
>(
    () =>
        [
            {
                label: '自定义审批环节',
                value: 'custom',
                fields: [
                    {
                        field: 'custom',
                        component: ApprovalStage,
                        props: {
                            ref: configFormRef,
                            disabled: isPreview.value,
                            options: personnelTypeOptions.value
                        }
                    }
                ]
            },
            {
                label: '部门审批',
                value: 'department',
                fields: (
                    [
                        {
                            label: '环节类型',
                            field: 'department.nodeKind',
                            component: 'select',
                            gridItemProps: { span: 1 },
                            rules: [
                                {
                                    required: true,
                                    message: '请选择环节类型'
                                }
                            ],
                            props: {
                                options: [
                                    { label: '审批办理', value: 'approve' },
                                    { label: '审批+抄送', value: 'approve-cc' }
                                ],
                                disabled: isPreview.value
                            }
                        },
                        {
                            label: '层级限制',
                            field: 'department.level',
                            component: 'number',
                            slots: {
                                suffix: () => '层'
                            },
                            gridItemProps: {
                                span: 1
                            },
                            props: {
                                readonly: isPreview.value,
                                min: 0
                            }
                        },
                        {
                            label: '层级多人办理方法',
                            field: 'department.signingKind',
                            component: 'select',
                            rules: [
                                {
                                    required: true,
                                    message: '请选择层级多人办理方法'
                                }
                            ],
                            props: {
                                options: [
                                    { label: '或签（其中一人办理即可）', value: 'or' },
                                    { label: '会签（同时到达办理）', value: 'joint' }
                                ],
                                disabled: isPreview.value
                            },
                            gridItemProps: {
                                span: 1
                            },
                            config: {
                                labelWidth: '150px'
                            }
                        }
                    ] as FormValidateField
                ).concat(
                    data.value.department?.nodeKind === 'approve-cc'
                        ? [
                              {
                                  label: '抄送人员类型',
                                  field: 'department.ccKind',
                                  component: 'select',
                                  gridItemProps: { span: 1 },
                                  rules: [
                                      {
                                          required: true,
                                          message: '请选择抄送人员类型'
                                      }
                                  ],
                                  props: { options: personnelTypeOptions.value, disabled: isPreview.value }
                              },
                              {
                                  label: '选择抄送人员或类型',
                                  field: 'department.ccIds',
                                  component: BsPeopleSelect,
                                  props: {
                                      multiple: true,
                                      disabled: isPreview.value || data.value.department?.ccKind === 'custom',
                                      kind: data.value.department?.ccKind,
                                      placeholder:
                                          data.value.department?.ccKind === 'custom'
                                              ? '无需选择'
                                              : '请选择抄送人员或类型'
                                  },
                                  rules: [
                                      {
                                          required: data.value.department?.ccKind === 'custom' ? false : true,
                                          message: '请选择抄送人员或类型'
                                      }
                                  ],
                                  gridItemProps: { span: 2 }
                              }
                          ]
                        : []
                ),
                disabled: data.value.presetKind === 'group'
            }
        ] as any
);
watchEffect(() => {
    if (data.value.department?.ccKind === 'custom') {
        data.value.department.ccIds = [];
    }
});

const approvalFormInfoFields = computed<FormValidateField>(
    () => (approvalFormOptions.value.find((e: any) => e.value === data.value.kind) as any)?.fields || []
);

const formFields = computed<FormValidateField>(
    () =>
        [
            {
                label: '唯一识别号',
                field: 'businessId',
                component: 'input',
                gridItemProps: {
                    span: 1
                },
                props: {
                    readonly: true
                }
            },
            {
                label: '流程名称',
                field: 'name',
                component: 'input',
                gridItemProps: {
                    span: 1
                },
                rules: [
                    {
                        required: true,
                        message: '请输入流程名称'
                    }
                ],
                props: {
                    readonly: isPreview.value
                }
            },
            {
                label: '审批形式',
                field: 'kind',
                component: 'select',
                gridItemProps: {
                    span: 1
                },
                rules: [
                    {
                        required: true,
                        message: '请选择审批形式'
                    }
                ],
                props: {
                    options: approvalFormOptions.value,
                    disabled: isPreview.value
                }
            },
            {
                label: '环节办理超时预警',
                field: 'timeoutWarnStatus',
                component: ApprovalTimeoutWarning,
                rules: [
                    {
                        required: true,
                        message: '请选择是否开启环节办理超时预警'
                    }
                ],
                props: {
                    keyName: 'timeoutHour',
                    disabled: isPreview.value
                }
            },
            {
                label: '自动审批',
                field: 'autoApproveKind',
                component: 'select',
                rules: [
                    {
                        required: true,
                        message: '请选择是否自动审批'
                    }
                ],
                props: {
                    options: [
                        { label: '不启用', value: 'close' },
                        { label: '相同审批人连续出现', value: 'adjacent' },
                        { label: '相同审批人在流程中多次出现', value: 'any' }
                    ],
                    disabled: isPreview.value
                },
                gridItemProps: {
                    span: 1
                }
            },
            {
                label: '审批范围',
                field: 'presetKind',
                component: BsRadio,
                rules: [
                    {
                        required: true,
                        message: '请选择审批范围'
                    }
                ],
                props: {
                    options: [
                        { label: '公司范围', value: 'department' },
                        { label: '集团范围', value: 'group' }
                    ],
                    class: 'p-x-15px',
                    disabled: true
                },
                gridItemProps: {
                    span: 2
                }
            }
        ].concat(approvalFormInfoFields.value as any) as FormValidateField
);

onMounted(async () => {
    if (props.vId) {
        const res = await api.sass.api.v1.workflow.templates.get({ versionId: props.vId });
        data.value = res.data;
        data.value.id = res.data.mainId;
    }
});

const save = async () => {
    await configFormRef.value?.[0]?.validate?.();
    await formValidateRef.value.validate();

    await api.sass.api.v1.workflow.templates.update(
        omit(data.value, [data.value.kind === 'custom' ? 'department' : 'custom'])
    );
    window.$message.success('更新成功');
};
const formValidateRef = ref();
</script>

<style scoped></style>
