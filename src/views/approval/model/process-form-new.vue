<template>
    <alert-content :buttons="buttonConfig">
        <n-form
            ref="formRef"
            class="useFormDialog"
            :model="data"
            :rules="rules"
            label-placement="left"
            :label-width="160"
            require-mark-placement="right"
            :show-feedback="false"
        >
            <n-grid :cols="3" :x-gap="12" :y-gap="8">
                <!-- 基础信息 -->
                <n-grid-item>
                    <n-form-item label="唯一识别号" path="businessId">
                        <n-input v-model:value="data.businessId" readonly />
                    </n-form-item>
                </n-grid-item>

                <n-grid-item>
                    <n-form-item label="流程名称" path="name">
                        <n-input v-model:value="data.name" :readonly="isPreview" placeholder="请输入流程名称" />
                    </n-form-item>
                </n-grid-item>

                <n-grid-item>
                    <n-form-item label="审批形式" path="kind">
                        <n-select
                            v-model:value="data.kind"
                            :options="approvalFormOptions"
                            :disabled="isPreview"
                            placeholder="请选择审批形式"
                        />
                    </n-form-item>
                </n-grid-item>

                <n-grid-item :span="3">
                    <n-form-item label="环节办理超时预警" path="timeoutWarnStatus">
                        <n-space align="center">
                            <n-switch v-model:value="data.timeoutWarnStatus" :disabled="isPreview" />
                            <template v-if="data.timeoutWarnStatus">
                                <n-input-number
                                    v-model:value="data.timeoutHour"
                                    :disabled="isPreview"
                                    :min="1"
                                    placeholder="请输入超时时间"
                                />
                                <span>小时</span>
                            </template>
                        </n-space>
                    </n-form-item>
                </n-grid-item>

                <n-grid-item>
                    <n-form-item label="自动审批" path="autoApproveKind">
                        <n-select
                            v-model:value="data.autoApproveKind"
                            :options="autoApproveOptions"
                            :disabled="isPreview"
                            placeholder="请选择是否自动审批"
                        />
                    </n-form-item>
                </n-grid-item>

                <n-grid-item :span="2">
                    <n-form-item label="审批范围" path="presetKind">
                        <n-radio-group v-model:value="data.presetKind" :disabled="true">
                            <n-space>
                                <n-radio value="department">公司范围</n-radio>
                                <n-radio value="group">集团范围</n-radio>
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                </n-grid-item>

                <!-- 自定义审批环节表单 -->
                <template v-if="data.kind === 'custom'">
                    <n-grid-item :span="3">
                        <n-form-item path="custom">
                            <approval-stage
                                ref="configFormRef"
                                v-model="data.custom"
                                :disabled="isPreview"
                                :options="personnelTypeOptions"
                            />
                        </n-form-item>
                    </n-grid-item>
                </template>

                <!-- 部门审批表单 -->
                <template v-if="data.kind === 'department'">
                    <n-grid-item>
                        <n-form-item label="环节类型" path="department.nodeKind">
                            <n-select
                                v-model:value="data.department.nodeKind"
                                :options="nodeKindOptions"
                                placeholder="请选择环节类型"
                            />
                        </n-form-item>
                    </n-grid-item>

                    <n-grid-item>
                        <n-form-item label="层级限制" path="department.level">
                            <n-input-number v-model:value="data.department.level">
                                <template #suffix>层</template>
                            </n-input-number>
                        </n-form-item>
                    </n-grid-item>

                    <n-grid-item>
                        <n-form-item label="层级多人办理方法" path="department.signingKind">
                            <n-select
                                v-model:value="data.department.signingKind"
                                :options="signingKindOptions"
                                placeholder="请选择层级多人办理方法"
                            />
                        </n-form-item>
                    </n-grid-item>

                    <template v-if="data.department?.nodeKind === 'approve-cc'">
                        <n-grid-item>
                            <n-form-item label="抄送人员类型" path="department.ccKind">
                                <n-select
                                    v-model:value="data.department.ccKind"
                                    :options="personnelTypeOptions"
                                    placeholder="请选择抄送人员类型"
                                />
                            </n-form-item>
                        </n-grid-item>

                        <n-grid-item :span="2">
                            <n-form-item label="选择抄送人员" path="department.ccIds">
                                <bs-people-select
                                    v-model:value="data.department.ccIds"
                                    :kind="data.department.ccKind"
                                    multiple
                                />
                            </n-form-item>
                        </n-grid-item>
                    </template>
                </template>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import type { FormInst, FormRules, FormItemRule } from 'naive-ui';
import { TemplatesList } from '@/api/sass/api/v1/workflow/templates';
import AlertContent from '@/components/alert-content.vue';
import ApprovalStage from '@/views/approval/components/approval-stage.vue';
import BsPeopleSelect from '@/components/business/people-select.vue';

const props = withDefaults(
    defineProps<{
        id?: string;
        vId?: string;
        type: string;
    }>(),
    {
        id: '',
        vId: '',
        type: '编辑'
    }
);

const isPreview = computed(() => props.type === '详情');
const data = ref<TemplatesList>({} as TemplatesList);
const formRef = ref<FormInst | null>(null);
const configFormRef = ref<{ validate: () => Promise<void>; restoreValidation: () => void }>();

// 按钮配置
const buttonConfig = computed(() => ({
    cancel: {
        text: isPreview.value ? '关闭' : '取消',
        show: true
    },
    save: {
        text: '保存',
        show: !isPreview.value,
        onClick: () => save()
    }
}));

// 表单验证规则
const rules: FormRules = {
    name: {
        required: true,
        message: '请输入流程名称',
        trigger: ['blur', 'input']
    },
    kind: {
        required: true,
        message: '请选择审批形式',
        trigger: ['blur', 'change']
    },
    timeoutWarnStatus: {
        required: true,
        type: 'boolean',
        message: '请选择是否开启环节办理超时预警',
        trigger: ['blur', 'change']
    },
    autoApproveKind: {
        required: true,
        message: '请选择是否自动审批',
        trigger: ['blur', 'change']
    },
    presetKind: {
        required: true,
        message: '请选择审批范围',
        trigger: ['blur', 'change']
    },
    'department.nodeKind': {
        required: true,
        message: '请选择环节类型',
        trigger: ['blur', 'change']
    },
    'department.signingKind': {
        required: true,
        message: '请选择层级多人办理方法',
        trigger: ['blur', 'change']
    },
    'department.ccKind': {
        required: true,
        message: '请选择抄送人员类型',
        trigger: ['blur', 'change']
    } as FormItemRule,
    'department.ccIds': {
        required: true,
        message: '请选择抄送人员',
        trigger: ['blur', 'change']
    } as FormItemRule
};

// 选项配置
const autoApproveOptions = [
    { label: '不启用', value: 'close' },
    { label: '相同审批人连续出现', value: 'adjacent' },
    { label: '相同审批人在流程中多次出现', value: 'any' }
];

const nodeKindOptions = [
    { label: '审批办理', value: 'approve' },
    { label: '审批+抄送', value: 'approve-cc' }
];

const signingKindOptions = [
    { label: '或签（其中一人办理即可）', value: 'or' },
    { label: '会签（同时到达办理）', value: 'joint' }
];

const personnelTypeOptions = computed(() => [
    {
        label: '指定人员',
        value: 'designate'
    },
    {
        label: '指定角色',
        value: 'role'
    },
    {
        label: '指定岗位',
        value: 'position'
    },
    {
        label: '自定义人员',
        value: 'custom'
    },
    {
        label: '指定集团人员',
        value: 'group_designate',
        disabled: data.value.presetKind === 'department'
    }
]);

const approvalFormOptions = computed(() => [
    {
        label: '自定义审批环节',
        value: 'custom'
    },
    {
        label: '部门审批',
        value: 'department',
        disabled: data.value.presetKind === 'group'
    }
]);

onMounted(async () => {
    if (props.vId) {
        const res = await api.sass.api.v1.workflow.templates.get({ versionId: props.vId });
        data.value = res.data;
        data.value.id = res.data.mainId;
    }
});

const save = async () => {
    // 先验证表单
    await formRef.value?.validate();
    await configFormRef.value?.validate?.();

    // 保存数据
    await api.sass.api.v1.workflow.templates.update(data.value);
    window.$message.success('更新成功');
};
</script>

<style scoped lang="less">
.approval-process-form {
    &:deep(*) {
        .n-form-item .n-form-item-label {
            white-space: nowrap;
        }
        .n-form-item {
            margin-bottom: 0;
        }
        .n-grid {
            --n-x-gap: 24px;
            --n-y-gap: 16px;
        }
    }
}
</style>
