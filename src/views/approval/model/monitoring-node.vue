<template>
    <alert-content :on-default-save="save">
        <n-form
            ref="formRef"
            :model="data"
            :rules="rules"
            label-placement="left"
            :show-feedback="false"
            require-mark-placement="left"
        >
            <n-tabs v-model:value="activeTab" type="line" size="small" class="mt-10px" @update:value="updateTab">
                <n-tab-pane v-if="nodeApprover?.length > 0" name="update" tab="更改节点办理人员">
                    <n-form-item label="当前节点人员" path="taskId" class="py-5px">
                        <n-select
                            v-model:value="data.taskId"
                            :options="nodeApprover"
                            label-field="approverNickname"
                            value-field="taskId"
                        />
                    </n-form-item>
                    <n-form-item label="办理人更改为" path="approverId" class="py-5px">
                        <bs-people-select
                            v-model:model-value="data.approverId"
                            kind="designate"
                            :filterPeople="nodeApprover.map((item) => item.approverId)"
                        />
                    </n-form-item>
                </n-tab-pane>
                <n-tab-pane name="add" tab="添加同环节办理人员">
                    <n-form-item label="添加办理人员" path="approverId">
                        <bs-people-select v-model:model-value="data.approverId" kind="designate" />
                    </n-form-item>
                </n-tab-pane>
            </n-tabs>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormRules } from 'naive-ui';

const props = withDefaults(
    defineProps<{
        id: string;
        nodeId: string;
        nodeApprover: any[];
    }>(),
    {
        id: '',
        nodeId: ''
    }
);

const activeTab = ref('update');
const updateTab = () => {
    data.value.approverId = '';
    data.value.taskId = '';
};
const data = ref<Record<string, any>>({
    approverId: ''
});

const rules = ref<FormRules>({
    taskId: {
        required: true,
        message: '请选择当前节点人员'
    },
    approverId: {
        required: true,
        message: '请选择办理人'
    }
});

const formRef = ref();
const save = async () => {
    await formRef.value?.validate();
    if (activeTab.value === 'update') {
        await api.sass.api.v1.workflow.monitor.approver_update({
            taskId: data.value.taskId,
            approverId: data.value.approverId
        });
    } else {
        await api.sass.api.v1.workflow.monitor.approval_add({
            nodeId: props.nodeId,
            approverId: data.value.approverId
        });
    }
    window.$message.success('操作成功');
};

onMounted(() => {
    if (props.nodeApprover?.length > 0) {
        activeTab.value = 'update';
    } else {
        activeTab.value = 'add';
    }
});
</script>

<style scoped></style>
