<template>
    <AlertContent :on-default-save="save">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="top"
            :label-width="110"
            require-mark-placement="left"
        >
            <n-form-item label="选择分发对象" path="organizationList">
                <n-card size="small" class="flex-v">
                    <template v-if="organizationOptions.length > 0">
                        <n-checkbox
                            v-model:checked="checkAll"
                            :disabled="organizationOptions.every((val) => val.disabled)"
                            @update:checked="handleCheckAll"
                        >
                            全选</n-checkbox
                        >
                        <n-checkbox-group v-model:value="formData.organizationList">
                            <div class="flex-v">
                                <n-checkbox
                                    v-for="item in organizationOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                    :disabled="item.disabled"
                                />
                            </div>
                        </n-checkbox-group>
                    </template>
                    <n-empty v-else description="未获取到组织机构数据，请重试或者联系管理员" />
                </n-card>
            </n-form-item>
        </n-form>
    </AlertContent>
</template>

<script setup lang="ts">
import { FormRules } from 'naive-ui';

const props = withDefaults(defineProps<{ id: string }>(), {});

// 表单验证规则
const rules: FormRules = {
    organizationList: {
        type: 'array',
        required: true,
        message: '请至少选择一个分发对象',
        trigger: ['blur', 'input']
    }
};

const formData = ref<any>({});
const checkAll = ref(false);
const organizationOptions = ref<any[]>([]);

const handleCheckAll = (checked: boolean) => {
    if (checked) {
        formData.value.organizationList = organizationOptions.value
            .filter((val) => !val.disabled)
            .map((item) => item.value);
    } else {
        formData.value.organizationList = [];
    }
};

const presetInfo = ref();
const getInfo = async (id: string) => {
    const res = await api.sass.api.v1.workflow.templatePreset.get(id);
    presetInfo.value = res.data;
};

const getOrganizationOptions = async () => {
    const res = await api.sass.api.v1.organization.typeList({
        nodeTypes: [0, 1]
    });
    organizationOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        disabled: presetInfo.value.organizationIds.includes(item.id)
    }));
};

const formRef = ref();
const save = async () => {
    await formRef.value?.validate((errors: any) => {
        if (errors) {
            window.$message.error(errors[0]?.[0]?.message);
            return false;
        } else {
            return true;
        }
    });
    await api.sass.api.v1.workflow.templatePreset.bind({
        id: props.id,
        organizationIds: formData.value.organizationList
    });
    window.$message.success('分发成功');
};

onMounted(async () => {
    await getInfo(props.id);
    await getOrganizationOptions();
});
</script>

<style scoped></style>
