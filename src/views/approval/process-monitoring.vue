<template>
    <div class="approval-process-monitoring">
        <n-search-table-page
            ref="searchTablePageRef"
            :params="params"
            :data-api="api.sass.api.v1.workflow.monitor.list"
            :data-table-props="{
                columns,
                rowClassName: rowClassNameRender,
                size: 'small',
                scrollX: 1000,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: '请输入流程名称'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="init"
        >
            <template #search_form_middle>
                <n-date-picker
                    class="w-400px!"
                    v-model:value="params.flowCreatedTime"
                    start-placeholder="发起开始时间"
                    end-placeholder="发起结束时间"
                    type="datetimerange"
                    clearable
                ></n-date-picker>
                <n-select
                    class="w-160px"
                    v-model:value="params.flowNodeApproverId"
                    :options="flowPeopleOptions"
                    filterable
                    placeholder="请选择环节处理人"
                    clearable
                ></n-select>
                <n-select
                    class="w-160px"
                    v-model:value="params.flowCreatedUserId"
                    :options="flowPeopleOptions"
                    filterable
                    placeholder="请选择发起人"
                    clearable
                ></n-select>
                <n-select
                    v-model:value="params.flowStatus"
                    :options="approvalStatus"
                    placeholder="请选择状态"
                    class="w-120px"
                    clearable
                ></n-select>
            </template>
            <template #table_key="{ row, index }">
                <div class="abs-r">
                    <n-tooltip trigger="hover" v-if="row.warnStatus">
                        <template #trigger>
                            <svg-icon
                                name="svgs-warning"
                                class="abs-center transform-translate-x-[calc(-100%-10px)] text-#e33c3c"
                                current-color
                            ></svg-icon>
                        </template>
                        {{ row.warnStatus === 'timeout' ? '审批环节超时' : '审批环节处理人异常' }}
                    </n-tooltip>
                    {{ index + 1 }}
                </div>
            </template>
            <template #table_currentNodeApprover="{ row }">
                <n-ellipsis :line-clamp="1" class="v-top">
                    <span v-if="row.currentNodeApprover?.length > 0">{{
                        row.currentNodeApprover.map((item: any) => item.approverNickname).join(',')
                    }}</span>
                    <span v-else>--</span>
                </n-ellipsis>
            </template>
            <template #table_flowCreatedTime="{ row }">
                <n-time :time="row.flowCreatedTime" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_flowStatus="{ row }">
                <n-tag size="small" :type="type(row.flowStatus)" round secondary :bordered="false">{{
                    label(row.flowStatus)
                }}</n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="processMonitoringView">
                        <n-button size="tiny" @click="handle(row, 'view')">详情</n-button>
                    </n-permission>
                    <n-permission
                        v-if="row.flowStatus === $datas.approvalStatus.underReview.value"
                        has="processMonitoringNodeView"
                    >
                        <n-button type="primary" size="tiny" @click="nodeEdit(row)">节点管理</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { WorkflowTaskList } from '@/api/sass/api/v1/workflow/task';
import { DataTableColumns } from 'naive-ui';

const params = ref<any>({});
const columns = ref<DataTableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60
    },
    { title: '流程名称', key: 'flowName', align: 'center', minWidth: 200, fixed: 'left', ellipsis: { tooltip: true } },
    { title: '当前环节', key: 'currentNodeName', align: 'center', ellipsis: { tooltip: true } },
    { title: '环节处理人', key: 'currentNodeApprover', align: 'center', ellipsis: { tooltip: true } },
    { title: '发起人员', key: 'flowCreatedUserNickname', align: 'center', ellipsis: { tooltip: true } },
    { title: '发起时间', key: 'flowCreatedTime', align: 'center', width: 180 },
    { title: '状态', key: 'flowStatus', align: 'center', width: 80 },
    { title: '操作', key: 'todo', align: 'center', width: 160, fixed: 'right' }
]);

// 环节处理人 - 发起人
const flowPeopleOptions = ref([]);
const getFlowPeopleOptions = async () => {
    await api.sass.api.v1.organizationUserInfo.groupAllList().then((res) => {
        flowPeopleOptions.value = res.data?.map((item: any) => {
            return {
                label: item.nickname,
                value: item.id
            };
        });
    });
};

const approvalStatus = computed(() => {
    return Object.values($datas.approvalStatus)
        .filter((item) => item.value !== 'notStarted')
        .map((item) => {
            return {
                label: item.label,
                value: item.value
            };
        });
});

const label = (status: keyof typeof $datas.approvalStatus) => {
    return $datas.approvalStatus[status]?.label;
};
const type = (status: keyof typeof $datas.approvalStatus) => {
    return $datas.approvalStatus[status]?.type;
};
const rowClassNameRender = (row: any) => {
    return row.warnStatus ? 'warnStatus' : '';
};

const handle = (row: WorkflowTaskList, type: 'handle' | 'view') => {
    $alert.dialog({
        title: '详情',
        style: 'width: 80%; max-width: 1200px; min-width: 480px;',
        content: import('./model/pending-details.vue'),
        props: {
            flowId: row.workflowId,
            type
        }
    });
};

const nodeEdit = (row: any) => {
    $alert.dialog({
        title: '节点管理',
        content: import('./model/monitoring-node.vue'),
        width: '600px',
        props: {
            id: row.workflowId,
            nodeName: row.currentNodeName,
            nodeId: row.currentNodeId,
            nodeApprover: row.currentNodeApprover,
            onSave: () => init()
        }
    });
};

const searchTablePageRef = ref();
const init = () => {
    params.value = {
        flowCreatedTime: null,
        flowNodeApproverId: null,
        flowCreatedUserId: null,
        flowStatus: null
    };
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

onMounted(() => {
    getFlowPeopleOptions();
});
</script>

<style scoped lang="less">
.approval-process-monitoring {
    &:deep(*) {
        .warnStatus {
            td {
                --n-merged-td-color: #ffeaea !important;
            }
            &:hover {
                td {
                    --n-merged-td-color-hover: #ffc3c3 !important;
                }
            }
        }
    }
}
</style>
