<template>
    <div class="approval-stage w-100% p-15px">
        <div
            class="flex-center justify-between"
            :class="{
                'm-b-15px': modelValue?.length > 0
            }"
        >
            <div class="bold text-18px">审批环节</div>
            <n-button type="primary" @click="add" :disabled="disabled">
                <template #icon>
                    <SvgIcon name="svgs-add"></SvgIcon>
                </template>
                新增审批环节
            </n-button>
        </div>
        <n-space vertical>
            <approval-stage-item
                v-for="(item, index) in modelValue"
                :key="index"
                ref="forms"
                :disabled="disabled"
                :options="options"
                :title="`审批环节${index + 1}`"
                :data="item"
                @delete="deleteItem(index)"
            ></approval-stage-item>
        </n-space>
    </div>
</template>
<script setup lang="ts">
import ApprovalStageItem from './approval-stage-item.vue';
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        disabled?: boolean;
        options: Record<string, any>[];
    }>(),
    {
        modelValue: [],
        disabled: false,
        options: () => []
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
const add = () => {
    if (modelValue.value === null) {
        modelValue.value = [];
    }
    nextTick(() => {
        modelValue.value.push({});
    });
};
const deleteItem = (index: number) => {
    window.$dialog.warning({
        title: '提示',
        content: '确定要删除该审批环节吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
            modelValue.value.splice(index, 1);
        }
    });
};
const forms = ref([]);
defineExpose({
    validate: async () => {
        return await Promise.all(forms.value.map((e: any) => e.form.validate()));
    }
});
</script>
<style scoped lang="less"></style>
