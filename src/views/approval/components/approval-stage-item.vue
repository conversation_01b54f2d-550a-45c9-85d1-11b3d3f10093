<template>
    <div class="approval-stage w-100% p-x-15px p-y-10px bg-#f8f9fc">
        <div class="flex-center justify-between m-b-10px">
            <div>
                <slot name="title">{{ title }}</slot>
            </div>
            <div>
                <slot name="button">
                    <n-button @click="emit('delete')" type="error" size="tiny" :disabled="disabled" round secondary>
                        <template #icon>
                            <SvgIcon name="svgs-delete" color="#d03050"></SvgIcon>
                        </template>
                        删除
                    </n-button>
                </slot>
            </div>
        </div>
        <FormValidate
            ref="form"
            v-model="data"
            :field="field"
            :config="config"
            :grid-props="{
                cols: 3,
                xGap: 5,
                yGap: 5
            }"
            white-form-theme
        ></FormValidate>
    </div>
</template>
<script setup lang="ts">
import { FormProps } from 'naive-ui';

const props = withDefaults(
    defineProps<{
        data?: any;
        title?: any;
        disabled?: boolean;
        options: Record<string, any>[];
    }>(),
    {
        data: () => [],
        disabled: false,
        options: () => []
    }
);
const emit = defineEmits(['update:data', 'delete']);
const { data } = useVModels(props, emit);
const config = computed<FormProps>(() => ({
    labelPlacement: 'left',
    showFeedback: false,
    requireMarkPlacement: 'left'
}));
const field = computed<FormValidateField>(
    () =>
        [
            {
                label: '环节名称',
                field: 'nodeName',
                component: 'input',
                gridItemProps: { span: 1 },
                rules: [
                    {
                        required: true,
                        message: '请选择环节名称'
                    }
                ],
                props: {
                    readonly: props.disabled
                }
            },
            {
                label: '环节类型',
                field: 'nodeKind',
                component: 'select',
                gridItemProps: { span: 1 },
                rules: [
                    {
                        required: true,
                        message: '请选择环节类型'
                    }
                ],
                props: {
                    disabled: props.disabled,
                    options: [
                        { label: '审批办理', value: 'approve' },
                        { label: '审批+抄送', value: 'approve-cc' }
                    ]
                }
            },
            {
                label: '层级多人办理方法',
                field: 'signingKind',
                component: 'select',
                rules: [
                    {
                        required: true,
                        message: '请选择层级多人办理方法'
                    }
                ],
                props: {
                    disabled: props.disabled,
                    options: [
                        { label: '或签（其中一人办理即可）', value: 'or' },
                        { label: '会签（同时到达办理）', value: 'and' }
                    ]
                },
                gridItemProps: { span: 1 }
            },
            {
                label: '办理人员类型',
                field: 'approvalKind',
                component: 'select',
                gridItemProps: { span: 1 },
                rules: [
                    {
                        required: true,
                        message: '请选择办理人员类型'
                    }
                ],
                props: { options: props.options, disabled: props.disabled }
            },
            {
                label: '选择办理人员或类型',
                field: 'approverIds',
                component: BsPeopleSelect,
                rules: [
                    {
                        required: props.data?.approvalKind === 'custom' ? false : true,
                        message: '请选择办理人员或类型'
                    }
                ],
                props: {
                    multiple: true,
                    disabled: props.disabled || props.data?.approvalKind === 'custom',
                    kind: props.data?.approvalKind,
                    placeholder: props.data?.approvalKind === 'custom' ? '无需选择' : '请选择办理人员或类型'
                },
                gridItemProps: { span: 2 }
            }
        ].concat(
            props.data.nodeKind === 'approve-cc'
                ? [
                      {
                          label: '抄送人员类型',
                          field: 'ccKind',
                          component: 'select',
                          gridItemProps: { span: 1 },
                          rules: [
                              {
                                  required: true,
                                  message: '请选择抄送人员类型'
                              }
                          ],
                          props: { options: props.options, disabled: props.disabled }
                      },
                      {
                          label: '选择抄送人员或类型',
                          field: 'ccIds',
                          component: BsPeopleSelect,
                          rules: [
                              {
                                  required: props.data?.ccKind === 'custom' ? false : true,
                                  message: '请选择抄送人员或类型'
                              }
                          ],
                          props: {
                              multiple: true,
                              disabled: props.disabled || props.data?.ccKind === 'custom',
                              kind: props.data?.ccKind,
                              placeholder: props.data?.ccKind === 'custom' ? '无需选择' : '请选择抄送人员'
                          },
                          gridItemProps: { span: 2 }
                      }
                  ]
                : []
        ) as any
);

watchEffect(() => {
    if (data.value.approvalKind === 'custom') {
        data.value.approverIds = [];
    }
    if (data.value.ccKind === 'custom') {
        data.value.ccIds = [];
    }
});

const form = ref();
defineExpose({
    form
});
</script>

<style scoped lang="less"></style>
