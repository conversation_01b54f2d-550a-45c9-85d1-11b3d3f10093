<template>
    <div class="approval-timeout-warning w-100% flex-center p-x-15px gap-10px h-$n-height">
        <bsRadio v-model="modelValue" :options="options" :disabled="disabled"></bsRadio>
        <n-input-number
            :readonly="!modelValue || disabled"
            class="flex-1"
            v-model:value="formData[keyName]"
            :min="0"
            clearable
        >
            <template #suffix>小时</template>
        </n-input-number>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
        formData?: any;
        keyName?: any;
        disabled?: boolean;
    }>(),
    {
        keyName: 'value',
        disabled: false
    }
);
const emit = defineEmits(['update:modelValue', 'update:formData']);
const { modelValue, formData } = useVModels(props, emit);
const options = ref([
    {
        label: '开启',
        value: true
    },
    {
        label: '关闭',
        value: false
    }
]);
</script>
<style scoped lang="less"></style>
