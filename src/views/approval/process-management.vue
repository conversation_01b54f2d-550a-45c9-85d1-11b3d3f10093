<template>
    <div class="approval-process-management">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-api="api.sass.api.v1.workflow.templates.list"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1100,
                maxHeight: 'calc(100vh - 390px)'
            }"
            :search-input-props="{}"
            :search-props="{
                inputWidth: 300,
                searchInputPlaceholder: '请输入唯一识别码 / 流程名称',
                showAdd: false
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="init"
        >
            <template #table_versionNo="{ row }"> V{{ row.versionNo }} </template>
            <template #table_createdAt="{ row }">
                <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_updatedAt="{ row }">
                <n-time :time="row.updatedAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="processManagementView">
                        <n-button size="tiny" @click="handlePreview(row, '详情')">详情</n-button>
                    </n-permission>
                    <n-permission has="processManagementEdit">
                        <n-button size="tiny" type="success" @click="handlePreview(row, '编辑')">编辑</n-button>
                    </n-permission>
                    <n-permission has="processManagementPublish">
                        <n-button size="tiny" type="primary" @click="publishRecord(row)">发布记录</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>
<script setup lang="ts">
import { TemplatesList } from '@/api/sass/api/v1/workflow/templates';
import { DataTableColumns } from 'naive-ui';
const columns = ref<DataTableColumns>([
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    {
        title: '唯一识别码',
        key: 'businessId',
        align: 'center',
        fixed: 'left',
        minWidth: 120,
        ellipsis: { tooltip: true }
    },
    { title: '流程名称', key: 'name', minWidth: 120, ellipsis: { tooltip: true } },
    { title: '当前版本', key: 'versionNo', align: 'center', ellipsis: { tooltip: true } },
    { title: '更新人', key: 'updatedBy', align: 'center', ellipsis: { tooltip: true } },
    { title: '更新时间', key: 'updatedAt', align: 'center', width: 180 },
    { title: '创建人', key: 'createdBy', align: 'center', ellipsis: { tooltip: true } },
    { title: '创建时间', key: 'createdAt', align: 'center', width: 180 },
    {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 200
    }
]);

const handlePreview = async (row: TemplatesList, type: string) => {
    $alert.dialog({
        title: type,
        content: import('./model/process-form.vue'),
        style: 'width: 80%; max-width: 1600px;',
        props: {
            vId: row.versionId,
            type,
            onSave: () => init()
        }
    });
};

const publishRecord = async (row: TemplatesList) => {
    $alert.dialog({
        title: '发布记录',
        content: import('./model/publish-record.vue'),
        style: 'width: 80%; max-width: 1200px;',
        props: {
            id: row.id,
            onEnable: () => init()
        }
    });
};

const searchTablePageRef = ref();
const init = () => {
    nextTick(() => searchTablePageRef.value?.initData());
};
</script>
<style scoped lang="less"></style>
