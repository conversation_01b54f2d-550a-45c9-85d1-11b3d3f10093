<template>
    <div class="login flex w-full h-100vh bg-#fff min-w-480px overflow-hidden">
        <div class="left flex-0.4 h-100vh bg-#fff">
            <video
                src="/file/login-video.mp4"
                alt="登录页宣传视频"
                autoplay
                loop
                muted
                class="w-100% h-100vh object-cover"
            ></video>
        </div>
        <div class="right flex-0.6 flex-v items-center h-100vh overflow-auto">
            <div class="main flex-0.96 flex-v justify-center items-center w-420px 3xl:w-600px">
                <div class="title w-480px 3xl:w-720px flex justify-center items-center px-5px" v-if="!isForgetPassword">
                    <img class="w-40px h-40px 3xl:w-54px 3xl:h-54px" :src="logo" />
                    <span class="text-38px 3xl:text-52px 3xl:tracking-5px font-bold ml-10px"> {{ base.title }} </span>
                </div>
                <n-tabs
                    class="py-15px 3xl:py-30px px-10px"
                    :value="currentActiveTab"
                    :on-update:value="(value: string)=>changeLoginType(value)"
                >
                    <n-tab
                        v-for="(item, key) in loginType.filter((k: LoginType) => (isForgetPassword ? k.hiddenInTab : !k.hiddenInTab))"
                        :key="key"
                        :name="item.name"
                    >
                        {{ item.name }}
                    </n-tab>
                </n-tabs>
                <div class="form w-full">
                    <template v-for="(item, key) in loginType" :key="key">
                        <template v-if="item.isActive">
                            <div class="list">
                                <p v-text="item.tip[0]" />
                                <n-input
                                    v-model:value="userForm[item.formKey[0]]"
                                    type="text"
                                    :placeholder="`请输入${item.tip[0]}`"
                                    @keydown.enter="login"
                                />
                            </div>
                            <div class="list fixed">
                                <p v-text="item.tip[1]" />
                                <n-input
                                    v-model:value="userForm[item.formKey[1]]"
                                    :type="`${key === 0 ? 'password' : 'text'}`"
                                    :maxlength="`${key === 2 ? 6 : null}`"
                                    :placeholder="`请输入${item.tip[1]}`"
                                    show-password-on="click"
                                    @keydown.enter="login"
                                >
                                    <template #suffix>
                                        <div
                                            v-if="[1, 2].includes(key)"
                                            class="code"
                                            :class="{ coding: countDown > 0 }"
                                            @click="handleCountDown"
                                        >
                                            <n-countdown
                                                ref="countdownRef"
                                                :duration="countDown"
                                                :active="countDown > 0"
                                                :render="countdownRender"
                                                @finish="goFinish"
                                            />
                                        </div>
                                    </template>
                                </n-input>
                            </div>
                            <div class="list fixed">
                                <p v-text="item.tip[2]" />
                                <n-input
                                    v-model:value="userForm[item.formKey[2]]"
                                    type="text"
                                    :placeholder="`请输入${item.tip[2]}`"
                                    show-password-on="click"
                                    @keydown.enter="login"
                                >
                                    <template #suffix>
                                        <div class="code img" @click="init">
                                            <img :src="imgCode" alt="" />
                                        </div>
                                        <n-popover placement="top" trigger="click">
                                            <template #trigger>
                                                <n-icon :component="Expand" class="cursor-pointer" />
                                            </template>
                                            <img :src="imgCode" alt="" class="bg-#989fb6 b-rd-5px w-200px h-80px" />
                                        </n-popover>
                                    </template>
                                </n-input>
                            </div>
                            <div class="list fixed" v-if="key === 2">
                                <p v-text="item.tip[3]" />
                                <n-input
                                    v-model:value="userForm[item.formKey[3]]"
                                    :type="`${key === 2 ? 'password' : 'text'}`"
                                    :placeholder="`请输入${item.tip[3]}`"
                                    show-password-on="click"
                                    @keydown.enter="login"
                                />
                            </div>
                        </template>
                    </template>
                </div>

                <div v-if="!isForgetPassword" class="btn" @click="login">登录</div>
                <div v-else class="btn" @click="resetPassword">保存</div>
                <div class="text-14px text-center cursor-pointer h-20px">
                    <span v-if="loginType[0].isActive" @click="handleForgetPassword(true)"> 忘记密码 </span>
                    <span v-if="loginType[2].isActive" @click="handleForgetPassword(false)"> 返回登录 </span>
                </div>
            </div>
            <div class="remarks flex flex-0.04 items-center justify-between md:w-50% w-70% py-20px c-#BDBDBD">
                <div class="wx w-120px h-30px b-rd-179px bg-#f9f9f9 flex justify-center items-center cursor-pointer">
                    <img src="@/assets/images/login/wx.webp" alt="" width="12px" height="12px" />
                    <span class="ml-10px text-12px"> 小程序 </span>
                </div>
                <div class="copyright flex items-center">
                    <img src="@/assets/images/login/copyright.webp" alt="" width="12px" height="12px" />
                    <span class="ml-10px text-12px"> 浙公网安备 33020002001220号 </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import { Ref } from 'vue';
import { LoginUserInfo } from '@/typings';
import { setRoutes } from '@/router/set-routes';
import useTabbarStore from '@/store/modules/tabbar';
import logo from '@/assets/images/logo.webp';
import { Expand } from '@vicons/ionicons5';
import { LoginRequest } from '@/api/apis/stargate/api/v1/auth';
import { utils } from 'wp-utils';
import { CountdownInst } from 'naive-ui';
import base from '@/config/base';

const loginType = ref<LoginType[]>([
    {
        name: '账号密码登录',
        tip: ['账号', '密码', '图形验证码'],
        formKey: ['account', 'password', 'captcha'],
        kind: 'account_and_password',
        isActive: true
    },
    {
        name: '验证码登录',
        tip: ['手机号', '验证码', '图形验证码'],
        formKey: ['account', 'captcha', 'imgCaptcha'],
        kind: 'mobile_and_sms_captcha',
        isActive: false
    },
    {
        name: '重置密码',
        tip: ['手机号', '验证码', '图形验证码', '新密码'],
        formKey: ['account', 'captcha', 'imgCaptcha', 'password'],
        kind: 'find_back_the_password',
        isActive: false,
        hiddenInTab: true
    }
]);
const imgCode = ref('');

const store = useStore();
const router = useRouter();
const tabbarStore = useTabbarStore();

/**
 * 清空 token tabs
 */
store.setToken();
store.setUserInfo(null);
tabbarStore.clear();

const userForm = ref<LoginRequest>({
    account: '',
    password: '',
    captcha: '',
    captchaId: '',
    imgCaptcha: ''
});

const countDown = ref(0) as Ref<number>;
const countdownRef = ref<CountdownInst[]>([]);
const handleCountDown = async () => {
    countdownRef.value[0]?.reset();
    if (countDown.value === 0) {
        if (!userForm.value.account) return window.$message.error('请输入正确的手机号');
        if (!userForm.value.imgCaptcha) return window.$message.error('此操作需要效验图形验证码，请输入图形验证码');
        await $apis.stargate.api.v1.auth
            .send_sms_captcha({
                account: userForm.value.account,
                kind: loginType.value[2].isActive ? 'sms_reset_password' : 'sms_login',
                imgCaptcha: userForm.value.imgCaptcha,
                imgCaptchaId: userForm.value.captchaId
            })
            .then(() => {
                window.$message.success('验证码已发送');
                countDown.value = 60000;
            })
            .catch(() => {
                init();
            });
    }
};
const countdownRender = computed(() => {
    return (props: { day: number; hours: number; minutes: number; seconds: number; milliseconds: number }) => {
        return countDown.value > 0 ? `${props.seconds}秒进行重试` : '获取验证码';
    };
});
const goFinish = () => {
    countDown.value = 0;
};

const login = async () => {
    const currentLoginType: LoginType = loginType.value.filter((v: LoginType) => v.isActive)[0];
    if (!userForm.value[currentLoginType.formKey[0]]) return window.$message.error(`请输入${currentLoginType.tip[0]}`);
    if (!userForm.value[currentLoginType.formKey[1]]) return window.$message.error(`请输入${currentLoginType.tip[1]}`);
    if (loginType.value[0].isActive && !userForm.value[currentLoginType.formKey[2]])
        return window.$message.error(`请输入${currentLoginType.tip[2]}`);
    try {
        const data: LoginRequest = {
            account: userForm.value.account,
            password: userForm.value.password,
            captcha: userForm.value.captcha,
            captchaId: userForm.value.captchaId,
            loginKind: currentLoginType.kind,
            deviceKind: 'web'
        };
        const res: LoginInfo = await $apis.stargate.api.v1.auth.login(data);
        await store.setToken(res.data.accessToken);
        const userRes = await api.sass.api.v1.user.info();
        await store.setUserInfo(userRes.data);
        await setRoutes();
        await router.push('/');
    } catch {
        init();
    }
};

// 重置密码
const resetPassword = async () => {
    const currentLoginType: LoginType = loginType.value.filter((v: LoginType) => v.isActive)[0];
    if (!userForm.value[currentLoginType.formKey[0]]) return window.$message.error(`请输入${currentLoginType.tip[0]}`);
    if (!userForm.value[currentLoginType.formKey[1]]) return window.$message.error(`请输入${currentLoginType.tip[1]}`);
    if (!userForm.value[currentLoginType.formKey[2]]) return window.$message.error(`请输入${currentLoginType.tip[2]}`);
    if (!utils.checkPassword(userForm.value.password as string)) {
        return window.$message.error('请输入8-20位,且同时包含大小写字母、数字以及特殊字符的密码！');
    }

    await $apis.stargate.api.v1.auth.reset(userForm.value);
    window.$message.success('重置密码成功');
    nextTick(() => {
        handleForgetPassword(false);
    });
};

// 切换登录模式
const changeLoginType = (value: string) => {
    loginType.value.forEach((v: LoginType) => {
        v.isActive = v.name === value;
    });
    userForm.value = {
        account: '',
        password: '',
        captcha: '',
        captchaId: '',
        imgCaptcha: ''
    };
    init();
};
// 当前激活的tab
const currentActiveTab = computed(() => {
    const activeTab = loginType.value.find((item) => item.isActive);
    return activeTab ? activeTab.name : '';
});

// 获取接口加密状态
const getApiEncryptStatus = async () => {
    const res = await $apis.security.encrypt.status();
    store.setApiEncryptStatus(res.data?.openRequestDecrypt || false);
};

// 初始化
const init = async () => {
    await getApiEncryptStatus();
    const res = await $apis.stargate.api.v1.auth.img_captcha();
    userForm.value.captchaId = res.data.captchaId;
    imgCode.value = res.data.captchaBase64;
};

// 忘记密码
const isForgetPassword = ref(false);
const handleForgetPassword = (value: boolean) => {
    userForm.value = {
        account: '',
        password: '',
        captcha: '',
        captchaId: '',
        imgCaptcha: ''
    };
    isForgetPassword.value = value;
    if (value) changeLoginType('重置密码');
    else changeLoginType('账号密码登录');
    init();
};

onMounted(init);

interface LoginType {
    name: string;
    tip: string[];
    formKey: ('account' | 'password' | 'captcha' | 'imgCaptcha')[];
    kind: string;
    isActive: boolean;
    hiddenInTab?: boolean;
}

interface LoginInfo {
    data: {
        accessToken: string;
        user: LoginUserInfo;
    };
}
</script>

<style scoped lang="less">
.login {
    .right {
        :deep(.n-tabs) {
            .n-tabs-tab {
                .n-tabs-tab__label {
                    font-size: 16px;
                    font-weight: 500;
                    color: #959595;
                }
                &.n-tabs-tab--active {
                    .n-tabs-tab__label {
                        font-size: 20px;
                        font-weight: 500;
                        color: #005eff;
                    }
                }
            }
            .n-tabs-bar {
                display: none;
            }
        }

        .list {
            background: #ffffff;
            flex-direction: column;
            display: flex;
            justify-content: space-between;
            padding: 12px 10px;
            // margin: 10px auto;
            p {
                margin: 0;
                padding: 0;
                font-family: PingFang SC;
                font-size: 18px;
                font-weight: 500;
                line-height: normal;
                letter-spacing: 0px;
                color: #41474f;
            }
            :deep(.n-input) {
                flex: 1;
                font-size: 16px;
                margin-top: 5px;
                padding: 5px 0;
                outline: none;
                --n-border: none !important;
                --n-border-hover: none !important;
                --n-border-focus: none !important;
                --n-border-radius: 0 !important;
                --n-box-shadow-focus: none !important;
                border-bottom: 1px solid #bdbdbd !important;
                .n-input-wrapper {
                    padding: 0 !important;
                    display: flex;
                    align-items: end;
                }
            }
            .code {
                display: inline-block;
                border-radius: 5px;
                color: #366ef4;
                cursor: pointer;
                margin-right: 10px;
                overflow: hidden;
                &.coding {
                    color: #aaaaaa;
                    cursor: not-allowed;
                }
                &.img {
                    width: 90px;
                    height: 30px;
                    background: #989fb6;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
        .fixed {
            position: relative;
        }
        .btn {
            margin: auto;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 18px;
            cursor: pointer;
            left: 957px;
            top: 696px;
            width: 95%;
            height: 58px;
            border-radius: 179px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            opacity: 1;
            background: linear-gradient(270deg, #4a9bff 3%, #005eff 99%),
                linear-gradient(270deg, #00c9ff 4%, #68f0c9 99%);
        }
    }
}

@media screen and (max-width: 1200px) {
    .login {
        .right {
            width: 350px;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            flex: 1;
        }
        .left {
            display: none;
        }
    }
}
</style>
