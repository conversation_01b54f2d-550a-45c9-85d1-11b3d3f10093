<template>
    <alert-content :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="110px"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="12" :x-gap="16">
                <n-form-item-gi :span="12" label="申请人">
                    <n-input v-model:value="params.applicant" readonly disabled />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="申请日期">
                    <n-input :value="dayjs(params.applyDate).format('YYYY-MM-DD')" readonly disabled />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发放类型" path="distributeType" required>
                    <n-select
                        v-model:value="params.distributeType"
                        :options="issuanceTypeOptions"
                        placeholder="请选择发放类型"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类型" path="fileType" required>
                    <n-select
                        v-model:value="params.fileType"
                        :options="fileTypeOptions"
                        placeholder="请选择文件类型"
                        clearable
                        @update:value="onFileTypeChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类别" path="typeDictNodeId" required>
                    <SelectTreeDictionary
                        v-model:value="params.typeDictNodeId"
                        placeholder="请选择文件类别"
                        clearable
                        filterable
                        :disabled="!params.fileType"
                        :need-path-info="true"
                        @change="onTypeDictNodeIdChange"
                        :params="params.fileType === 1 ? 'internal' : params.fileType === 2 ? 'external' : ''"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发放原因" path="reason" required>
                    <n-select
                        v-model:value="params.reason"
                        :options="reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                        @update:value="onReasonChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="params.reason === '其他'" :span="24" label="其他原因" path="otherReason" required>
                    <n-input
                        v-model:value="params.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="期望发放日期" path="wishDistributeDate">
                    <n-date-picker
                        v-model:value="params.wishDistributeDate"
                        type="date"
                        placeholder="请选择期望发放日期"
                        clearable
                        format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-32px mb-10px required-field">发放清单</span>
                            <n-button
                                type="primary"
                                size="tiny"
                                :disabled="!params.typeDictNodeId"
                                @click="distributionListVxeTableRef.addFile()"
                            >
                                增加文件
                            </n-button>
                        </div>
                        <distribution-list-vxe-table
                            ref="distributionListVxeTableRef"
                            v-model="params.distributeList"
                            :fileType="params.fileType ?? undefined"
                            :fileCategory="params.typeDictNodeId ?? undefined"
                            :issuanceType="params.distributeType ?? undefined"
                            :type="props.type"
                        />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import type { ButtonsConfig } from '@/components/alert-content.vue';
import DistributionListVxeTable from '../components/distribution-list-vxe-table.vue';

const store = useStore();
const formRef = ref<FormInst>();
const pathString = ref('');

const buttons: ButtonsConfig = {
    save: {
        text: '提交',
        onClick() {
            return onSubmit();
        }
    },
    extra: {
        saveTemp: {
            text: '暂存',
            type: 'success',
            onClick: () => {
                return onSubmit('saveTemp');
            }
        }
    }
};

const props = defineProps<{
    oId: string;
    row: any;
    type: 'add' | 'edit';
}>();

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];
const reasonOptions = [
    { label: '新员工入职', value: '新员工入职' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '新增业务/流程实施', value: '新增业务/流程实施' },
    { label: '跨部门协作需求', value: '跨部门协作需求' },
    { label: '其他', value: '其他' }
];

const params = reactive({
    applicant: store.userInfo.nickname || '自动生成',
    applyDate: dayjs().valueOf(),
    distributeType: null,
    fileType: null,
    typeDictNodeId: null,
    reason: null,
    otherReason: null,
    wishDistributeDate: null,
    distributeList: []
});
const initFormData = async () => {
    if (props.row && props.row !== false && props.oId) {
        try {
            const res = await $apis.nebula.api.v1.issuanceApplication.getDetail(props.oId);

            if (res && res.data) {
                const detail = res.data;
                // 填充表单数据
                params.applicant = detail.applicantName || detail.applicant;
                params.applyDate = detail.applyDate;
                params.distributeType = detail.distributeType;
                params.fileType = detail.fileType;
                params.typeDictNodeId = detail.typeDictNodeId;
                params.reason = detail.reason;
                params.otherReason = detail.otherReason;
                params.wishDistributeDate = detail.wishDistributeDate !== 0 ? detail.wishDistributeDate : null;
                pathString.value = detail.fileCategory;
                // 发放清单数据
                if (detail.distributeList && detail.distributeList.length > 0) {
                    params.distributeList = detail.distributeList.map((item: any) => {
                        // 构建权限列表
                        const permissions = (item.permissions || []).map((perm: any) => {
                            const mappedReceivedBy = (perm.receivedBy || []).map((user: any) => ({
                                userId: user.userId,
                                userName: user.userNickname || user.userName,
                                name: user.userNickname || user.userName || user.name
                            }));

                            return {
                                fileForm: Number(perm.fileForm),
                                filePermission: Number(perm.filePermission),
                                recipient: perm.recipient || '',
                                receivedBy: mappedReceivedBy
                            };
                        });
                        return {
                            fileId: item.fileId,
                            fileName: item.fileName,
                            number: item.number,
                            version: item.version,
                            permissions: permissions
                        };
                    });
                } else {
                    params.distributeList = [];
                }

                // 在编辑模式下，数据初始化完成后调用过滤功能
                if (props.type === 'edit') {
                    nextTick(() => {
                        distributionListVxeTableRef.value?.filterAllPersons();
                    });
                }
            }
        } catch (error) {
            window.$message.error('获取编辑数据失败');
        }
    }
};

const rules = computed<FormRules>(() => {
    return {
        distributeType: { required: true, type: 'number', message: '请选择发放类型', trigger: 'change' },
        fileType: { required: true, type: 'number', message: '请选择文件类型', trigger: 'change' },
        typeDictNodeId: { required: true, message: '请选择文件类别', trigger: 'change' },
        reason: { required: true, message: '请选择发放原因', trigger: 'change' },
        otherReason: { required: params.reason === '其他', message: '请输入其他原因', trigger: 'blur' },
        distributeList: [
            {
                required: true,
                validator: () => {
                    if (!params.distributeList.length) {
                        return new Error('请填写发放清单');
                    }
                    return true;
                }
            }
        ]
    };
});

const onFileTypeChange = () => {
    params.typeDictNodeId = null;
    // 清空发放清单
    params.distributeList = [];
};

const onReasonChange = (val: string) => {
    if (val !== '其他') {
        params.otherReason = null;
    }
};

const onTypeDictNodeIdChange = (val: any, pathInfo: any) => {
    pathString.value = pathInfo.pathString;
    // 清空发放清单
    params.distributeList = [];
};
const onSubmit = async (type: 'submit' | 'saveTemp' = 'submit') => {
    try {
        await formRef.value?.validate();
        await distributionListVxeTableRef.value?.tableValid();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    const data = props.row ? { ...params, id: props.oId } : { ...params };
    await new Promise((resolve) => {
        window.$dialog.warning({
            title: '确认提示',
            content: `确认后将${type === 'saveTemp' ? '暂存' : '发起审批流程'}，是否确认？`,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                try {
                    if (type === 'saveTemp') {
                        await $apis.nebula.api.v1.issuanceApplication.setDistributeInfo({
                            ...data,
                            saveMethod: 1
                        });
                        window.$message.success('暂存成功');
                        $alert.dialog.close();
                    } else {
                        const formContent = JSON.stringify({
                            businessId: 'FILE_GRANT',
                            version: '1.0.0',
                            data: {
                                ...data,
                                category: pathString.value
                            }
                        });
                        await $hooks.useApprovalProcess('FILE_GRANT', formContent);
                        window.$message.success('提交成功');
                    }
                    resolve(true);
                } catch (error) {
                    resolve(false);
                }
            }
        });
    });
};

// 组件挂载时也尝试初始化数据
onMounted(() => {
    initFormData();
});

const distributionListVxeTableRef = ref();
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
