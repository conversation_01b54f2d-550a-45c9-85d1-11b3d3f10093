<template>
    <alert-content :showDefaultButtons="false">
        <n-descriptions label-placement="left" :column="2" size="small" class="mb-2">
            <n-descriptions-item label="文件名称">{{ records.fileName }}</n-descriptions-item>
            <n-descriptions-item label="文件编号">{{ records.fileNumber }}</n-descriptions-item>
        </n-descriptions>
        <p>处置记录</p>
        <n-search-table-page
            :data-table-props="{
                columns,
                data: records.disposalRecords,
                size: 'small',
                bordered: true,
                pagination: false,
                scrollX: 1200
            }"
            :search-props="{ show: false }"
            :table-props="{ showPagination: false }"
        >
            <template #table_handoverDate="{ row }">
                <p>{{ dayjs(row.handoverDate).format('YYYY-MM-DD') }}</p>
            </template>
            <template #table_recycleDate="{ row }">
                <p>{{dayjs(row.recycleDate).format('YYYY-MM-DD') }}</p>
            </template>
            <template #table_disposalDate="{ row }">
                <p>{{ dayjs(row.disposalDate).format('YYYY-MM-DD') }}</p>
            </template>
        </n-search-table-page>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AlertContent from '@/components/alert-content.vue';
import dayjs from 'dayjs';

const props = defineProps<{
    id: string;
    fileName?: string;
    fileNo?: string;
}>();

const records = ref<any>({});

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '交还人', key: 'handoverPerson', width: 120 },
    { title: '交还日期', key: 'handoverDate', width: 120 },
    { title: '回收人', key: 'recyclePerson', width: 120 },
    { title: '回收日期', key: 'recycleDate', width: 120 },
    { title: '处置人', key: 'disposalPerson', width: 120 },
    { title: '处置日期', key: 'disposalDate', width: 120 },
    { title: '处置方式', key: 'disposalMethod', width: 120 }
];

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDisposalDetail(props.id);
    records.value = res.data;
});
</script>

<style scoped>
.mb-2 {
    margin-bottom: 16px;
}
</style>
