<template>
    <alert-content :showDefaultButtons="false">
        <n-descriptions label-placement="left" :column="2" size="small" class="mb-2">
            <n-descriptions-item label="文件名称">{{ data.fileName || '-' }}</n-descriptions-item>
            <n-descriptions-item label="文件编号">{{ data.fileNumber || '-' }}</n-descriptions-item>
        </n-descriptions>
        <p>回收记录</p>
        <n-search-table-page
            :data-table-props="{
                columns,
                data: data.recycleRecords || [],
                size: 'small',
                bordered: true,
                pagination: false,
                scrollX: 1800
            }"
            :search-props="{ show: false }"
            :table-props="{ showPagination: false }"
        >
            <template #table_auditors="{ row }">
                <p>{{ row.auditors.join(',') }}</p>
            </template>
            <template #table_approvers="{ row }">
                <p>{{ row.approvers.join(',') }}</p>
            </template>
            <template #table_recycleDate="{ row }">
                <p>{{ dayjs(row.recycleDate).format('YYYY-MM-DD') }}</p>
            </template>
        </n-search-table-page>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, h } from 'vue';
import AlertContent from '@/components/alert-content.vue';
import dayjs from 'dayjs';

const props = defineProps<{ id: string }>();

const data = ref<any>({});

const MAX_DISPLAY = 4;

const expandedStates = ref<Record<string, boolean>>({});

const renderHandover = (val: any, fileForm: number, filePermission: number) => {
    // 添加空值检查
    if (!val.handoverPersons) {
        return '';
    }

    let list: string[] = [];

    if (Array.isArray(val.handoverPersons)) {
        // 根据 fileForm 和 filePermission 过滤人员
        const filteredPersons = val.handoverPersons.filter(
            (person: any) => person.fileForm === fileForm && person.filePermission === filePermission
        );

        // 提取人员名字
        list = filteredPersons.map((person: any) => person.handoverName || '').filter(Boolean);
    }

    // 如果没有数据，返回 '-'
    if (list.length === 0) {
        return '';
    }

    if (list.length <= MAX_DISPLAY) {
        return list.join('、');
    }

    // 生成唯一的状态键
    const stateKey = `${val.id || Math.random()}-${fileForm}-${filePermission}`;
    const isExpanded = expandedStates.value[stateKey] || false;

    const displayList = isExpanded ? list : list.slice(0, MAX_DISPLAY);
    const hasMore = list.length > MAX_DISPLAY;

    const elements: any[] = [];
    displayList.forEach((person: string, index: number) => {
        elements.push(h('span', person));
        if (index < displayList.length - 1) {
            elements.push(h('span', '、'));
        }
    });

    if (hasMore) {
        elements.push(
            h('span', '、'),
            h(
                'span',
                {
                    style: { color: '#2080f0', cursor: 'pointer', fontSize: '12px' },
                    onClick: () => {
                        expandedStates.value[stateKey] = !isExpanded;
                    }
                },
                isExpanded ? '收起' : `+${list.length - MAX_DISPLAY}人`
            )
        );
    }

    return h('div', { style: 'line-height: 1.5;' }, elements);
};

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '回收发起人', key: 'recycleInitiator', width: 100 },
    { title: '回收原因', key: 'recycleReason', width: 120 },
    {
        title: '交还人-内发：电子文件-查阅',
        key: 'handoverEFileLook',
        width: 220,
        render: (row: any) => renderHandover(row, 1, 1)
    },
    {
        title: '交还人-内发：电子文件-查阅/下载',
        key: 'handoverEFileLookAndDownload',
        width: 300,
        render: (row: any) => renderHandover(row, 1, 2)
    },
    {
        title: '交还人-内发：纸质文件-一次下载',
        key: 'handoverPaperDocumentOnceDownload',
        width: 280,
        render: (row: any) => renderHandover(row, 2, 3)
    },
    {
        title: '交还人-内发：电子文件-一次下载',
        key: 'handoverEFileOnceDownload',
        width: 280,
        render: (row: any) => renderHandover(row, 1, 3)
    },
    { title: '审批人', key: 'auditors', width: 100 },
    { title: '批准人', key: 'approvers', width: 100 },
    { title: '回收日期', key: 'recycleDate', width: 100 }
];

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeRecycleInfo(props.id);

    data.value = res.data || {};
});
</script>

<style scoped>
.mb-2 {
    margin-bottom: 16px;
}
</style>
