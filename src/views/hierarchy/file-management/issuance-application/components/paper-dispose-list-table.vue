<template>
    <div class="paper-dispose-list-table">
        <n-data-table
            :columns="columns"
            :data="tableData"
            :row-key="(row) => row.fileId"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleSelectionChange"
            :scroll-x="1400"
            :loading="loading"
        >
            <template #exchangePerson="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="checkedMap[row.fileId] ? checkedMap[row.fileId].exchangePerson : []"
                        @update:value="(val) => handleChange(row, 'exchangePerson', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in getAvailablePersons(row)" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isDispose"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #todo="{ row }">
                <n-button @click="showDisposalRecord(row)" size="tiny" type="primary">处置记录</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
interface TableRow {
    id: string;
    fileId: string;
    fileName: string;
    number: string;
    version: string;
    eFileLook: any;
    eFileLookAndDownload: any;
    eFileOnceDownload: any;
    paperDocumentOnceDownload: any;
}

interface BackDataItem {
    inventoryId: string;
    permissions: {
        fileForm: number;
        filePermission: number;
        receivedBy: string[];
        receivedByNames: string[];
        fileName: string;
        number: string;
        version: string;
        distributeCount: number;
        recycleCount: number;
        disposalCount: number;
    };
}

const props = defineProps<{ modelValue: BackDataItem[]; id: string }>();
const emit = defineEmits(['update:modelValue']);

const columns = ref<any[]>([
    { type: 'selection', width: 50, fixed: 'left' },
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    { key: 'fileName', title: '文件名称', align: 'center', fixed: 'left', minWidth: 160, ellipsis: { tooltip: true } },
    { key: 'number', title: '文件编号', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'version', title: '版本/版次', align: 'center', width: 100, ellipsis: { tooltip: true } },
    {
        key: 'paperDocumentOnceDownload.fileForm',
        title: '文件形式',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true },
        render: (row: any) => (row.paperDocumentOnceDownload.fileForm == 2 ? '纸质文件' : '电子文件')
    },
    {
        key: 'paperDocumentOnceDownload.distributeCount',
        title: '发放份数',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true }
    },
    {
        key: 'paperDocumentOnceDownload.recycleCount',
        title: '回收份数',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true }
    },
    {
        key: 'paperDocumentOnceDownload.disposalCount',
        title: '处置份数',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true }
    },
    {
        key: 'exchangePerson',
        title: '交还人',
        align: 'center',
        ellipsis: { tooltip: true },
        render: (row: any) => formatExchangePerson(row)
    },
    { key: 'todo', title: '操作', align: 'center', fixed: 'right', width: 100 }
]);

const tableData = ref<TableRow[]>([]);
const checkedMap = ref<Record<string, { exchangePerson: string[] }>>({});
const checkedRowKeys = ref<string[]>([]);
const loading = ref(false);

const loadData = async () => {
    loading.value = true;
    try {
        const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.id);

        tableData.value = res.data.data;
        initCheckedMap();
    } finally {
        loading.value = false;
    }
};

function formatExchangePerson(row: any) {
    const {  paperDocumentOnceDownload } = row;
    const allUsers = [
        ...userList(paperDocumentOnceDownload)
    ];
    return allUsers.map((user: any) => user.nickname).join(',');
}

const userList = (item: any) => {
    if (item && item.receivedBy) {
        return item.receivedBy.filter((user: any) => user.status >= 3);
    }
    return [];
};

// 获取所有可处置的人员
function getAvailablePersons(row: any) {
    const {  paperDocumentOnceDownload } = row;
    const allUsers = [
        ...userList(paperDocumentOnceDownload)
    ];
    return allUsers.map((user: any) => {
        return {
            label: user.nickname,
            value: user.userId,
            isDispose: user.status > 3
        };
    });
}
function initCheckedMap() {
    checkedMap.value = {};
    for (const row of tableData.value) {
        checkedMap.value[row.fileId] = { exchangePerson: [] };
    }
    // 回填 modelValue
    for (const item of props.modelValue || []) {
        if (item.permissions && item.permissions.receivedBy) {
            if (checkedMap.value[item.inventoryId]) {
                checkedMap.value[item.inventoryId].exchangePerson = [...item.permissions.receivedBy];
            }
        }
    }
    updateCheckedRowKeys();
}

function updateCheckedRowKeys() {
    checkedRowKeys.value = tableData.value
        .filter((row) => {
            const checked = checkedMap.value[row.fileId];
            const availablePersons = getAvailablePersons(row);
            return checked && checked.exchangePerson.length === availablePersons.length && availablePersons.length > 0;
        })
        .map((row) => row.fileId);
}

function handleChange(row: TableRow, key: 'exchangePerson', val: (string | number)[]) {
    checkedMap.value[row.fileId][key] = val.map(String);
    updateCheckedRowKeys();
    emitBackData();
}

function handleSelectionChange(keys: (string | number)[]) {
    const stringKeys = keys.map(String);
    checkedRowKeys.value = stringKeys;

    for (const row of tableData.value) {
        const isChecked = stringKeys.includes(row.fileId);
        const availablePersons = getAvailablePersons(row);
        const wasChecked =
            checkedMap.value[row.fileId].exchangePerson.length === availablePersons.length &&
            checkedMap.value[row.fileId].exchangePerson.length > 0;
        if (isChecked && !wasChecked) {
            // 选中：全选可处置的人员（过滤掉 isDispose 为 true 的项）
            checkedMap.value[row.fileId].exchangePerson = availablePersons
                .filter((i) => !i.isDispose)
                .map((i) => i.value);
        } else if (!isChecked && wasChecked) {
            checkedMap.value[row.fileId].exchangePerson = [];
        }
        // 其他行不变
    }
    emitBackData();
}

function emitBackData() {
    const backData = Object.entries(checkedMap.value)
        .filter(([, checked]) => checked.exchangePerson.length)
        .map(([fileId, checked]) => {
            // 找到对应的行数据
            const rowData = tableData.value.find((row) => row.fileId === fileId);
            if (!rowData) return null;

            // 获取选中用户的姓名
            const selectedUserNames = checked.exchangePerson.map((userId) => {
                // 从所有可用人员中查找对应的姓名
                const allUsers = getAvailablePersons(rowData);
                const user = allUsers.find((u) => u.value === userId);
                return user ? user.label : userId;
            });

            return {
                inventoryId: rowData.id,
                permissions: {
                    fileForm: 2,
                    filePermission: 3,
                    receivedBy: checked.exchangePerson,
                    receivedByNames: selectedUserNames,
                    fileName: rowData.fileName,
                    number: rowData.number,
                    version: rowData.version,
                    distributeCount: rowData.paperDocumentOnceDownload?.distributeCount || 0,
                    recycleCount: rowData.paperDocumentOnceDownload?.recycleCount || 0,
                    disposalCount: rowData.paperDocumentOnceDownload?.disposalCount || 0
                }
            };
        })
        .filter(Boolean); // 过滤掉 null 值
    console.log(backData);
    emit('update:modelValue', backData);
}

onMounted(() => {
    loadData();
});

const showDisposalRecord = (row: any) => {
    $alert.dialog({
        title: '处置记录',
        content: import('../models/disposal-record.vue'),
        width: '60%',
        props: {
            id: row.id,
      
        }
    });
};
</script>

<style scoped lang="less"></style>
