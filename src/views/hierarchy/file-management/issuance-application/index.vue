<template>
    <div class="min-h-300px mini-hellp-list">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1800,
                maxHeight: 'calc(100vh - 440px)',
                rowKey: (row: any) => row.id
            }"
            :dataApi="$apis.nebula.api.v1.issuanceApplication.getList"
            :params="params"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :search-table-space="{
                size: 20
            }"
            :search-props="{
                showAdd: false,
                showInput: false
            }"
            @reset="onReset"
        >
            <template #search_form_pre>
                <n-space>
                    <n-input
                        v-model:value="params.fileNumber"
                        placeholder="输入文件编号"
                        style="width: 180px"
                        clearable
                    />
                    <n-input
                        v-model:value="params.fileName"
                        placeholder="输入文件名称"
                        style="width: 180px"
                        clearable
                    />
                </n-space>
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="showMore = !showMore">{{ showMore ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="showMore" :show="showMore">
                    <n-space>
                        <n-input
                            v-model:value="params.applicant"
                            placeholder="输入申请人"
                            style="width: 180px"
                            clearable
                        />
                        <n-select
                            v-model:value="params.status"
                            :options="statusOptions"
                            placeholder="选择状态"
                            style="width: 180px"
                            clearable
                        />
                        <n-select
                            v-model:value="params.distributeType"
                            :options="issuanceTypeOptions"
                            placeholder="选择发放类型"
                            style="width: 180px"
                            clearable
                        />
                        <n-select
                            v-model:value="params.fileType"
                            :options="fileTypeOptions"
                            placeholder="选择文件类别"
                            style="width: 180px"
                            clearable
                        />
                        <select-tree-dictionary
                            v-model:value="params.fileCategory"
                            placeholder="选择文件类别"
                            :params="params.fileType === 1 ? 'internal' : params.fileType === 2 ? 'external' : ''"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-space justify="end">
                    <n-permission has="issuanceApplicationAdd">
                        <n-button type="primary" @click="onAddEdit('add')">新增</n-button>
                    </n-permission>
                    <n-permission has="issuanceApplicationExport">
                        <n-button type="warning" @click="exportFile">导出</n-button>
                    </n-permission>
                </n-space>
            </template>
            <template #table_approver_approvers="{ row }">
                <n-ellipsis :line-clamp="1" :tooltip="true">
                    <p>{{ formatApproverList(row.approver, 'approvers') }}</p>
                </n-ellipsis>
            </template>
            <template #table_approver_auditors="{ row }">
                <n-ellipsis :line-clamp="1" :tooltip="true">
                    <p>{{ formatApproverList(row.approver, 'auditors') }}</p>
                </n-ellipsis>
            </template>
            <template #table_reviewer="{ row }">
                <div>
                    <template v-for="(r, idx) in getReviewerList(row)" :key="idx">
                        <div style="line-height: 1.5">{{ r.name }}{{ r.date ? ' ' + r.date : '' }}</div>
                    </template>
                </div>
            </template>
            <template #table_approver="{ row }">
                <div>
                    <template v-for="(a, idx) in getApproverList(row)" :key="idx">
                        <div style="line-height: 1.5">{{ a.name }}{{ a.date ? ' ' + a.date : '' }}</div>
                    </template>
                </div>
            </template>
            <template #table_distributeCount="{ row }">
                <n-popover trigger="hover" placement="top" :show-arrow="true">
                    <template #trigger>
                        <span class="cursor-pointer">{{ row.distributeCount }}</span>
                    </template>
                    <div class="min-w-300px max-w-400px">
                        <div class="text-sm font-medium mb-2 text-gray-700">发放人员清单</div>
                        <div class="space-y-2 max-h-200px overflow-y-auto">
                            <template v-if="getDistributeList(row).length > 0">
                                <template v-for="(p, idx) in getDistributeList(row)" :key="idx">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <span class="font-medium text-gray-800">{{ p.name }}</span>
                                        <div class="flex gap-1">
                                            <n-tag :type="getUserStatusTagType(p.status)" size="small" round>
                                                {{ getUserStatusText(p.status) }}
                                            </n-tag>
                                            <n-tag type="info" size="small" round>
                                                {{ getFileFormText(p.fileForm) }}
                                            </n-tag>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <template v-else>
                                <div class="flex items-center justify-center p-4 text-gray-500">暂无数据</div>
                            </template>
                        </div>
                    </div>
                </n-popover>
            </template>
            <template #table_receivedCount="{ row }">
                <n-popover trigger="hover" placement="top" :show-arrow="true">
                    <template #trigger>
                        <span class="cursor-pointer">{{ row.receivedCount }}</span>
                    </template>
                    <div class="min-w-300px max-w-400px">
                        <div class="text-sm font-medium mb-2 text-gray-700">签收人员清单</div>
                        <div class="space-y-2 max-h-200px overflow-y-auto">
                            <template v-if="getReceivedList(row).length > 0">
                                <template v-for="(p, idx) in getReceivedList(row)" :key="idx">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <span class="font-medium text-gray-800">{{ p.name }}</span>
                                        <div class="flex gap-1">
                                            <n-tag type="success" size="small" round> 已签收 </n-tag>
                                            <n-tag type="info" size="small" round>
                                                {{ getFileFormText(p.fileForm) }}
                                            </n-tag>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <template v-else>
                                <div class="flex items-center justify-center p-4 text-gray-500">暂无数据</div>
                            </template>
                        </div>
                    </div>
                </n-popover>
            </template>
            <template #table_disposalCount="{ row }">
                <n-popover trigger="hover" placement="top" :show-arrow="true">
                    <template #trigger>
                        <span class="cursor-pointer">{{ row.disposalCount }}</span>
                    </template>
                    <div class="min-w-300px max-w-400px">
                        <div class="text-sm font-medium mb-2 text-gray-700">处置人员清单</div>
                        <div class="space-y-2 max-h-200px overflow-y-auto">
                            <template v-if="getRecycledList(row).length > 0">
                                <template v-for="(p, idx) in getRecycledList(row)" :key="idx">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <span class="font-medium text-gray-800">{{ p.name }}</span>
                                        <div class="flex gap-1">
                                            <n-tag type="error" size="small" round> 已处置 </n-tag>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <template v-else>
                                <div class="flex items-center justify-center p-4 text-gray-500">暂无数据</div>
                            </template>
                        </div>
                    </div>
                </n-popover>
            </template>
            <template #table_operation="{ row }">
                <n-space justify="center">
                    <template v-for="(button, index) in getOperationButtons(row)" :key="index">
                        <n-button v-if="button.type === 'button'" v-bind="button.props" @click="button.onClick">
                            {{ button.text }}
                        </n-button>
                        <n-dropdown
                            v-else-if="button.type === 'dropdown'"
                            v-bind="button.props"
                            @select="button.onSelect"
                        >
                            <n-button size="tiny">{{ button.text }}</n-button>
                        </n-dropdown>
                    </template>
                </n-space>
            </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="statusTagType(row.status)">
                    {{ statusLabel(row.status) }}
                </n-tag>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { IssuanceApplicationRow } from '@/api/apis/nebula/api/v1/issuance-application';
import useStore from '@/store/modules/main';
import { DataTableColumns, NTag, NPopover } from 'naive-ui';
import dayjs from 'dayjs';

const showMore = ref(false);

const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];

const statusOptions = [
    { label: '待提交', value: 1 },
    { label: '待审批', value: 2 },
    { label: '已审批', value: 3 },
    { label: '已驳回', value: 4 }
];

const searchTablePageRef = ref();
const params = ref({
    fileNumber: '',
    fileName: '',
    fileType: null,
    fileCategory: null,
    distributeType: null,
    status: null,
    applicant: ''
});

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'serialNumber',
        width: 60,
        align: 'center',
        render: (_: any, index: number) => `${index + 1}`
    },
    {
        title: '发放类型',
        key: 'distributeType',
        align: 'center',
        ellipsis: { tooltip: true },
        resizable: true,
        render: (row: any) => {
            return row.distributeType === 1 ? '内部发放' : row.distributeType === 2 ? '外部发放' : '';
        }
    },
    {
        title: '文件类型',
        key: 'fileType',
        align: 'center',
        ellipsis: { tooltip: true },
        resizable: true,
        render: (row: any) => {
            return row.fileType === 1 ? '内部文件' : row.fileType === 2 ? '外部文件' : '';
        }
    },
    { title: '文件类别', key: 'fileCategory', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '申请日期',
        key: 'applyDate',
        align: 'center',
        minWidth: 180,
        ellipsis: { tooltip: true },
        resizable: true,
        render: (row: any) => {
            return row.applyDate ? dayjs(row.applyDate).format('YYYY-MM-DD') : '';
        }
    },
    {
        title: '期望发放日期',
        key: 'wishDistributeDate',
        align: 'center',
        ellipsis: { tooltip: true },
        resizable: true,
        render: (row: any) => {
            return row.wishDistributeDate ? dayjs(row.wishDistributeDate).format('YYYY-MM-DD') : '';
        }
    },
    { title: '审核人', key: 'approver_approvers', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '批准人', key: 'approver_auditors', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '发放份数', key: 'distributeCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '签收份数', key: 'receivedCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '处置份数', key: 'disposalCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '状态', key: 'status', align: 'center', width: 100 },
    { title: '操作', key: 'operation', align: 'center', width: 180, fixed: 'right' }
];

const store = useStore();

const getOperationButtons = (row: any) => {
    const buttons: any[] = [];
    const buttonDefault: any[] = [];
    const perms = store.permissions;
    if (perms.includes('issuanceApplicationDistributeDetail')) {
        buttonDefault.push({
            type: 'button',
            props: {
                type: 'primary',
                size: 'tiny'
            },
            text: '详情',
            onClick: () => onDistributeDetail(row)
        });
    }
    if (perms.includes('issuanceApplicationCancel') && row.status === 2) {
        buttonDefault.push({
            type: 'button',
            props: {
                type: 'error',
                size: 'tiny'
            },
            text: '撤销',
            onClick: () => onCancel(row)
        });
    }
    if (perms.includes('issuanceApplicationRecycle') && row.status === 3) {
        buttonDefault.push({
            type: 'button',
            props: {
                type: 'warning',
                size: 'tiny'
            },
            text: '回收',
            onClick: () => onRecycle(row)
        });
    }
    if (perms.includes('issuanceApplicationEdit') && (row.status === 1 || row.status === 4)) {
        buttons.push({
            type: 'button',
            props: {
                type: 'info',
                size: 'tiny'
            },
            text: '编辑',
            onClick: () => onAddEdit('edit', row.id || '', row)
        });
    }

    if (perms.includes('issuanceApplicationPaperDispose') && row.status === 3) {
        buttons.push({
            type: 'button',
            props: {
                type: 'error',
                size: 'tiny'
            },
            text: '纸质文件处置',
            onClick: () => onPaperDisposal(row)
        });
    }

    if (perms.includes('issuanceApplicationPaperDetail') && row.status === 3) {
        buttons.push({
            type: 'button',
            props: {
                type: 'warning',
                size: 'tiny'
            },
            text: '纸质文件处置详情',
            onClick: () => onPaperDetail(row)
        });
    }

    if (perms.includes('issuanceApplicationDelete') && (row.status === 1 || row.status === 4)) {
        buttons.push({
            type: 'button',
            props: {
                type: 'error',
                size: 'tiny'
            },
            text: '删除',
            onClick: () => onDelete(row)
        });
    }

    const visibleButtons = buttonDefault.slice(0, 2);
    const moreButtons = buttons;

    if (moreButtons.length > 0) {
        const moreOptions = moreButtons.map((btn) => ({
            label: btn.text,
            key: btn.text
        }));

        visibleButtons.push({
            type: 'dropdown',
            props: {
                trigger: 'click',
                options: moreOptions
            },
            text: '更多',
            onSelect: (key: string) => {
                const selectedButton = moreButtons.find((btn) => btn.text === key);
                if (selectedButton) {
                    selectedButton.onClick();
                }
            }
        });
    }

    return visibleButtons;
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};
const onReset = () => {
    params.value = {
        fileNumber: '',
        fileName: '',
        fileType: null,
        fileCategory: null,
        distributeType: null,
        status: null,
        applicant: ''
    };
    init();
};

const onAddEdit = (type: 'add' | 'edit', oId?: string, row?: IssuanceApplicationRow) => {
    $alert.dialog({
        title: `${type === 'add' ? '新增' : '编辑'}发放申请`,
        width: '60%',
        content: import('./models/issuance-form.vue'),
        props: {
            oId: oId || '',
            row: row || false,
            type,
            onSave: () => init()
        }
    });
};
const exportFile = () => {
    window.$dialog.warning({
        title: '提示',
        content: '确认后将导出该筛选条件下的数据，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.internal.export({
                moduleType: 4,
                params: params.value
            });
            window.$message.info('数据导出成功，请在右上角下拉菜单【数据导出】中查看');
        }
    });
};

const onDistributeDetail = (_row: any) => {
    $alert.dialog({
        title: h('div', { class: 'flex items-center' }, [
            h('span', { class: 'mr-10px' }, '发放回收详情'),
            ...(_row.status == 3
                ? [
                      h(
                          NTag,
                          {
                              type:
                                  _row.recycleStatus === '未回收'
                                      ? 'error'
                                      : _row.recycleStatus == '部分回收'
                                      ? 'warning'
                                      : 'success',
                              size: 'small'
                          },
                          () => _row.recycleStatus
                      )
                  ]
                : [])
        ]),
        width: '60%',
        content: import('./models/issuance-detail.vue'),
        props: {
            row: _row
        }
    });
};
const onPaperDetail = async (_row: any) => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(_row.id);
    const list = res.data.data;
    console.log(res.data, 'list');

    $alert.dialog({
        title: h('div', { class: 'flex items-center' }, [
            h('span', { class: 'mr-10px' }, '纸质文件处置详情'),
            ...(res.data.disposalStatus
                ? [
                      h(
                          NTag,
                          {
                              type:
                                  res.data.disposalStatus === '未处置'
                                      ? 'error'
                                      : res.data.disposalStatus == '部分处置'
                                      ? 'warning'
                                      : 'success',
                              size: 'small'
                          },
                          () => res.data.disposalStatus
                      )
                  ]
                : [])
        ]),
        width: '60%',
        content: import('./models/disposal-detail.vue'),
        props: {
            row: _row,
            list
        }
    });
};

const onPaperDisposal = (_row: any) => {
    $alert.dialog({
        title: '纸质文件处置',
        width: '60%',
        content: import('./models/paper-disposal-form.vue'),
        props: {
            row: _row
        }
    });
};

const onCancel = (_row: any) => {
    console.log(_row);
    window.$dialog.warning({
        title: '撤销文件',
        content: `确认撤销文件，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await api.sass.api.v1.workflow.workflow.reject(_row.workflowId);
            window.$message.success(`文件撤销已提交，请等待系统处理`);
            setTimeout(() => {
                init();
            }, 3000);
        }
    });
};

const onRecycle = (_row: any) => {
    $alert.dialog({
        title: '回收文件',
        width: '60%',
        content: import('./models/recycle-form.vue'),
        props: {
            row: _row
        }
    });
};

const onDelete = (_row: any) => {
    window.$dialog.error({
        title: '确认提示',
        content: '确认后将删除文件发放申请，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.issuanceApplication.deleteDistributeInfo(_row.id);
            window.$message.success(`文件发放申请删除成功`);
            init();
            $alert.dialog.close();
        }
    });
};

const statusTagType = (status: number) => {
    switch (status) {
        case 1:
            return 'default';
        case 2:
            return 'warning';
        case 4:
            return 'error';
        case 3:
            return 'success';
        default:
            return 'default';
    }
};
const statusLabel = (status: number) => {
    switch (status) {
        case 1:
            return '待提交';
        case 2:
            return '待审批';
        case 3:
            return '已审批';
        case 4:
            return '已驳回';
        default:
            return '未知状态';
    }
};

const getUserStatusText = (status?: number) => {
    switch (status) {
        case 1:
            return '已签收';
        case 2:
            return '未签收';
        case 3:
            return '已回收';
        case 4:
            return '已处置';
        default:
            return '';
    }
};

const getFileFormText = (fileForm?: number) => {
    switch (fileForm) {
        case 1:
            return '电子文件';
        case 2:
            return '纸质文件';
        default:
            return '';
    }
};

const getUserStatusTagType = (status?: number) => {
    switch (status) {
        case 1:
            return 'success';
        case 2:
            return 'warning';
        case 3:
            return 'error';
        case 4:
            return 'info';
        default:
            return 'default';
    }
};

const getReviewerList = (row: any): Array<{ name: string; date?: string }> => {
    if (Array.isArray(row.reviewer)) {
        return row.reviewer;
    }
    if (row.reviewer) {
        return [{ name: row.reviewer, date: row.reviewerDate }];
    }
    return [];
};

const getApproverList = (row: any): Array<{ name: string; date?: string }> => {
    if (Array.isArray(row.approver)) {
        return row.approver;
    }
    if (row.approver) {
        return [{ name: row.approver, date: row.approverDate }];
    }
    return [];
};

const formatApproverList = (row: any, type: 'approvers' | 'auditors') => {
    if (type === 'approvers') {
        if (row.approvers) {
            return row.approvers.map((a: any) => a.userNickname).join(', ');
        }
        return '';
    }
    if (type === 'auditors') {
        if (row.auditors) {
            return row.auditors.map((a: any) => a.userNickname).join(', ');
        }
        return '';
    }
    return '';
};

const getDistributeList = (row: any) => {
    if (!row.distributeUsers) return '';
    return row.distributeUsers.map((item: any) => ({
        name: item.nickname,
        status: item.status,
        fileForm: item.fileForm,
        userId: item.userId
    }));
};

const getReceivedList = (row: any) => {
    if (!row.distributeUsers) return [];
    return row.distributeUsers
        .filter((item: any) => item.status === 1) // 过滤已签收的人员
        .map((item: any) => ({
            name: item.nickname,
            fileForm: item.fileForm
        }));
};

const getRecycledList = (row: any) => {
    if (!row.distributeUsers) return [];
    return row.distributeUsers
        .filter((item: any) => item.status === 4) // 过滤已处置的人员
        .map((item: any) => ({
            name: item.nickname
        }));
};
</script>

<style scoped lang="less"></style>
