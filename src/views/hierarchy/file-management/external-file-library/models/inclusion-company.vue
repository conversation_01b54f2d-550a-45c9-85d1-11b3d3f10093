<template>
    <alert-content :on-default-save="handleSubmit">
        <n-form
            :model="formModel"
            label-placement="left"
            label-width="100"
            style="box-sizing: border-box; padding: 20px 0"
        >
            <n-grid :cols="2" :x-gap="24">
                <n-gi>
                    <n-form-item label="原文件编号">
                        <n-input
                            v-model:value="formModel.originalNumber"
                            placeholder="原文件编号"
                            maxlength="50"
                            show-count
                        />
                    </n-form-item>
                </n-gi>
                <n-gi>
                    <n-form-item label="原版本/版次">
                        <n-input
                            v-model:value="formModel.originalVersion"
                            placeholder="原版本/版次"
                            maxlength="50"
                            show-count
                        />
                    </n-form-item>
                </n-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import useStore from '@/store/modules/main';

const formModel = ref({
    originalNumber: '',
    originalVersion: ''
});

const store = useStore();

const props = defineProps<{
    row: any;
}>();

const handleSubmit = async () => {
    const formContent = JSON.stringify({
        businessId: 'FILE_INCORPORATE',
        version: '1.0.0',
        data: {
            data: [
                {
                    fileName: props.row.name,
                    fileNo: props.row.number,
                    id: props.row.id,
                    originalNumber: formModel.value.originalNumber,
                    originalVersion: formModel.value.originalVersion
                }
            ],
            nickname: store.userInfo.nickname,
            reason: '纳入公司流程'
        }
    });
    await new Promise((resolve) => {
        window.$dialog.warning({
            title: '确认提交',
            content: '确认后将发起审批，是否确认提交？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                const res = await $apis.nebula.api.v1.external.plagiarismCheck({
                    ids: [props.row.id]
                });
                if (res.code === 0) {
                    await $hooks.useApprovalProcess('FILE_INCORPORATE', formContent);
                    window.$message.success('提交成功，审批通过后将纳入子公司库');
                    resolve(true);
                } else {
                    window.$message.error(res.message || '提交失败');
                    resolve(false);
                }
            }
        });
    });
};
</script>
