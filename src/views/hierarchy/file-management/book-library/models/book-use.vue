<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            ref="formRef"
            label-align="right"
            :model="formData"
            :rules="rules"
            label-placement="left"
            label-width="80px"
            require-mark-placement="left"
        >
            <n-form-item :label="`${props.type === 'receive' ? '领用' : '借用'}原因`" path="useReason">
                <n-input
                    v-model:value="formData.useReason"
                    type="textarea"
                    maxlength="50"
                    show-count
                    placeholder="请输入领用/借用原因"
                    clearable
                />
            </n-form-item>
        </n-form>
    </alert-content>
</template>
<script setup lang="ts">
import { BookRow } from '@/api/apis/nebula/api/v1/book';

const props = defineProps<{
    row: BookRow;
    type: 'receive' | 'borrow';
}>();

const formData = ref({
    useReason: ''
});
const rules = ref({
    useReason: [{ required: true, message: '请输入领用/借用原因', trigger: 'blur' }]
});
const formRef = ref();

const onSubmit = async () => {
    await formRef.value
        ?.validate()
        .then(() => {
            window.$dialog.warning({
                title: '确认提示',
                content: `确认后将${props.type === 'receive' ? '领用' : '借用'}书籍，是否确认？`,
                positiveText: '确认',
                negativeText: '取消',
                onPositiveClick: async () => {
                    if (props.type === 'receive') {
                        await $apis.nebula.api.v1.book.receiveBook(props.row.id, formData.value.useReason);
                    } else {
                        await $apis.nebula.api.v1.book.borrowBook(props.row.id, formData.value.useReason);
                    }
                    window.$message.success(`书籍${props.row.name}领用成功`);
                    $alert.dialog.close();
                }
            });
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};
</script>
<style scoped lang="less"></style>
