<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            class="flex-v gap-y-10px"
            ref="formRef"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="80px"
            :show-feedback="false"
        >
            <n-form-item :span="24" label="发放原因" path="reason">
                <n-select
                    v-model:value="params.reason"
                    :options="reasonOptions"
                    placeholder="请选择发放原因"
                    clearable
                    @update:value="onReasonChange"
                />
            </n-form-item>
            <n-form-item v-if="params.reason === '其他'" label="其他原因" path="otherReason">
                <n-input v-model:value="params.otherReason" maxlength="50" placeholder="请输入其他原因" show-count />
            </n-form-item>
            <n-form-item v-if="type === 4" label="接收方" path="recipient">
                <n-input v-model:value="params.recipient" maxlength="50" placeholder="请输入其他原因" show-count />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import { FormRules } from 'naive-ui';

const props = defineProps<{
    row: any;
    type: number;
    pType: string;
    persons: { key: string; label: string }[];
}>();

const store = useStore();

const params = reactive({
    reason: null,
    otherReason: null,
    recipient: null
});
const rules = computed<FormRules>(() => {
    return {
        reason: { required: true, message: '请选择发放原因', trigger: ['change', 'blur'] },
        otherReason: { required: params.reason === '其他', message: '请输入其他原因', trigger: ['blur', 'change'] },
        recipient: { required: true, message: '请输入接收方', trigger: ['blur', 'change'] }
    };
});

const reasonOptions = [
    { label: '新员工入职', value: '新员工入职' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '新增业务/流程实施', value: '新增业务/流程实施' },
    { label: '跨部门协作需求', value: '跨部门协作需求' },
    { label: '其他', value: '其他' }
];

const onReasonChange = (val: string) => {
    if (val !== '其他') {
        params.otherReason = null;
    }
};

const formRef = ref();
const onSubmit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    await new Promise((resolve) => {
        window.$dialog.info({
            title: '确认提示',
            content: `确认后将发起发放审批流程，是否确认？`,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                try {
                    // 根据 type 确定文件权限类型、文件形式和发放类型
                    const typeConfig: Record<
                        number,
                        { filePermission: number; fileForm: number; distributeType: number }
                    > = {
                        1: { filePermission: 1, fileForm: 1, distributeType: 1 }, // 内发：电子文件 - 查询
                        2: { filePermission: 2, fileForm: 1, distributeType: 1 }, // 内发：电子文件 - 查询/下载
                        3: { filePermission: 3, fileForm: 2, distributeType: 1 }, // 内发：纸质文件 - 一次下载
                        4: { filePermission: 3, fileForm: 1, distributeType: 2 } // 外发：电子文件 - 一次下载
                    };

                    const { filePermission, fileForm, distributeType } = typeConfig[props.type] || {
                        filePermission: 1,
                        fileForm: 1,
                        distributeType: 1
                    };

                    // 兼容各种 row 结构
                    const fileId = props.row.id || props.row.fileId || '';
                    const fileName =
                        (props.row.fileInfo && props.row.fileInfo.fileName) ||
                        props.row.name ||
                        props.row.fileName ||
                        '';
                    const number = props.row.number || props.row.no || props.row.fileNo || '';
                    const version = props.row.version || props.row.versionNo || '';
                    const typeDictNodeId = props.row.typeDictionaryNodeId || props.row.docCategoryId || '';

                    const distributeList = [
                        {
                            fileId,
                            fileName,
                            number,
                            version,
                            permissions: [
                                {
                                    fileForm,
                                    filePermission,
                                    recipient: params.recipient,
                                    receivedBy: props.persons.map((p) => ({
                                        userId: p.key,
                                        userName: p.label
                                    }))
                                }
                            ]
                        }
                    ];

                    const result = {
                        applicant: store.userInfo.nickname,
                        applyDate: Date.now(),
                        distributeType,
                        fileType: props.pType === 'interior' ? 1 : 2,
                        typeDictNodeId,
                        category: props.row.docCategoryName || props.row.docType,
                        reason: params.reason,
                        otherReason: params.otherReason,
                        wishDistributeDate: null,
                        distributeList
                    };

                    const formData = JSON.stringify({
                        businessId: 'FILE_GRANT',
                        version: '1.0.0',
                        data: result
                    });
                    await $hooks.useApprovalProcess('FILE_GRANT', formData);
                    window.$message.success('发放申请已提交');
                    resolve(true);
                } catch (error) {
                    resolve(false);
                }
            }
        });
    });
};
</script>

<style scoped></style>
