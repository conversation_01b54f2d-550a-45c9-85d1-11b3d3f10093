<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            class="flex-v gap-y-10px"
            ref="formRef"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="80px"
            :show-feedback="false"
        >
            <n-form-item :span="24" label="回收原因" path="reason">
                <n-select
                    v-model:value="params.reason"
                    :options="reasonOptions"
                    placeholder="请选择发放原因"
                    clearable
                    @update:value="onReasonChange"
                />
            </n-form-item>
            <n-form-item v-if="params.reason === '其他'" label="其他原因" path="otherReason">
                <n-input v-model:value="params.otherReason" maxlength="50" placeholder="请输入其他原因" show-count />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import { FormRules } from 'naive-ui';

const props = defineProps<{
    row: any;
    type: number;
    pType: string;
    keys: string[];
    recordData: any;
}>();

const store = useStore();

const params = reactive({
    reason: null,
    otherReason: null,
    recipient: null
});
const rules = computed<FormRules>(() => {
    return {
        reason: { required: true, message: '请选择发放原因', trigger: ['change', 'blur'] },
        otherReason: { required: params.reason === '其他', message: '请输入其他原因', trigger: ['blur', 'change'] }
    };
});

const reasonOptions = [
    { label: '员工离职/调离', value: '员工离职/调离' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '文件正式作废', value: '文件正式作废' },
    { label: '文件过期失效', value: '文件过期失效' },
    { label: '错误发放纠正', value: '错误发放纠正' },
    { label: '定期清理', value: '定期清理' },
    { label: '其他', value: '其他' }
];
const onReasonChange = (val: string) => {
    if (val !== '其他') {
        params.otherReason = null;
    }
};
const formRef = ref();
const onSubmit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    await new Promise((resolve) => {
        window.$dialog.info({
            title: '确认提示',
            content: `确认后将发起回收审批流程，是否确认？`,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                try {
                    // 根据 type 确定文件权限类型和文件形式
                    const typeConfig: Record<number, { filePermission: number; fileForm: number }> = {
                        1: { filePermission: 1, fileForm: 1 }, // 内发：电子文件 - 查询
                        2: { filePermission: 2, fileForm: 1 }, // 内发：电子文件 - 查询/下载
                        3: { filePermission: 3, fileForm: 2 }, // 内发：纸质文件 - 一次下载
                        4: { filePermission: 3, fileForm: 1 } // 外发：电子文件 - 一次下载
                    };

                    const { filePermission, fileForm } = typeConfig[props.type] || { filePermission: 1, fileForm: 1 };

                    // 从 recordData 中获取对应权限的人员信息
                    const filteredRecordData = props.recordData.filter((item: any) => {
                        return item.fileForm === fileForm && item.filePermission === filePermission;
                    });

                    // 根据 keys 过滤出要回收的人员
                    const recycleUsers = filteredRecordData.filter((item: any) => props.keys.includes(item.userId));

                    // 按 inventoryId 分组用户
                    const groupedByInventory = new Map();
                    recycleUsers.forEach((user: any) => {
                        const inventoryId = user.inventoryId;
                        if (!groupedByInventory.has(inventoryId)) {
                            groupedByInventory.set(inventoryId, []);
                        }
                        groupedByInventory.get(inventoryId).push(user);
                    });

                    // 构建 recycleList，按 inventoryId 分组
                    const recycleList = Array.from(groupedByInventory.entries()).map(([inventoryId, users]) => ({
                        inventoryId: inventoryId,
                        permissions: [
                            {
                                fileForm: fileForm,
                                filePermission: filePermission,
                                receivedBy: users.map((user: any) => user.userId)
                            }
                        ]
                    }));

                    const recycleListInfo = [
                        {
                            fileName: props.row.fileName || props.row.name || '',
                            number: props.row.number || props.row.no || '',
                            fileForm: props.row.version || props.row.versionNo || '',
                            internalFileRead:
                                filePermission === 1
                                    ? recycleUsers.map((user: any) => ({
                                          label: user.userNickName || user.nickname,
                                          value: user.userNickName || user.nickname
                                      }))
                                    : [],
                            internalFileReadAndDownload:
                                filePermission === 2
                                    ? recycleUsers.map((user: any) => ({
                                          label: user.userNickName || user.nickname,
                                          value: user.userNickName || user.nickname
                                      }))
                                    : [],
                            internalFileOneDownload:
                                filePermission === 3
                                    ? recycleUsers.map((user: any) => ({
                                          label: user.userNickName || user.nickname,
                                          value: user.userNickName || user.nickname
                                      }))
                                    : []
                        }
                    ];

                    const recycleData = {
                        recycleDate: dayjs().valueOf(),
                        recycleReason: '其他',
                        otherReason: '回收文件',
                        recycleList: recycleList,
                        // 回收人信息
                        recycler: {
                            nickname: store.userInfo.nickname,
                            recycleDate: dayjs().format('YYYY-MM-DD')
                        },
                        // 回收原因
                        recycleReasonInfo: {
                            reason: '其他',
                            otherReason: '回收文件'
                        },
                        // 发放信息
                        distributeData: {
                            distributeTypeText: props.type <= 3 ? '内部发放' : '外部发放',
                            fileTypeText: props.pType === 'interior' ? '内部文件' : '外部文件',
                            fileCategory: props.row.docCategoryName || props.row.docType || '',
                            wishDistributeDateText: dayjs(new Date().getTime()).format('YYYY-MM-DD')
                        },
                        recycleListInfo
                    };

                    const formData = JSON.stringify({
                        businessId: 'FILE_RECLAIM',
                        version: '1.0.0',
                        data: recycleData
                    });

                    await $hooks.useApprovalProcess('FILE_RECLAIM', formData);
                    window.$message.success('回收流程已发起');
                    resolve(true);
                } catch (error) {
                    resolve(false);
                }
            }
        });
    });
};
</script>

<style scoped></style>
