<template>
    <div class="approval-process">
        <div class="approval-title">
            <span>{{ title || sections.title }}</span>
            <n-tag round :bordered="false" size="small" :type="sections.tagType" class="ml-16px font-normal">{{
                sections.tag
            }}</n-tag>
        </div>
        <div class="approval-content">
            <div class="approval-row" v-for="(item, i) in sections.approvers" :key="i">
                <div class="label">{{ item.label }}</div>
                <div class="value">{{ item.name }}</div>
                <div class="label">{{ item.dateLabel }}</div>
                <div class="value">{{ item.date }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        flowId: string;
        title?: string;
    }>(),
    {
        flowId: ''
    }
);
const emit = defineEmits(['update:flowId']);
const { flowId } = useVModels(props, emit);

type ApproverStatus = 'passed' | 'rejected' | 'pending';

interface FlowData {
    flowName: string;
    nodes: Node[];
    formContent: string; // JSON 字符串
}

interface Node {
    nodeName: string;
    signingKind: 'or' | 'and';
    updatedAt: number;
    approvers: Approver[];
}

interface Approver {
    approverId?: string;
    approverNickname: string;
    status: ApproverStatus;
    updatedAt: number;
}

interface TransformedApprover {
    label: string;
    name: string;
    dateLabel: string;
    date: string;
}

interface TransformedData {
    title: string;
    tag: string; // 发放 / 回收 / 处置
    tagType: string; // success / warning / error / default
    approvers: TransformedApprover[];
}

function transformFlowData(data: FlowData): TransformedData {
    const title = data.flowName;
    const approvers: TransformedApprover[] = [];
    const nodes = data.nodes || [];
    const nodeCount = nodes.length;

    // 解析 businessId
    let businessId = '';
    try {
        const parsed = JSON.parse(data.formContent);
        businessId = parsed.businessId || parsed?.data?.businessId || '';
    } catch (e) {
        console.warn('formContent JSON 解析失败:', e);
    }

    // tag 与 tagType 映射
    const tagMap: Record<string, { tag: string; tagType: string }> = {
        FILE_GRANT: { tag: '发放', tagType: 'success' },
        FILE_RECLAIM: { tag: '回收', tagType: 'warning' },
        FILE_DISPOSAL: { tag: '处置', tagType: 'error' }
    };
    const tagInfo = tagMap[businessId] || { tag: '未知', tagType: 'default' };

    const formatDate = (ts: number) => dayjs(ts).format('YYYY-MM-DD HH:mm:ss');

    const extractForNode = (node: Node, roleLabel: string, dateLabel: string): TransformedApprover[] => {
        const list: TransformedApprover[] = [];

        if (node.signingKind === 'or') {
            const passed = node.approvers.find((a) => a.status === 'passed');
            if (passed) {
                list.push({
                    label: roleLabel,
                    name: passed.approverNickname,
                    dateLabel,
                    date: formatDate(passed.updatedAt)
                });
            }
        } else if (node.signingKind === 'and') {
            node.approvers
                .filter((a) => a.status === 'passed')
                .forEach((passed) => {
                    list.push({
                        label: roleLabel,
                        name: passed.approverNickname,
                        dateLabel,
                        date: formatDate(passed.updatedAt)
                    });
                });
        }

        return list;
    };

    nodes.forEach((node, idx) => {
        const isLast = idx === nodeCount - 1;

        if (nodeCount === 1) {
            approvers.push(...extractForNode(node, '审核人', '审核日期'));
            approvers.push(...extractForNode(node, '批准人', '批准日期'));
        } else {
            if (!isLast) {
                approvers.push(...extractForNode(node, '审核人', '审核日期'));
            } else {
                approvers.push(...extractForNode(node, '批准人', '批准日期'));
            }
        }
    });

    return {
        title,
        tag: tagInfo.tag,
        tagType: tagInfo.tagType,
        approvers
    };
}

// 获取审批流程详情
const sections = ref<any>({});
const getWorkflowDetail = async (id: string) => {
    const res = await api.sass.api.v1.workflow.workflow.detail(id);
    if (!res.data) {
        throw new Error('获取审批详情失败');
    }
    sections.value = transformFlowData(res.data);
    console.log('🚀 ~ getWorkflowDetail ~ sections.value:', sections.value);
};

// const sections = computed(() => {
//     if (type.value === 1) {
//         return [
//             {
//                 title: '内发：电子文件-查询',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询/下载',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询/下载',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '外发：电子文件-一次下载',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     } else if (type.value === 2) {
//         return [
//             {
//                 title: '内发：纸质文件-一次下载变更记录',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：纸质文件-一次下载变更记录',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     } else {
//         return [
//             {
//                 title: '借阅记录',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '借阅记录',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     }
// });

onMounted(() => {
    getWorkflowDetail(flowId.value);
});
</script>

<style scoped lang="less">
.approval-process {
    font-size: 15px;
    .approval-section {
        margin-bottom: 24px;
    }
    .approval-title {
        font-weight: bold;
        margin-bottom: 8px;
        .action {
            margin-left: 16px;
        }
    }
    .approval-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    .approval-row {
        display: flex;
        gap: 12px;
        margin-bottom: 2px;
    }
    .label {
        min-width: 56px;
        color: #333;
    }
    .value {
        min-width: 60px;
        color: #000;
    }
}
</style>
