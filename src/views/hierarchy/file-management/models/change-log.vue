<template>
    <div class="change-log">
        <n-data-table :columns="columns" :data="data" size="small" :pagination="pagination" scroll-x="1200px">
            <template #todo="{ row }">
                <n-button type="primary" size="tiny" @click="handleView(row)">详情</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
import { PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const props = withDefaults(
    defineProps<{
        id?: string;
    }>(),
    {
        id: ''
    }
);
const emit = defineEmits(['update:id']);
const { id } = useVModels(props, emit);

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'index',
        align: 'center',
        width: 60
    },
    {
        title: '文件编号',
        key: 'file_no',
        align: 'center',
        minWidth: 200,
        fixed: 'left',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '版本/版次',
        key: 'version',
        align: 'center',
        width: 80
    },
    {
        title: '文件名称',
        key: 'file_name',
        align: 'center',
        minWidth: 200,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件类别',
        key: 'file_type',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制部门',
        key: 'department',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制人',
        key: 'creator',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审核人',
        key: 'auditor',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '批准人',
        key: 'approver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发布日期',
        key: 'publish_date',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '实施日期',
        key: 'implement_date',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作类型',
        key: 'operation_type',
        align: 'center',
        width: 80,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '查看详情',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const data = ref([]);

const getChangeLog = async () => {
    const res = await $apis.test.mockData({
        params: { total: 20, noPage: true },
        data: {
            'index|+1': 1,
            'file_no|1': ['@word(10)'],
            'version|1': ['v1', 'v2', 'v3'],
            'file_name|1': ['@cword(5)'],
            'file_type|1': ['技术性文件', '管理性文件', '规章制度'],
            'department|1': ['技术部', '管理部', '行政部'],
            'creator|1': ['@cname'],
            'auditor|1': ['@cname', '张三'],
            'approver|1': ['@cname'],
            'publish_date|1': ['@date("yyyy-MM-dd")'],
            'implement_date|1': ['@date("yyyy-MM-dd")'],
            'operation_type|1': ['新增', '修订', '作废'],
            'todo|1': ['@cname']
        }
    });
    data.value = res.data.data;
};

const pagination = ref<PaginationProps>({
    pageSize: 10,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

const handleView = (row: any) => {
    window.$message.info(`变更记录：${row.time}`);
};

onMounted(() => {
    console.log(id.value);
    getChangeLog();
});
</script>

<style scoped lang="less"></style>
