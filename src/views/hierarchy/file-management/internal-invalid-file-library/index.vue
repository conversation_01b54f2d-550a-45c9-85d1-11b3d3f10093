<template>
    <div class="internal-invalid-file-library">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1200,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :params="params"
            :data-api="
                $apis.test.mockList.bind(null, {
                    'no|1': ['1234567890', '1234567891', '1234567892'],
                    'name|1': ['文件名称1', '文件名称2', '文件名称3'],
                    'docCategoryName|1': ['文件类别1', '文件类别2', '文件类别3'],
                    'publishDate|1': [1735689600000, 1735776000000, 1735862400000],
                    'effectiveDate|1': [1735689600000, 1735776000000, 1735862400000],
                    'invalidDate|1': [1735689600000, 1735776000000, 1735862400000],
                    'invalidVersion|1': [1, 2, 3]
                })
            "
            :search-props="{
                showAdd: false,
                showInput: false,
                searchInputPlaceholder: '请输入文件编号、名称 / 原文件编号',
                inputWidth: '280px'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="handleReset"
        >
            <template #search_form_middle>
                <n-input class="w-198px" v-model:value="params.no" placeholder="请输入文件编号" />
                <n-input class="w-198px" v-model:value="params.name" placeholder="请输入文件名称" />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="show = !show">{{ show ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="show" :show="show">
                    <n-space>
                        <n-input class="w-198px" v-model:value="params.originalNo" placeholder="请输入原文件编号" />
                        <select-tree-dictionary
                            class="w-198px"
                            v-model:value="params.docCategoryIds"
                            placeholder="选择文件类别"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                        <select-tree-organization
                            ref="departmentTreeRef"
                            class="w-198px"
                            v-model:value="params.departmentIds"
                            multiple
                            checkable
                            filterable
                            cascade
                            :show-path="false"
                            maxTagCount="responsive"
                            placeholder="选择编制部门"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.hasAttachment"
                            :options="$datas.fileLibrary.hasAttachmentOptions"
                            clearable
                            placeholder="是否有附件"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-permission has="internalInvalidFileExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>

            <template #table_publishDate="{ row }">
                <n-time :time="row.publishDate" format="yyyy-MM-dd" />
            </template>
            <template #table_effectiveDate="{ row }">
                <n-time :time="row.effectiveDate" format="yyyy-MM-dd" />
            </template>
            <template #table_invalidDate="{ row }">
                <n-time :time="row.invalidDate" format="yyyy-MM-dd" />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center" :wrap="false">
                    <n-permission has="internalInvalidFileBorrow">
                        <n-button size="tiny" type="primary" @click="handleBorrow(row)">借阅</n-button>
                    </n-permission>
                    <n-permission has="internalInvalidFileDetail">
                        <n-button size="tiny" @click="handleDetail(row)">作废详情</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';

const searchTablePageRef = ref();
const params = ref<any>({
    no: '', // 文件编号
    name: '', // 文件名称
    originalNo: '', // 原文件编号
    docCategoryIds: [], // 文件类别
    departmentIds: null, // 编制部门
    status: null, // 状态
    hasAttachment: null // 是否有附件
});

const show = ref(false);

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件编号', key: 'no', align: 'center', fixed: 'left', ellipsis: { tooltip: true } },
    { title: '文件名称', key: 'name', align: 'center', ellipsis: { tooltip: true } },
    { title: '文件类别', key: 'docCategoryName', align: 'center', ellipsis: { tooltip: true } },
    { title: '首次发布日期', key: 'publishDate', align: 'center', width: 120 },
    { title: '首次实施日期', key: 'effectiveDate', align: 'center', width: 120 },
    { title: '最后作废日期', key: 'invalidDate', align: 'center', width: 120 },
    { title: '作废版本数', key: 'invalidVersion', align: 'center', width: 120, ellipsis: { tooltip: true } },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 160 }
];

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const handleReset = () => {
    params.value = {
        no: '',
        name: '',
        originalNo: '',
        departmentIds: null,
        status: null,
        hasAttachment: null,
        docCategoryIds: params.value.docCategoryIds || []
    };
    init();
};

const handleExport = () => {
    window.$message.info('点击导出');
};

/**
 * 详情操作
 */
const handleDetail = (row: any) => {
    console.log('详情', row);
};

/**
 * 借阅操作
 */
const handleBorrow = (row: any) => {
    console.log('借阅', row);
};
</script>
