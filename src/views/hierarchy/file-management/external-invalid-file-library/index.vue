<template>
    <div class="external-invalid-file-library bg-#fff">
        <n-tabs v-model:value="activeTab" type="line" class="px-10px mb-5px">
            <n-tab v-if="store.permissions.indexOf('externalInvalidFileGroupTab') > -1" name="group">集团库</n-tab>
            <n-tab v-if="store.permissions.indexOf('externalInvalidFileCompanyTab') > -1" name="company">公司库</n-tab>
        </n-tabs>
        <n-search-table-page
            v-if="activeTab !== ''"
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1200,
                maxHeight: 'calc(100vh - 480px)'
            }"
            :params="params"
            :data-api="
                $apis.test.mockList.bind(null, {
                    'fileCode|1': ['1234567890', '1234567891', '1234567892'],
                    'originFileNo|1': ['1234567890', '1234567891'],
                    'fileName|1': ['文件名称1', '文件名称2', '文件名称3'],
                    'fileType|1': ['文件类别1', '文件类别2', '文件类别3'],
                    'publishDate|1': [1735689600000, 1735776000000, *********0000],
                    'implementDate|1': [1735689600000, 1735776000000, *********0000],
                    'invalidDate|1': [1735689600000, 1735776000000, *********0000],
                    'invalidVersion|1': [1, 2, 3]
                })
            "
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: activeTab === 'group' ? '请输入集团文件编号' : '请输入文件编号',
                inputWidth: '168px'
            }"
            :search-table-space="{ size: 20 }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="handleReset"
        >
            <template #search_form_middle>
                <n-input class="w-168px!" v-model:value="params.fileName" clearable placeholder="输入文件名称" />
            </template>
            <template #search_handle_after>
                <n-permission has="externalInvalidFileExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="showMore = !showMore">{{ showMore ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition :show="showMore" v-if="showMore">
                    <n-space>
                        <n-input
                            class="w-168px!"
                            v-model:value="params.originFileCode"
                            clearable
                            placeholder="输入原文件编号"
                        />

                        <n-select
                            class="w-168px"
                            v-model:value="params.domain"
                            :options="$datas.fileLibrary.domainOptions"
                            clearable
                            placeholder="选择所属领域"
                        />
                        <select-tree-dictionary
                            class="w-168px"
                            v-model:value="params.docCategoryIds"
                            placeholder="选择文件类别"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                        <n-input
                            class="w-168px!"
                            v-model:value="params.originFileNo"
                            clearable
                            placeholder="输入原文件号"
                        />
                        <n-input class="w-168px!" v-model:value="params.issueNo" clearable placeholder="输入发文号" />
                        <n-input
                            class="w-168px!"
                            v-model:value="params.issueDept"
                            clearable
                            placeholder="输入发文部门"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.certType"
                            :options="$datas.fileLibrary.certTypeOptions"
                            clearable
                            placeholder="选择认证方式"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.hasAttachment"
                            :options="$datas.fileLibrary.hasAttachmentOptions"
                            clearable
                            placeholder="是否有附件"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #table_publishDate="{ row }">
                <n-time :time="row.publishDate" format="yyyy-MM-dd" />
            </template>
            <template #table_implementDate="{ row }">
                <n-time :time="row.implementDate" format="yyyy-MM-dd" />
            </template>
            <template #table_invalidDate="{ row }">
                <n-time :time="row.invalidDate" format="yyyy-MM-dd" />
            </template>

            <template #table_todo="{ row }">
                <n-space justify="center" :wrap="false">
                    <n-permission has="externalInvalidFileBorrow">
                        <n-button size="tiny" type="primary" @click="handleBorrow(row)">借阅</n-button>
                    </n-permission>
                    <n-permission has="externalInvalidFileDetail">
                        <n-button size="tiny" @click="handleDetail(row)">作废详情</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
        <n-empty v-else description="暂无权限  无法访问" class="min-h-300px justify-center" />
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const activeTab = ref('');

const searchTablePageRef = ref();
const showMore = ref(false);
const params = ref<any>({
    fileCode: '',
    fileName: '',
    originFileNo: '',
    docCategoryIds: null,
    domain: null,
    issueNo: '',
    issueDept: '',
    certType: null,
    hasAttachment: null,
    invalidDate: '',
    invalidVersion: ''
});

const columns = computed<DataTableColumns>(() => [
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    {
        title: activeTab.value === 'group' ? '集团文件编号' : '文件编号',
        key: 'fileCode',
        align: 'center',
        fixed: 'left',
        ellipsis: { tooltip: true }
    },
    { title: '文件名称', key: 'fileName', align: 'center', ellipsis: { tooltip: true } },
    { title: '原文件号', key: 'originFileNo', align: 'center', ellipsis: { tooltip: true } },
    { title: '文件类别', key: 'fileType', align: 'center', ellipsis: { tooltip: true } },
    { title: '首次发布日期', key: 'publishDate', align: 'center', width: 120 },
    { title: '首次实施日期', key: 'implementDate', align: 'center', width: 120 },
    { title: '最后作废日期', key: 'invalidDate', align: 'center', width: 120 },
    { title: '作废版本数', key: 'invalidVersion', align: 'center', width: 120, ellipsis: { tooltip: true } },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 160 }
]);

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};
const handleReset = () => {
    params.value = {
        fileCode: '',
        fileName: '',
        originFileCode: '',
        docCategoryIds: null,
        domain: null,
        originFileNo: '',
        issueNo: '',
        issueDept: '',
        certType: null,
        hasAttachment: null,
        invalidDate: '',
        invalidVersion: ''
    };
    init();
};

const handleExport = () => {
    window.$message.info('点击导出');
};

const handleDetail = (row: any) => {
    console.log('作废详情', row);
};

const handleBorrow = (row: any) => {
    console.log('借阅', row);
};

onMounted(async () => {
    // 根据权限设置默认tab
    if (store.permissions.indexOf('externalInvalidFileGroupTab') > -1) activeTab.value = 'group';
    else if (store.permissions.indexOf('externalInvalidFileCompanyTab') > -1) activeTab.value = 'company';
});
</script>

<style scoped lang="less"></style>
