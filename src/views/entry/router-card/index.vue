<template>
    <div class="router-card-wrapper">
        <div
            class="router-card"
            v-for="item in routerCards"
            :key="item.key"
            @click="$router.replace({ name: item.key })"
            @mouseenter="hoverKey = item.key"
            @mouseleave="hoverKey = null"
        >
            <div class="card-content">
                <h2 class="card-title">{{ item.title }}</h2>
                <p class="card-subtitle">{{ item.key }}</p>
            </div>
            <div class="card-pointer">
                <n-image width="24" :src="hoverKey === item.key ? arrowRightBlue : arrowRightWhite" preview-disabled />
            </div>
            <div class="card-leaf abs left--50px" v-if="hoverKey === item.key">
                <n-image width="100%" height="100%" fit="contain" :src="cardLeaf" preview-disabled />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import configHooks from '@/config/config-hooks';
import arrowRightWhite from '@/assets/images/entry/arrow-right-white.webp';
import arrowRightBlue from '@/assets/images/entry/arrow-right-blue.webp';
import cardLeaf from '@/assets/images/entry/hover-card-leaf.webp';
import type { RouteRecordName } from 'vue-router';

interface FunctionCard {
    title: string;
    subtitle: string;
    key: RouteRecordName;
}

const props = defineProps({
    route: {
        type: String,
        default: 'Entry'
    }
});

const store = useStore();
const routerCards = ref<FunctionCard[]>([]);
const hoverKey = ref<RouteRecordName | null>(null);

const menuOptions = store.routes
    .filter((v) => configHooks.layout.filterNav(v) || !v.meta?.hidden || !v.meta?.isActive)
    .map((route) => ({
        key: route.name,
        label: route.meta?.title || route.name,
        info: route
    }));

watchEffect(() => {
    const menuItem = menuOptions.find((item) => item.key === props.route);
    routerCards.value =
        menuItem?.info?.children
            ?.filter((child) => {
                return child.meta?.isActive !== false;
            })
            .map((child) => ({
                title: child.meta?.title || (child.name as string),
                subtitle: String(child.name),
                key: child.name
            })) || [];
});
</script>

<style lang="less" scoped>
.router-card-wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    padding: 0 50px;
    width: 100%;
    justify-items: center;

    .router-card {
        width: 100%;
        max-width: 356px;
        height: auto;
        min-height: 386px;
        display: flex;
        cursor: pointer;
        align-items: center;
        flex-direction: column;
        border: 1px solid #fff;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        @transition: 0.3s ease-in-out;
        transition: @transition;
        background: linear-gradient(333deg, #ffffff 48%, rgba(255, 255, 255, 0) 111%);

        .card-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 24px;
            width: 100%;
            height: calc(100% - 46px - 48px);
            z-index: 1;
            position: absolute;
            top: 0;

            .card-title {
                font-size: 36px;
                font-weight: 500;
                color: #333;
                text-align: center;
            }

            .card-subtitle {
                margin-top: 30px;
                font-size: 18px;
                color: #666;
                text-align: center;
            }
        }

        .card-pointer {
            margin-bottom: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 86px;
            height: 46px;
            border-radius: 118px;
            color: #005eff;
            z-index: 1;
            position: absolute;
            bottom: 0;
            transition: @transition;
            background: linear-gradient(271deg, #4a9bff 4%, #005eff 98%);
        }

        * {
            transition: @transition;
        }

        &::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(333deg, #fff 48%, rgba(255, 255, 255, 0) 111%);
            backdrop-filter: blur(10px);
            transition: @transition;
            opacity: 1;
            transition: @transition;
        }

        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(332deg, rgba(74, 155, 255, 0) 42%, #005eff 87%);
            opacity: 0;
            transition: @transition;
        }

        &:hover {
            transform: scale(1.05);
            background-image: url('@/assets/images/entry/hover-card-leaf.webp');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background: linear-gradient(321deg, #4a9bff 4%, #005eff 90%),
                linear-gradient(332deg, #ffffff 57%, rgba(255, 255, 255, 0) 110%);

            &::before {
                opacity: 0;
            }

            &::after {
                opacity: 1;
            }

            .card-title,
            .card-subtitle {
                color: #fff;
            }
            .card-pointer {
                background: #fff;
            }
        }
    }
}

@media screen and (max-width: 1540px) {
    .router-card-wrapper {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 1240px) {
    .router-card-wrapper {
        grid-template-columns: 1fr;
    }
}
</style>
