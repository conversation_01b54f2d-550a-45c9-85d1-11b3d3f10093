<template>
    <div class="flex flex-col gap-12px min-h-300px">
        <bs-todo-notice-card-item
            v-for="(item, index) in todos"
            :key="index"
            :img-src="type === 'todo' ? todo : notice"
            :title="item.title"
            :date="item.date"
        />
        <n-image v-if="todos.length === 0" :src="emptyData" preview-disabled />
    </div>
</template>

<script setup lang="ts">
import emptyData from '@/assets/images/entry/todo-no-data.webp';
import todo from '@/assets/images/entry/todo.webp';
import notice from '@/assets/images/entry/notice.webp';

defineProps({
    type: {
        type: String,
        default: 'todo'
    }
});

const todos = ref<{ title: string; date: string }[]>([
    { title: '这是一条关于人力资源培训待办的提醒', date: '2025.01.02' },
    { title: '这是一条关于人力资源培训待办的提醒', date: '2025.01.02' }
]);
</script>
