<template>
    <div
        class="w-420px h-868px flex-v mr-24px rounded-8px border-[1px,solid,#fff] bg-[linear-gradient(347deg,_#EAF3FF_44%,_rgba(255,255,255,0.3)_76%)]"
    >
        <bs-todo-notice-list
            title="待办"
            :list="todos"
            :img-src="todo"
            :empty-img="emptyData"
            @more="openDialog('todo')"
        />
        <bs-todo-notice-list
            title="新消息通知"
            :list="notices"
            :img-src="notice"
            :empty-img="emptyData"
            :max="3"
            @more="openDialog('notice')"
        />
    </div>
</template>

<script setup lang="ts">
import todo from '@/assets/images/entry/todo.webp';
import notice from '@/assets/images/entry/notice.webp';
import emptyData from '@/assets/images/entry/todo-no-data.webp';

const todos = ref<{ title: string; date: string }[]>([]);

const notices = ref<{ title: string; date: string }[]>([
    {
        title: '这是一条人力资源培训会议的提醒',
        date: '2025.01.02'
    },
    {
        title: '这是一条人力资源培训会议的提醒',
        date: '2025.01.02'
    },
    {
        title: '这是一条人力资源培训会议的提醒',
        date: '2025.01.02'
    },
    {
        title: '这是一条人力资源培训会议的提醒',
        date: '2025.01.02'
    },
    {
        title: '这是一条人力资源培训会议的提醒',
        date: '2025.01.02'
    }
]);

const openDialog = (k: string) => {
    $alert.dialog({
        title: k === 'todo' ? '待办提醒' : '消息通知',
        content: import('../models/todo-dialog.vue'),
        width: '600px',
        props: {
            type: k
        }
    });
};
</script>

<style scoped></style>
