<template>
    <div class="entry flex-v h-100vh min-w-1000px">
        <!-- 背景视频 -->
        <div
            class="bg-video absolute top-0 left-1/2 -translate-x-1/2 w-full min-w-1000px max-w-2000px h-100vh overflow-hidden z--10"
        >
            <video
                ref="startVideo"
                src="/file/entry_bg_start.mp4"
                alt="背景视频"
                autoplay
                muted
                class="w-full min-w-1000px h-100% object-cover filter-brightness-110"
                @ended="onStartVideoEnded"
                v-show="showStartVideo"
            ></video>
            <video
                ref="loopVideo"
                src="/file/entry_bg_loop.mp4"
                alt="背景视频"
                autoplay
                loop
                muted
                class="w-full min-w-1000px h-100% object-cover filter-brightness-110"
                v-show="!showStartVideo"
            ></video>
        </div>
        <!-- 背景蒙版 -->
        <div class="mask abs top-0 w-full h-100vh z--9 overflow-hidden">
            <div class="mask-left abs top-0 h-100vh"></div>
            <div class="mask-right abs top-0 h-100vh"></div>
        </div>
        <!-- 顶部导航区域 -->
        <home-scroller-menu class="bg-transparent!" v-model:activeMenu="activeMenu" />
        <!-- 中间内容区域 -->
        <div class="content-area flex-1">
            <div class="content-wrapper h-full py-62px px-60px overflow-y-auto">
                <div class="area max-w-1920px mx-auto w-full flex min-h-full">
                    <to-do-list v-if="activeComponent !== Home" class="h-full"></to-do-list>
                    <div class="flex-1 min-w-0 h-full flex flex-wrap gap-4 content-start">
                        <component :is="activeComponent" :route="activeMenu" class="h-full" />
                    </div>
                </div>
            </div>
        </div>
        <!-- 公共底部区域 -->
        <footer class="footer-area py-12px text-14px c-#888">
            <div class="footer-content flex flex-wrap justify-around gap-y-8px px-100px">
                <div v-for="(item, idx) in footerItems" :key="idx" class="flex items-center">
                    <img :src="item.img" alt="" width="12px" height="12px" />
                    <span class="ml-5px">{{ item.label }}</span>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import Home from '@/views/entry/home/<USER>';
import RouterCard from '@/views/entry/router-card/index.vue';
import copyright from '@/assets/images/login/copyright.webp';
import wx from '@/assets/images/login/wx.webp';
import email from '@/assets/images/login/email.webp';
import record from '@/assets/images/login/record.webp';
import user from '@/assets/images/login/user.webp';
import badge from '@/assets/images/login/badge.webp';
import ToDoList from '@/views/entry/todo-list/index.vue';
import { shallowRef, type Component, watch, onMounted, onUnmounted } from 'vue';

const router = useRouter();
const route = useRoute();

const activeMenu = ref((route.query?.activeMenu as string) || 'Entry');
const activeComponent = shallowRef<Component>(Home);

// 视频控制相关
const showStartVideo = ref(true);
const startVideo = ref<HTMLVideoElement>();
const loopVideo = ref<HTMLVideoElement>();

const onStartVideoEnded = () => {
    showStartVideo.value = false;
    // 确保循环视频开始播放
    nextTick(() => {
        if (loopVideo.value) {
            loopVideo.value.play();
        }
    });
};

// 监听 activeMenu 变化
watch(activeMenu, (key) => {
    if (key === 'Entry') {
        activeComponent.value = Home;
    } else {
        activeComponent.value = RouterCard;
    }
    // 更新 URL
    window.history?.pushState?.(
        '',
        '',
        '#' + router.currentRoute.value.path + (key === 'Entry' ? '' : '?activeMenu=' + key)
    );
});

// 处理浏览器前进后退
const handlePopState = () => {
    const searchParams = new URLSearchParams(window.location.search);
    const newActiveMenu = searchParams.get('activeMenu');
    if (newActiveMenu !== activeMenu.value) {
        activeMenu.value = newActiveMenu || 'Entry';
    }
};

onMounted(() => {
    // 初始化时设置正确的菜单
    const initialActiveMenu = route.query.activeMenu as string;
    if (initialActiveMenu) {
        activeMenu.value = initialActiveMenu;
        // 这里同步设置 activeComponent
        if (initialActiveMenu === 'Entry') {
            activeComponent.value = Home;
        } else {
            activeComponent.value = RouterCard;
        }
    }
    // 添加浏览器返回事件监听
    window.addEventListener('popstate', handlePopState);
});

onUnmounted(() => {
    // 移除 popstate 事件监听
    window.removeEventListener('popstate', handlePopState);
});

const onlineCount = ref(400);
const footerItems = computed(() => [
    {
        label: '版权所有©浙江中一检测研究院股份有限公司',
        img: copyright,
        remark: ''
    },
    {
        label: '邮箱登录',
        img: email,
        remark: ''
    },
    {
        label: '小程序',
        img: wx,
        remark: ''
    },
    {
        label: '浙ICP备17009639号-1',
        img: record,
        remark: ''
    },
    {
        label: '浙公网安备 33020002011220号',
        img: badge,
        remark: ''
    },
    {
        label: `当前在线人数：${onlineCount.value}人`,
        img: user,
        remark: ''
    }
]);
</script>

<style lang="less" scoped>
@media screen and (min-width: 2001px) {
    .mask {
        @w: calc((100% - 2000px) / 2);

        .mask-left {
            background-image: linear-gradient(90deg, #fff 0%, rgba(241, 239, 247, 0) 100%);
            width: 300px;
            left: @w;
        }

        .mask-right {
            background-image: linear-gradient(-90deg, #fff 0%, rgba(241, 239, 247, 0) 100%);
            width: 300px;
            right: @w;
        }
    }
}

@media screen and (max-width: 1001px) {
    .entry {
        .bg-video {
            left: 0 !important;
            transform: translateX(0);
        }
    }
}

.content-area {
    height: calc(100vh - 120px); // 减去头部和底部的高度
    position: relative;

    .content-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow-y: overlay;
        scrollbar-gutter: stable;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 94, 255, 0.01);
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(74, 155, 255, 0) 0%, rgba(0, 94, 255, 0) 100%);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        &:hover::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(74, 155, 255, 0.3) 0%, rgba(0, 94, 255, 0.3) 100%);

            &:hover {
                background: linear-gradient(180deg, rgba(0, 94, 255, 0.4) 0%, rgba(74, 155, 255, 0.4) 100%);
            }
        }
    }
}
</style>

<style lang="less">
.loading {
    height: 100vh;
    min-height: 100vh;
    overflow-y: hidden !important;
}

.vaw-layout-container {
    min-width: 1000px !important;
    overflow-y: hidden !important;
}
</style>
