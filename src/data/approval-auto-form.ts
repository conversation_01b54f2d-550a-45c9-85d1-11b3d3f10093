import { markRaw } from 'vue';
import { FormProps, GridProps } from 'naive-ui';

// 表单配置类型定义
interface FormConfig {
    title: string;
    formConfig?: FormProps;
    gridProps?: GridProps;
    fields?: FormValidateField;
    components?: any;
}

// 版本配置类型定义
type VersionConfig = {
    [version: string]: FormConfig;
};

// 审批表单类型定义
type ApprovalForms = {
    [key: string]: VersionConfig;
};

// 审批表单配置
export const APPROVAL_FORMS: ApprovalForms = {
    SIGNATURE_APPROVAL: {
        '1.0.0': {
            title: '签名审批单',
            formConfig: {
                labelWidth: '100px'
            },
            fields: [
                {
                    label: '申请人',
                    field: 'nickname',
                    component: 'input',
                    props: { readonly: true },
                    gridItemProps: { span: 24 }
                },
                {
                    label: '签名图片',
                    field: 'signatureBase64',
                    component: markRaw(signatureImage),
                    props: {
                        width: 300,
                        height: 140,
                        noPreview: true
                    },
                    gridItemProps: { span: 24 }
                },
                {
                    label: '申请原因',
                    field: 'reason',
                    component: 'input',
                    props: { readonly: true },
                    gridItemProps: { span: 24 }
                }
            ]
        }
    },
    TEST_APPROVAL: {
        '1.0.0': {
            title: '测试审批单',
            fields: [
                {
                    label: '申请人',
                    field: 'nickname',
                    component: markRaw(BsFormText),
                    props: { ellipsis: true },
                    gridItemProps: { span: 24 }
                },
                {
                    label: '申请原因',
                    field: 'reason',
                    component: markRaw(BsFormText),
                    props: { ellipsis: true },
                    gridItemProps: { span: 24 }
                }
            ]
        }
    },
    FILE_INCORPORATE: {
        '1.0.0': {
            title: '纳入集团外部文件到子公司审批单',
            fields: [
                {
                    label: '申请人',
                    field: 'nickname',
                    component: markRaw(BsFormText),
                    props: { ellipsis: true },
                    gridItemProps: { span: 24 }
                },
                {
                    field: 'data',
                    component: markRaw(BsInclusionProcess),
                    gridItemProps: { span: 24 }
                }
            ]
        }
    },
    FILE_GRANT: {
        '1.0.0': {
            title: '文件管理-发放申请',
            components: markRaw(BsCustomApprovalForm)
        }
    },
    FILE_RECLAIM: {
        '1.0.0': {
            title: '文件管理-回收申请',
            components: markRaw(BsRecycleForm)
        }
    },
    FILE_DISPOSAL: {
        '1.0.0': {
            title: '文件管理-纸质文件处置',
            components: markRaw(BsPaperDisposalForm)
        }
    }
};

// 获取指定业务类型和版本的表单配置
const getFormConfig = async (businessId: string, version = '1.0.0'): Promise<FormConfig> => {
    try {
        // 检查类型是否存在
        if (!APPROVAL_FORMS[businessId]) {
            console.warn(`未找到类型 ${businessId} 的表单配置`);
            return Promise.reject(`未找到类型 ${businessId} 的表单配置`);
        }

        // 检查版本是否存在
        const typeConfig = APPROVAL_FORMS[businessId];
        if (!typeConfig[version]) {
            window.$message.error(`未找到类型 ${businessId} 版本 ${version} 的表单配置，将使用最新版本`);
            // 获取最新版本
            const versions = Object.keys(typeConfig);
            // 按版本号排序，获取最新版本
            const latestVersion = versions.sort().reverse()[0];
            return typeConfig[latestVersion];
        }

        return typeConfig[version];
    } catch (error) {
        console.error('获取表单配置失败:', error);
        return Promise.reject('获取表单配置失败');
    }
};
export default {
    getFormConfig
};
