interface StatusOption {
    label: string;
    value: number;
    type: 'default' | 'primary' | 'success' | 'error';
}
/**
 * 借阅与交还 状态
 */
const status: StatusOption[] = [
    {
        label: '待提交',
        value: 1,
        type: 'default'
    },
    {
        label: '待审批',
        value: 2,
        type: 'primary'
    },
    {
        label: '已驳回',
        value: 3,
        type: 'error'
    },
    {
        label: '已审批',
        value: 4,
        type: 'success'
    }
];

export default {
    status
};
