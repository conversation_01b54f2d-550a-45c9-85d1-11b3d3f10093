import { showToast } from 'vant';
import 'vant/es/toast/style';
import { showDialog } from 'vant';
import 'vant/es/dialog/style';
import { DialogOptions } from 'vant/lib/dialog/types';

export function commonToast(msg: string, duration?: number) {
    showToast({
        message: msg,
        duration: duration ?? 3000
    });
}

export function commonDialog(options: DialogOptions) {
    return showDialog(options);
}

// 获取所有节点key
export const getAllKeys = (tree: any[]): string[] => {
    let keys: string[] = [];
    for (const node of tree) {
        keys.push(node.key);
        if (node.children) {
            keys = keys.concat(getAllKeys(node.children));
        }
    }
    return keys;
};

export const iconMap: { [key: string]: string } = {
    folder: 'svgs-com-file-open',
    jpg: 'svgs-file-image',
    png: 'svgs-file-image',
    svg: 'svgs-file-image',
    webp: 'svgs-file-image',
    mpeg: 'svgs-file-image',
    mp4: 'svgs-file-video',
    flv: 'svgs-file-video',
    mov: 'svgs-file-video',
    mkv: 'svgs-file-video',
    mvb: 'svgs-file-video',
    avi: 'svgs-file-video',
    mp3: 'svgs-file-music',
    pdf: 'svgs-file-pdf-1',
    doc: 'svgs-file-wps',
    docx: 'svgs-file-wps',
    pptx: 'svgs-file-ppt-1',
    ppt: 'svgs-file-ppt-1',
    xls: 'svgs-file-excel-1',
    xlsx: 'svgs-file-excel-1',
    zip: 'svgs-file-zip',
    txt: 'svgs-file-txt',
    '': 'svgs-file-other'
};

export const fileTypes = {
    office: ['docx', 'doc', 'pdf', 'xlsx', 'pptx', 'xls', 'ppt'],
    image: ['jpg', 'png', 'jpeg', 'webp'],
    video: ['mp4', 'mov', 'mkv']
};

// 数组比较函数 - 用于检测数组内容是否发生变化
export const isArrayChanged = (oldArray: any[] | null | undefined, newArray: any[] | null | undefined): boolean => {
    // 如果任一数组为空或未定义
    if (!oldArray || !newArray) {
        return oldArray !== newArray;
    }
    // 如果长度不同
    if (oldArray.length !== newArray.length) {
        return true;
    }
    // 逐个比较元素
    return oldArray.some((item, index) => item !== newArray[index]);
};
