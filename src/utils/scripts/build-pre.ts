import { camelCase, upperCase, upperFirst, lowerFirst, toLower } from 'lodash';
import { sync } from 'glob';
import { readJSONSync, writeJSONSync } from 'fs-extra';
import { resolve } from 'path';
const oupoutFile = resolve(process.cwd(), 'auto-import-business-preset.json');
type PresetArs = Array<{
    cwd: string;
    prefix?: string;
    suffix?: string;
    import?: string;
    preset?: any[];
}>;
export const AutoImportBusinessPreset = () => readJSONSync(oupoutFile);
const presetArsConfig = [
    {
        cwd: 'src/components/business',
        prefix: 'bs'
    },
    {
        cwd: 'src/hooks',
        suffix: 'hooks'
    }
] as PresetArs;
export const run = (presetArs: PresetArs = []) => {
    const defaultPresetArs: PresetArs = presetArsConfig.concat(presetArs);
    const syncCwd: PresetArs = [];
    const syncCwdPreset: PresetArs = [];
    defaultPresetArs.forEach((e) => {
        if (Array.isArray(e.preset)) {
            syncCwdPreset.push(e);
        } else {
            syncCwd.push(e);
        }
    });
    const presets = syncCwd.reduce((pre: any, { cwd, prefix, suffix }) => {
        const presetAlias = sync('**/*.{vue,ts,jsx,tsx}', {
            cwd: cwd,
            absolute: true
        }).reduce<string[]>((pre, cur: string) => {
            const filePath = cur;
            const relativePath = filePath
                .replace(resolve(process.cwd(), cwd), '')
                .replace(/^[/\\]/, '')
                .replace(/\..*$/, '');
            // 只使用文件名，不包含文件夹路径
            const fileName = relativePath.split(/[/\\]/).pop() || relativePath;
            const name = upperFirst(camelCase(fileName));
            let arr: any = [];
            arr.push(name);
            arr.push(lowerFirst(name));
            if (typeof prefix === 'string') {
                new Array(3).fill(toLower(prefix)).forEach((p, k) => {
                    p =
                        {
                            0: upperCase(p),
                            1: upperFirst(p)
                        }[k] || p;
                    arr.push(`${p}${name}`);
                });
            }
            if (typeof suffix === 'string') {
                arr = arr.map((e: any) => `${e}${upperFirst(camelCase(suffix))}`);
            }
            return pre.concat(
                arr.map((e: string) => ({
                    filePath,
                    import: filePath.replace(resolve(process.cwd(), 'src'), '@').replace(/\\/g, '/'),
                    as: e,
                    default: 'default'
                })) as any
            );
        }, []);
        presetAlias.forEach((e: any) => {
            pre[e.import] = [...(pre[e.import] || []), [e.default, e.as]];
        });
        return pre;
    }, {});
    syncCwdPreset.forEach(({ cwd, preset }) => {
        const _import = cwd.replace(process.cwd() + '/src', '@').replace(/.*\/*src/, '@');
        presets[_import] = preset || [];
    });
    writeJSONSync(oupoutFile, presets, { spaces: 2 });
    return presets;
};
if (process.argv.includes('--run-preset')) {
    run();
}
