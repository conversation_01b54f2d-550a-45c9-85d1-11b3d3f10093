/**
 * @description 上传文件到minio
 * <AUTHOR>
 * @date 2025-07-14
 * @param {File} file - 文件
 * @returns {Promise<{ id: string }>} 文件ID
 */
export default async function uploadToMinio(file: File): Promise<{ id: string }> {
    // 1. 获取预签名URL
    const res = await window.api.file.api.v1.file.upload({
        fileName: file.name,
        fileSize: file.size
    });
    if (!res.data) throw new Error('获取预签名URL失败');
    const { fileUploadPreSignedUrl, id } = res.data;

    // 2. 上传文件
    await new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', fileUploadPreSignedUrl, true);
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) resolve();
            else reject(new Error('上传失败'));
        };
        xhr.onerror = () => reject(new Error('网络错误'));
        xhr.send(file);
    });

    return { id };
}
