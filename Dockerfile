FROM registry.zhijiasoft.com/base/node:18-alpine AS build

ARG ACCESS_TOKEN

WORKDIR /app

ADD . .

ENV NODE_OPTIONS="--max_old_space_size=4096"

RUN sed -i "s/192.168.110.132/${ACCESS_TOKEN}@192.168.110.132/g" package.json

# RUN cat package.json

RUN sed -i 's#https\?://dl-cdn.alpinelinux.org/alpine#https://mirrors.tuna.tsinghua.edu.cn/alpine#g' /etc/apk/repositories

RUN apk add --no-cache git tar

RUN npm config set registry https://registry.npmmirror.com

RUN npm install -g pnpm

RUN pnpm store path

RUN --mount=type=cache,id=pnpm_cache,target=/root/.local/share/pnpm/store/v10 pnpm i 

RUN pnpm build

RUN tar -czf dist_management.tar.gz dist_management

FROM registry.zhijiasoft.com/base/nginx:1.27-alpine AS production

WORKDIR /app

COPY --from=build /app/dist_management.tar.gz /app/

COPY --from=build /app/dist_management /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]


